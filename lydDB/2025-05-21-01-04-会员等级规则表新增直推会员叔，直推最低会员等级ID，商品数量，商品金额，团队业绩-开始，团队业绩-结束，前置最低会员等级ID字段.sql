alter table t_member_level_rule add direct_member_qty int COMMENT '直推会员数';
alter table t_member_level_rule add direct_low_member_type_id VARCHAR(50) COMMENT '直推最低会员等级ID';
alter table t_member_level_rule add product_qty int COMMENT '商品数量';
alter table t_member_level_rule add product_amount DECIMAL(12,2) COMMENT '商品金额';
alter table t_member_level_rule add team_amount_start DECIMAL(12,2) COMMENT '团队业绩-开始';
alter table t_member_level_rule add team_amount_end DECIMAL(12,2) COMMENT '团队业绩-结束';
alter table t_member_level_rule add pre_low_member_type_id VARCHAR(50) COMMENT '前置最低会员等级ID';




