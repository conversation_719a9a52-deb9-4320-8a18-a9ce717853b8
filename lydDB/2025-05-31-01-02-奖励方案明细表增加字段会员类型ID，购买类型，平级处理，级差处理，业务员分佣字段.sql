alter table t_reward_scheme_det add member_type_id BIGINT COMMENT '会员类型ID';
alter table t_reward_scheme_det add buy_type tinyint COMMENT '购买类型:1-首单，2-复购';
alter table t_reward_scheme_det add same_level_flag tinyint COMMENT '平级处理:0-否，1-是，1表示相同等级才能获得分佣';
alter table t_reward_scheme_det add differ_flag tinyint COMMENT '级差处理:0-否，1-是，1表示分佣的比例金额按照等级差额进行计算';
alter table t_reward_scheme_det add salesman_flag tinyint COMMENT '业务员分佣:1-业务员，2-业务员+会员，1表示是业务员的分佣规则，2表示是业务员和会员共用的分佣规则，默认空值表示会员的分佣规则';