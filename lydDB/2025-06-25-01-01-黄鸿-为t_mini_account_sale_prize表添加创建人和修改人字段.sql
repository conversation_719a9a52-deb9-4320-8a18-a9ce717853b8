-- 为 t_mini_account_sale_prize 表添加创建人和修改人字段 4
ALTER TABLE `t_mini_account_sale_prize` 
ADD COLUMN `create_user_id` bigint DEFAULT NULL  COMMENT '创建人ID',
ADD COLUMN `create_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL  COMMENT '创建人姓名',
ADD COLUMN `last_modify_user_id` bigint DEFAULT NULL COMMENT '修改人ID',
ADD COLUMN `last_modify_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人姓名';