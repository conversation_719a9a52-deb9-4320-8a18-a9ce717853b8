#### application-open

##### nacos


spring:
  redis:
    password: '123456'
    host: 127.0.0.1
    port: 6379
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 100MB


management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS

feign:
  hystrix:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 200000
        readTimeout: 200000
  compression:
    request:
      enabled: true
    response:
      enabled: true
# hystrix If you need to use ThreadLocal bound variables in your RequestInterceptor`s
# you will need to either set the thread isolation strategy for Hystrix to `SEMAPHORE or disable Hystrix in Feign.
hystrix:
  command:
    default:
      execution:
        isolation:
          strategy: SEMAPHORE
          thread:
            timeoutInMilliseconds: 60000
  shareSecurityContext: true

gray:
  rule:
    enabled: true
    
ribbon:
  ReadTimeout: 10000
  ConnectTimeout: 10000

mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #
  typeAliasesPackage:  com.medusa.gruul.*.api.entity
  typeEnumsPackage: com.medusa.gruul.*.api.enums
  global-config:
    banner: false
    db-config:
      id-type: auto


swagger:
  enable: true
  host: 
  #host: dev.superprism.cn/api
  title:  Swagger API
  version: v2.0
  license: Powered By Medusa

# Logger Config
logging:
  file: /log/mall/application.log
  pattern:
    file: '%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID}){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx'
  level:
    root: error
    com.alibaba.nacos: error
    com.medusa: debug
    com.baomidou: debug
    com.medusa.gruul.common.data.tenant.TenantContextHolderFilter: error
#redis
redis:
  network:
    isintra: false
  intranet:
    host: 127.0.0.1
  outernet:
    host: 127.0.0.1
  port: 6379
  timeout: 3000
  password: '123456'
  database: 0
info:
  appId: wxd1f54ec4fdb933eb
  secret: f76dcbdcaa4fe0d3f2afe2ba101c849b
gruul:
  tenant:
    #使用多店铺
    use_shop: true  
    ##不使用shopId的表
    shopTables: 
      - t_a_log
      - t_account_center
      - t_account_center_menu
      - t_auth_menu_info
      - t_auth_model_info
      - t_auth_operation_log
      - t_auth_role_info
      - t_auth_role_menu
      - t_auth_menu_user
      - t_auth_user_info
      - t_auth_user_model
      - t_auth_user_req
      - t_auth_user_role
      - t_base_menu
      - t_dict
      - t_dict_item
      - t_ent_pay
      - t_ent_pay_back_log
      - t_external_system
      - t_external_system_item
      - t_external_system_temp
      - t_field_relationship
      - t_freight_template
      - t_log
      - t_logger
      - t_member_action_template
      - t_member_information
      - t_member_level
      - t_member_level_goods_price
      - t_member_level_rights
      - t_member_level_rights_relation
      - t_mini_account
      - t_mini_account_address
      - t_mini_account_commission
      - t_mini_account_commission_cash
      - t_mini_account_extends
      - t_mini_account_foot_mark
      - t_mini_account_integral
      - t_mini_account_oauths
      - t_mini_account_pass_ticket
      - t_mini_account_pass_ticket_code
      - t_mini_account_restrict
      - t_mini_account_tag
      - t_mini_account_tag_group
      - t_mini_auth_token
      - t_mini_info
      - t_mini_mp_conf
      - t_mini_subscriberi_base
      - t_mini_subscriberi_message
      - t_partner_info
      - t_payment
      - t_payment_record
      - t_payment_refund
      - t_payment_wechat
      - t_platform_account_balance_record
      - t_platform_code_version
      - t_platform_default_value
      - t_platform_libraries_info
      - t_platform_log
      - t_platform_pay_config
      - t_platform_server_cfg
      - t_platform_service_info
      - t_platform_shop_balance
      - t_platform_shop_info
      - t_platform_shop_template_detail
      - t_platform_shop_template_detail_minis
      - t_platform_shop_template_info
      - t_product_buy_in_item
      - t_product_sec_unit
      - t_product_stock
      - t_product_stock_item
      - t_product_unit
      - t_send_code
      - t_shop_base_stting
      - t_shop_commission_rule
      - t_shop_msg_open
      - t_shop_pass_ticket
      - t_shop_pass_ticket_partner
      - t_shop_pass_ticket_product
      - t_shop_support_pay
      - t_shops_settled
      - t_sms_order
      - t_sms_order_send_detail
      - t_sms_provider
      - t_sms_sign
      - t_sms_template
      - t_sys_shop_invoice_order
      - t_sys_shop_invoice_rise
      - t_sys_shop_package
      - t_sys_shop_package_order
      - t_system_conf
      - t_template_msg_send_record
      - t_text_data
      - t_order_setting
      - t_order_share_setting
      - t_shops_category
      - t_shops_show_category
      - t_auth_menu_button
      - t_shop_pass_ticket_rule 
      - t_platform_renovation_template
      - t_platform_mini_config
      - t_platform_renovation_template_plugin
      - t_platform_renovation_template_page
      - t_integral_rule
      - t_shop_coupon
      - t_shop_coupon_partner
      - t_shop_coupon_rule
      - t_mini_account_coupon
      - t_member_level_rule
      - t_wx_message_template
      - t_wx_message_template_detail
      - t_platform_data_permissions
      - t_member_level_rule_message
      - t_member_level_rule_product
      - t_member_level_goods_again_price
      - t_shop_coupon_product
      - t_shop_coupon_category
      - t_shop_coupon_account
      - t_wx_transfer_v3
      - t_wx_transfer_scene_v3
#配置文件属性加密
jasypt:
  encryptor:
    # 加密英子 自定义随机字符串
    password: 3b44347544385279a53a3abb1f29f05b
    # 加密算法 
    algorithm: PBEWithHmacSHA512AndAES_128
#图片、附件访问路径
file:
  path: http://127.0.0.1:10999/oss-open/
  localDir: E:/ossupload/mall/
# AI接口地址
LLM-AI:
  image-url: http://localhost:8022/ai/getBarCodeByImage
  id-image-api: http://localhost:8022/ai/getBarCodeByImage
  image-api: http://localhost:8022/ai/getBarCodeByImage