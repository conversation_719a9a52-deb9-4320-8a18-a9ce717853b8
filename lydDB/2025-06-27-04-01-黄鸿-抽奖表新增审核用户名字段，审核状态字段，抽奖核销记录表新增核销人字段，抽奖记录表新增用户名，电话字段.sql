
-- 添加审核用户名字段
ALTER TABLE t_shop_sale_prize
    ADD COLUMN audit_platform_user_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审核人姓名';

-- 添加审核状态字段
ALTER TABLE t_shop_sale_prize
    ADD COLUMN audit_status INT NULL DEFAULT NULL COMMENT '审核状态：100->待审核；101->审核通过；200->审核不通过';

-- 核销记录 增加核销人字段
ALTER TABLE t_mini_account_sale_prize_code
    ADD COLUMN verify_nick_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '核销人姓名';

-- 抽奖记录 增加用户名、电话字段
ALTER TABLE t_mini_account_sale_prize
    ADD COLUMN user_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户姓名';
ALTER TABLE t_mini_account_sale_prize
    ADD COLUMN user_phone VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户电话';

