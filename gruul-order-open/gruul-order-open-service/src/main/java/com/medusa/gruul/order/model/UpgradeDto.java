package com.medusa.gruul.order.model;

import com.medusa.gruul.order.api.model.ItemDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:17 2025/4/8
 */
@Data
@ApiModel(value = "判断是否升级参数")
public class UpgradeDto {
    /**
     * 商品规格数量Map
     */
    @NotEmpty(message = "商品不能为空")
    @ApiModelProperty("商品规格数量Map")
    private List<ItemDto> items;
}
