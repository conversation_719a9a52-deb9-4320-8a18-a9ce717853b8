package com.medusa.gruul.order.controller.mini;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * SSE (Server-Sent Events) 工具类
 * 用于管理服务器向客户端推送实时消息
 * 支持租户隔离和心跳检测
 *
 * <AUTHOR>
 */
@Slf4j
public class SseUtil {

    /**
     * 存储客户端连接信息 key: clientId, value: SseEmitterWrapper
     */
    private static final Map<String, SseEmitterWrapper> emitterMap = new ConcurrentHashMap<>();

    /**
     * 租户与客户端映射 key: tenantId, value: Set<clientId>
     */
    private static final Map<String, Set<String>> tenantClientMap = new ConcurrentHashMap<>();

    /**
     * 默认超时时间：30分钟
     */
    private static final long DEFAULT_TIMEOUT = 30 * 60 * 1000L;

    /**
     * 心跳间隔：30秒
     */
    private static final long HEARTBEAT_INTERVAL = 30 * 1000L;

    /**
     * 连接清理间隔：5分钟
     */
    private static final long CLEANUP_INTERVAL = 5 * 60 * 1000L;

    /**
     * 定时任务执行器
     */
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    /**
     * SSE连接包装类
     */
    private static class SseEmitterWrapper {
        private final SseEmitter emitter;
        private final String clientId;
        private final String tenantId;
        private final String userId;
        private final LocalDateTime createTime;
        private volatile LocalDateTime lastHeartbeat;

        public SseEmitterWrapper(SseEmitter emitter, String clientId, String tenantId, String userId) {
            this.emitter = emitter;
            this.clientId = clientId;
            this.tenantId = tenantId;
            this.userId = userId;
            this.createTime = LocalDateTime.now();
            this.lastHeartbeat = LocalDateTime.now();
        }

        public SseEmitter getEmitter() {
            return emitter;
        }

        public String getClientId() {
            return clientId;
        }

        public String getTenantId() {
            return tenantId;
        }

        public String getUserId() {
            return userId;
        }

        public LocalDateTime getCreateTime() {
            return createTime;
        }

        public LocalDateTime getLastHeartbeat() {
            return lastHeartbeat;
        }

        public void updateHeartbeat() {
            this.lastHeartbeat = LocalDateTime.now();
        }
    }

    static {
        // 启动心跳检测任务
        startHeartbeatTask();
        // 启动连接清理任务
        startCleanupTask();
    }

    /**
     * 创建新的SSE连接
     */
    public static SseEmitter create(String clientId) {
        return create(clientId, null, null, DEFAULT_TIMEOUT);
    }

    /**
     * 创建新的SSE连接
     */
    public static SseEmitter create(String clientId, String tenantId, String userId) {
        return create(clientId, tenantId, userId, DEFAULT_TIMEOUT);
    }

    /**
     * 创建新的SSE连接
     */
    public static SseEmitter create(String clientId, String tenantId, String userId, long timeout) {
        // 如果已存在则先关闭
        if (emitterMap.containsKey(clientId)) {
            removeClient(clientId);
        }

        SseEmitter emitter = new SseEmitter(timeout);
        SseEmitterWrapper wrapper = new SseEmitterWrapper(emitter, clientId, tenantId, userId);
        emitterMap.put(clientId, wrapper);

        // 添加到租户映射
        if (StrUtil.isNotBlank(tenantId)) {
            tenantClientMap.computeIfAbsent(tenantId, k -> ConcurrentHashMap.newKeySet()).add(clientId);
        }

        // 设置回调
        emitter.onCompletion(() -> {
            removeClient(clientId);
            log.info("SSE连接完成: clientId={}, tenantId={}, userId={}", clientId, tenantId, userId);
        });
        emitter.onTimeout(() -> {
            removeClient(clientId);
            log.warn("SSE连接超时: clientId={}, tenantId={}, userId={}", clientId, tenantId, userId);
        });
        emitter.onError(e -> {
            removeClient(clientId);
            log.error("SSE连接错误: clientId={}, tenantId={}, userId={}, error={}",
                     clientId, tenantId, userId, e.getMessage());
        });

        log.info("SSE连接创建成功: clientId={}, tenantId={}, userId={}", clientId, tenantId, userId);

        // 发送连接成功消息
        try {
            emitter.send(SseEmitter.event()
                    .name("connected")
                    .data("连接成功"));
        } catch (IOException e) {
            log.error("发送连接成功消息失败: {}", e.getMessage());
        }

        return emitter;
    }

    /**
     * 发送事件给指定客户端
     */
    public static void send(String clientId, SseEvent event) {
        if (emitterMap.containsKey(clientId)) {
            try {
                emitterMap.get(clientId).send(
                        SseEmitter.event()
                                .id(event.getEventId())
                                .name(event.getEventName())
                                .data(event.getData())
                );
            } catch (IOException e) {
                emitterMap.remove(clientId);
                throw new RuntimeException("发送SSE事件失败: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 广播事件给所有客户端
     */
    public static void broadcast(SseEvent event) {
        emitterMap.forEach((clientId, emitter) -> {
            send(clientId, event);
        });
    }

    /**
     * 关闭指定客户端的连接
     */
    public static void complete(String clientId) {
        if (emitterMap.containsKey(clientId)) {
            emitterMap.get(clientId).complete();
            emitterMap.remove(clientId);
        }
    }

}
