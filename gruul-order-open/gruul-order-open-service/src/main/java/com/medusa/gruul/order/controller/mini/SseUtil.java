package com.medusa.gruul.order.controller.mini;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

public class SseUtil {
    private static final Map<String, SseEmitter> emitterMap = new ConcurrentHashMap<>();
    private static final long DEFAULT_TIMEOUT = 30 * 60 * 1000L; // 10分钟 自动关闭

    /**
     * 创建新的SSE连接
     */
    public static SseEmitter create(String clientId) {
        return create(clientId, DEFAULT_TIMEOUT);
    }

    /**
     * 创建新的SSE连接
     */
    public static SseEmitter create(String clientId, long timeout) {
        // 如果已存在则先关闭
        if (emitterMap.containsKey(clientId)) {
            emitterMap.get(clientId).complete();
        }

        SseEmitter emitter = new SseEmitter(timeout);
        emitterMap.put(clientId, emitter);

        // 设置回调
        emitter.onCompletion(() -> {
            emitterMap.remove(clientId);
            System.out.println("SSE连接完成: " + clientId);
        });
        emitter.onTimeout(() -> {
            emitterMap.remove(clientId);
            System.out.println("SSE连接超时: " + clientId);
        });
        emitter.onError(e -> {
            emitterMap.remove(clientId);
            System.out.println("SSE连接错误: " + clientId + ", " + e.getMessage());
        });

        return emitter;
    }

    /**
     * 发送事件给指定客户端
     */
    public static void send(String clientId, SseEvent event) {
        if (emitterMap.containsKey(clientId)) {
            try {
                emitterMap.get(clientId).send(
                        SseEmitter.event()
                                .id(event.getEventId())
                                .name(event.getEventName())
                                .data(event.getData())
                );
            } catch (IOException e) {
                emitterMap.remove(clientId);
                throw new RuntimeException("发送SSE事件失败: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 广播事件给所有客户端
     */
    public static void broadcast(SseEvent event) {
        emitterMap.forEach((clientId, emitter) -> {
            send(clientId, event);
        });
    }

    /**
     * 关闭指定客户端的连接
     */
    public static void complete(String clientId) {
        if (emitterMap.containsKey(clientId)) {
            emitterMap.get(clientId).complete();
            emitterMap.remove(clientId);
        }
    }

}
