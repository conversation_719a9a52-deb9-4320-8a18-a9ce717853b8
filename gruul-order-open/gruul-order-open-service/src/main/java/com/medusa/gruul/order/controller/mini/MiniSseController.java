package com.medusa.gruul.order.controller.mini;

import com.medusa.gruul.common.core.annotation.EscapeLogin;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import io.swagger.annotations.*;

import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/mini/sse")
//@CrossOrigin(origins = "*")
@Api(tags = "小程序端商家订单接口")
public class MiniSseController {

    private static SseEmitter emitter;

    @EscapeLogin
    @GetMapping("/getOrderPayMsg")
    @ApiOperation("SSE推送订单下单成功消息")
    public String getOrderPayMsg(String orderId) {
        SseEvent event = new SseEvent("SSE推送订单下单成功消息", "123");
        SseUtil.send("123", event);
        return "事件发送成功";
    }

    @EscapeLogin
    @GetMapping(value = "/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter connect(@RequestParam String clientId) {
        return SseUtil.create(clientId);
    }

   /* @EscapeLogin
    @PostMapping("/broadcast")
    public String broadcastEvent(
            @RequestParam String eventName,
            @RequestBody Object data) {
        SseEvent event = new SseEvent(eventName, data);
        SseUtil.broadcast(event);
        return "广播发送成功";
    }*/
    @EscapeLogin
    @GetMapping("/close")
    public String close(@RequestParam String clientId) {
        SseUtil.complete(clientId);
        return "连接已关闭";
    }
}