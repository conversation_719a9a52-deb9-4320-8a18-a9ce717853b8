package com.medusa.gruul.api.web.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 外部系统子表状态枚举
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@Getter
public enum ExternalSystemItemEnum {
    /**
     * 本系统对外部
     */
    SYSTEM (0, "本系统对外部"),
    /**
     * 外部对本系统
     */
    EXTERNAL(1, "外部对本系统"),
    /**
     * 启用
     */
    ENABLE(1, "启用"),
    /**
     * 停用
     */
    DEACTIVATE(0, "停用"),


    /**
     * 子表类型
     */
    SUB_TABLE(1,"子表类型"),
    /**
     * 字符串类型
     */
    VARDHAR_TYPE(2,"字符串类型"),
    /**
     * 数字类型
     */
    NUMERIC_TYPE(3,"数字类型"),
    /**
     * 时间类型
     */
    TIME_TYPE(4,"时间类型"),
    /**
     * 字段所在的表的类型
     */
    TABLE_PARENT(1,"主表"),
    /**
     * 字段所在的表的类型
     */
    TABLE_SUB(2,"子表");

    @EnumValue
    /**
     * 值
     */
    private final int status;

    /**
     * 描述
     */
    private final String desc;

    ExternalSystemItemEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
