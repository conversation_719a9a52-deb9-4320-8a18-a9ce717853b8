package com.medusa.gruul.platform.api.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: plh
 * @Description: 角色授权dto
 * @Date: Created in 14:28 2023/9/19
 */
@Data
@ApiModel(value = "角色授权dto")
public class AuthRoleMenuDto {

    @ApiModelProperty(value = "角色id")
    @NotNull
    private Long roleId;

    @ApiModelProperty(value = "父级菜单")
    @NotEmpty
    private List<MenuDto> menuParent;

    @ApiModelProperty(value = "菜单")
    private List<MenuDto> menu;

    @ApiModelProperty(value = "按钮")
    private List<ButtonDto>button;


}
