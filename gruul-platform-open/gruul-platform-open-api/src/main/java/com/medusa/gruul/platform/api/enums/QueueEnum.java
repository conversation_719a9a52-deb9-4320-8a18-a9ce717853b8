package com.medusa.gruul.platform.api.enums;

import com.medusa.gruul.platform.api.constant.ExchangeConstant;
import com.medusa.gruul.platform.api.constant.QueueNameConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum QueueEnum {

    /**
     * 订阅消息发送队列
     */
    PLATFORM_SUBSCRIBE_MSG_SEND(ExchangeConstant.PLATFORM_EXCHANGE, QueueNameConstant.PLATFORM_SUBSCRIBE_MSG_SEND, QueueNameConstant.PLATFORM_SUBSCRIBE_MSG_SEND),

    /**
     * 微信订阅消息发送队列
     */
    PLATFORM_WX_SEND_MESSAGE(ExchangeConstant.WX_SEND_MESSAGE_EXCHANGE,QueueNameConstant.PLATFORM_WX_SEND_MESSAGE,QueueNameConstant.PLATFORM_WX_SEND_MESSAGE),
    /**
     * 公众号模板消息发送队列
     */
    PLATFORM_WX_MP_SEND_MESSAGE(ExchangeConstant.WX_MP_SEND_MESSAGE_EXCHANGE,QueueNameConstant.PLATFORM_WX_MP_SEND_MESSAGE,QueueNameConstant.PLATFORM_WX_MP_SEND_MESSAGE),
    /**
     * 批量发送发货消息队列
     */
    PLATFORM_BATCH_SAVE_SHOP_MESSAGE(ExchangeConstant.PLATFORM_EXCHANGE, QueueNameConstant.PLATFORM_BATCH_SAVE_SHOP_MESSAGE, QueueNameConstant.PLATFORM_BATCH_SAVE_SHOP_MESSAGE),

    /**
     * 批量发送代发货消息队列
     */
    PLATFORM_BATCH_SAVE_DELIVERY_PROXY_MESSAGE(ExchangeConstant.PLATFORM_EXCHANGE, QueueNameConstant.PLATFORM_BATCH_SAVE_DELIVERY_PROXY_MESSAGE, QueueNameConstant.PLATFORM_BATCH_SAVE_DELIVERY_PROXY_MESSAGE),

    /**
     * 批量发送提现审核消息队列
     */
    PLATFORM_BATCH_SAVE_COMMISSION_CASH_MESSAGE(ExchangeConstant.PLATFORM_EXCHANGE, QueueNameConstant.PLATFORM_BATCH_SAVE_COMMISSION_CASH_MESSAGE, QueueNameConstant.PLATFORM_BATCH_SAVE_COMMISSION_CASH_MESSAGE),
    /**
     * 套餐延迟队列
     */
    PLATFORM_PACKAGE_DUE(ExchangeConstant.PLATFORM_DELAY_EXCHANGE, QueueNameConstant.PLATFORM_PACKAGE_DUE, QueueNameConstant.PLATFORM_PACKAGE_DUE),

    /**
     * 入驻申请审核通过 拷贝职位模版、服务项目模版
     */
    PLATFORM_COPY_TEMPLATE_MESSAGE(ExchangeConstant.PLATFORM_COPY_TEMPLATE_EXCHANGE, QueueNameConstant.PLATFORM_BATCH_COPY_TEMPLATE_MESSAGE, QueueNameConstant.PLATFORM_BATCH_COPY_TEMPLATE_MESSAGE),
    /**
     * 批量发送取消预约订单消息队列
     */
    PLATFORM_CANCEL_RESERVATION_ORDER(ExchangeConstant.PLATFORM_CANCEL_RESERVATION_ORDER_EXCHANGE, QueueNameConstant.PLATFORM_CANCEL_RESERVATION_ORDER_MESSAGE, QueueNameConstant.PLATFORM_CANCEL_RESERVATION_ORDER_MESSAGE),

    /**
     * SSE消息推送队列
     */
    PLATFORM_SSE_MESSAGE_SEND(ExchangeConstant.PLATFORM_SSE_EXCHANGE, QueueNameConstant.PLATFORM_SSE_MESSAGE_SEND, QueueNameConstant.PLATFORM_SSE_MESSAGE_SEND);

    /**
     * 交换名称
     */
    private String exchange;
    /**
     * 队列名称
     */
    private String name;
    /**
     * 路由键
     */
    private String routeKey;

    QueueEnum(String exchange, String name, String routeKey) {
        this.exchange = exchange;
        this.name = name;
        this.routeKey = routeKey;
    }
}
