package com.medusa.gruul.platform.api.model.dto;

import com.medusa.gruul.common.core.constant.enums.LoginTerminalEnum;
import com.medusa.gruul.common.core.util.IDUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * SSE事件消息DTO
 */
@Data
@NoArgsConstructor
@ApiModel(value = "SSE事件消息DTO")
public class SseEventDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "事件ID")
    private String eventId = IDUtil.getId().toString();

    @ApiModelProperty(value = "租户ID", required = true)
    private String tenantId;

    /**
     * 目标客户端ID 为空则推送给该租户下所有客户端
     */
    @ApiModelProperty(value = "目标客户端ID")
    private String targetClientId;

    /**
     * 登录类型 PC/MINI/H5 为空则推送给所有类型
     */
    @ApiModelProperty(value = "登录类型", required = true)
    private LoginTerminalEnum loginType;

    @ApiModelProperty(value = "事件类型")
    @NotBlank(message = "事件类型不能为空")
    private String eventType;

    @ApiModelProperty(value = "消息内容", required = true)
    @NotNull(message = "消息内容不能为空")
    private Object data;


    public SseEventDto(String tenantId, String eventType, Object data) {
        this.tenantId = tenantId;
        this.eventType = eventType;
        this.data = data;
    }

    public SseEventDto(String tenantId, String targetClientId, String eventType, Object data) {
        this.tenantId = tenantId;
        this.targetClientId = targetClientId;
        this.eventType = eventType;
        this.data = data;
    }

}
