package com.medusa.gruul.platform.api.constant;

/**
 * <AUTHOR>
 * @description 用户模块队列
 * @data: 2019/12/11
 */
public class QueueNameConstant {

    /**
     * 发送订阅消息
     */
    public static final String PLATFORM_SUBSCRIBE_MSG_SEND = "gruul.platform.subscribe.msg.send";
    /**
     * 发送微信订阅消息
     */
    public static final String PLATFORM_WX_SEND_MESSAGE = "gruul.platform.wx.send.message";
    /**
     * 发送公众号模板消息
     */
    public static final String PLATFORM_WX_MP_SEND_MESSAGE = "gruul.platform.wx.mp.send.message";

    /**
     * 套餐到期延迟队列
     */
    public static final String PLATFORM_PACKAGE_DUE = "gruul.platform.package.due";

    /**
     * 发送发货消息队列
     */
    public static final String PLATFORM_SAVE_SHOP_MESSAGE = "gruul.platform.save.shop.message";

    /**
     * 批量发送发货消息队列
     */
    public static final String PLATFORM_BATCH_SAVE_SHOP_MESSAGE = "gruul.platform.batch.save.shop.message";

    /**
     * SSE消息推送队列
     */
    public static final String PLATFORM_SSE_MESSAGE_SEND = "gruul.platform.sse.message.send";

    /**
     * 订单发货成功-标记关联消息为已读
     */
    public static final String PLATFORM_MESSAGE_READ_ALREADY = "gruul.platform.message.read.already";

    /**
     * 批量发送代发货消息队列
     */
    public static final String PLATFORM_BATCH_SAVE_DELIVERY_PROXY_MESSAGE = "gruul.platform.batch.save.delivery.proxy.message";

    /**
     * 批量发送提现审核消息队列
     */
    public static final String PLATFORM_BATCH_SAVE_COMMISSION_CASH_MESSAGE = "gruul.platform.batch.save.commission.cash.message";

    /**
     * 入驻申请审核通过 拷贝职位模版、服务项目模版
     */
    public static final String PLATFORM_BATCH_COPY_TEMPLATE_MESSAGE = "gruul.platform.batch.copy.template.message";

    /**
     * 预约订单 超时自动取消
     */
    public static final String PLATFORM_CANCEL_RESERVATION_ORDER_MESSAGE = "gruul.platform.cancel.reservation.order.message";

}
