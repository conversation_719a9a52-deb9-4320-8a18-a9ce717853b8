package com.medusa.gruul.platform.api.constant;

/**
 * <AUTHOR>
 * @description 用户模块交换机
 * @data: 2019/12/11
 */
public class ExchangeConstant {

    /**
     * 平台服务交换机
     */
    public static final String PLATFORM_EXCHANGE = "gruul.platform.exchange";
    /**
     * 平台服务迟交换机
     */
    public static final String PLATFORM_DELAY_EXCHANGE = "gruul.platform.delay.exchange";

    /**
     * 订阅消息发送队列
     */
    public static final String WX_SEND_MESSAGE_EXCHANGE = "gruul.platform.wx.send.message.exchange";

    /**
     * 订阅消息发送队列
     */
    public static final String WX_MP_SEND_MESSAGE_EXCHANGE = "gruul.platform.wx.mp.send.message.exchange";


    public static final String PLATFORM_COPY_TEMPLATE_EXCHANGE = "gruul.platform.copy.template.exchange";

    public static final String PLATFORM_CANCEL_RESERVATION_ORDER_EXCHANGE = "gruul.platform.cancel.reservation.order.exchange";

    /**
     * SSE消息推送交换机
     */
    public static final String PLATFORM_SSE_EXCHANGE = "gruul.platform.sse.exchange";
}
