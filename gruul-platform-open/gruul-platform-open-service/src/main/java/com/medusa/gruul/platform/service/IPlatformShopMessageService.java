package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.model.message.AccountCommissionCashMessage;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.order.api.model.OrderDeliveryProxyMessage;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.platform.api.entity.PlatformShopMessage;
import com.medusa.gruul.platform.api.model.dto.SubscribeMsgSendDto;
import com.medusa.gruul.platform.api.model.vo.MiniMsgVo;
import com.medusa.gruul.platform.api.model.vo.ShopMessageVo;
import com.medusa.gruul.platform.model.dto.MotifyMsgStateDto;
import com.medusa.gruul.platform.model.dto.PlatformShopMessAgeParamDto;
import com.medusa.gruul.platform.model.vo.MessageDataVo;

import java.util.List;

/**
 * <p>
 * 店铺消息配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
public interface IPlatformShopMessageService extends IService<PlatformShopMessage> {

    /**
     * 获取店铺消息
     *
     * @return com.medusa.gruul.platform.api.model.vo.ShopMessageVo
     */
    List<ShopMessageVo> msgAll();

    /**
     * 发送订阅消息
     *
     * @param msgSendDto com.medusa.gruul.platform.api.model.dto.SubscribeMsgSendDto
     */
    void subscribeMsgSend(SubscribeMsgSendDto msgSendDto);

    /**
     * 修改消息状态
     *
     * @param msgStateDto com.medusa.gruul.platform.model.dto.MotifyMsgStateDto
     */
    void modifyState(MotifyMsgStateDto msgStateDto);

    /**
     * 保存订单发货提醒消息
     * @param orderVo
     */
    void saveDeliverOrderMessage(OrderVo orderVo);

    /**
     * 保存代发货订单发货提醒消息
     * @param message
     */
    void saveDeliverProxyOrderMessage(OrderDeliveryProxyMessage message);

    /**
     * 分页查询消息中心数据
     * @param platformShopMessAgeParamDto
     * @return
     */
    PageUtils<PlatformShopMessage> searchMessageInfo(PlatformShopMessAgeParamDto platformShopMessAgeParamDto);

    /**
     * 删除消息
     * @param id
     */
    void deleteMessageInfo(Long id);

    /**
     * 根据id标记消息为已读
     * @param id
     */
    void updateStatus(Long id);

    /**
     * 批量设置消息为已读为已读
     * @param ids
     */
    void updateBatchStatus(String ids);

    /**
     * 获取语音播报信息
     * @return
     */
    MessageDataVo getMessageDataVo();

    /**
     * 保存佣金提现提醒消息
     * @param message
     */
    void batchSaveCommissionCashMessage(AccountCommissionCashMessage message);
}
