package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.AuthUserRole;
import com.medusa.gruul.platform.model.dto.AuthUserRoleParamDto;
import com.medusa.gruul.platform.model.vo.AuthUserMenuVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: 用户角色关系服务类
 * @Date: Created in 19:04 2023/8/16
 */
public interface IAuthUserRoleService extends IService<AuthUserRole> {

    /**
     * 保存用户角色关系
     * @param authUserRoleParamDto
     */
    void saveAuthUserRole(AuthUserRoleParamDto authUserRoleParamDto);

    /**
     * 删除用户角色关系
     * @param authUserRoleParamDto
     */
    void deleteAuthUserRole(AuthUserRoleParamDto authUserRoleParamDto);

    /**
     * 获取用户菜单
     * @param id
     * @return
     */
    List<AuthUserMenuVo> getAuthUserMenuVo(Long id);

    /**
     * 获取用户二级菜单
     * @param id
     * @param menuPId
     * @return
     */
    List<AuthUserMenuVo> getAuthUserMenuSecondVo(Long id,Long menuPId);
}
