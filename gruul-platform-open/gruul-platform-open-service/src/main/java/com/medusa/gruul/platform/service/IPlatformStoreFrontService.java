package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.platform.api.entity.PlatformDepartment;
import com.medusa.gruul.platform.api.entity.PlatformStoreFront;
import com.medusa.gruul.platform.api.model.dto.OutPlatformDepartmentDto;
import com.medusa.gruul.platform.api.model.dto.OutPlatformStoreFrontDto;
import com.medusa.gruul.platform.api.model.dto.StoreFrontAddressDto;
import com.medusa.gruul.platform.api.model.vo.StoreFrontVo;
import com.medusa.gruul.platform.model.dto.PlatformStoreFrontDto;
import com.medusa.gruul.platform.model.param.ApiStoreFrontParam;
import com.medusa.gruul.platform.model.param.StoreFrontParam;
import com.medusa.gruul.platform.model.vo.ApiStoreFrontVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:27 2024/10/8
 */
public interface IPlatformStoreFrontService extends IService<PlatformStoreFront> {

    /**
     * 接收外部系统门店信息
     * @param outPlatformStoreFrontDto
     * @return
     */
    PlatformStoreFront newAdd(OutPlatformStoreFrontDto outPlatformStoreFrontDto);

    /**
     * 分页查询门店信息
     * @param storeFrontParam
     * @return
     */
    PageUtils<StoreFrontVo> searchStoreFront(StoreFrontParam storeFrontParam);

    /**
     * 获取门店编号
     * @param parentCode
     * @return
     */
    String getStoreNumber(String parentCode);

    /**
     * 添加门店
     * @param dto
     */
    void addStoreFront(PlatformStoreFrontDto dto);

    /**
     * 获取门店列表
     * @param param
     * @return
     */
    List<ApiStoreFrontVo> getApiStoreFront(ApiStoreFrontParam param);

    /**
     * 启用，停用门店信息
     * @param outPlatformStoreFrontDto
     */
    void updateStatusId(OutPlatformStoreFrontDto outPlatformStoreFrontDto);

    /**
     * 保存门店地址信息
     * @param storeFrontAddressDto
     */
    void updateAddress(StoreFrontAddressDto storeFrontAddressDto);


    PageUtils<StoreFrontVo> pageBindStoreFront(StoreFrontParam storeFrontParam);

    /**
     * 删除门店
     * @param dto
     */
    void deleteStoreFront(PlatformStoreFrontDto dto);

    /**
     * 编辑门店
     * @param dto
     */
    void editStoreFront(PlatformStoreFrontDto dto);
}
