package com.medusa.gruul.platform.mq;

import com.medusa.gruul.platform.api.enums.QueueEnum;
import com.medusa.gruul.platform.api.model.dto.ReservationOrderCancelMessage;
import com.medusa.gruul.platform.service.IReservationOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Slf4j
@Component
public class Sender {

    @Autowired
    private IReservationOrderService reservationOrderService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void sendAutoCancelReservationOrderMessage(ReservationOrderCancelMessage message, long expiration) {
        log.info("sendAutoCancelReservationOrderMessage:" + message);
        log.info("send time:" + LocalDateTime.now());
        rabbitTemplate.convertAndSend(QueueEnum.PLATFORM_CANCEL_RESERVATION_ORDER.getExchange(),
                QueueEnum.PLATFORM_CANCEL_RESERVATION_ORDER.getRouteKey() , message, new ExpirationMessagePostProcessor(expiration));
    }

}