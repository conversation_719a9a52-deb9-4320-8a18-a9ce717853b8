package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.model.dto.SpecialSettingDto;
import com.medusa.gruul.platform.api.model.vo.SpecialSettingVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: 特殊配置服务类
 * @Date: Created in 11:03 2024/8/15
 */
public interface ISpecialSettingService extends IService<SpecialSetting> {

    /**
     * 获取特殊配置
     * @return
     */
    SpecialSettingVo getSpecialSetting();

    /**
     * 保存或更新特殊配置
     * @param specialSettingDto
     */
    void saveSpecialSetting(SpecialSettingDto specialSettingDto);

    /**
     * 获取允许平台查看订单的店铺id
     * @return
     */
    List<String> getSpecialSettingShopIds(String shopId);

    /**
     * 通过店铺id获取特殊配置
     * @param shopId
     * @return
     */
    List<SpecialSetting> getSpecialSettingByShopId(String shopId);

    /**
     * 获取主店铺特殊配置
     * @return
     */
    SpecialSettingVo getMainSpecialSetting();
}
