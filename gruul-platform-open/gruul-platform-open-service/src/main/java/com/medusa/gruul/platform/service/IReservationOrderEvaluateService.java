package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.ReservationOrderEvaluate;
import com.medusa.gruul.platform.model.dto.ReservationOrderEvaluateDto;
import com.medusa.gruul.platform.model.param.ReservationOrderEvaluateQueryParam;
import com.medusa.gruul.platform.model.vo.ReservationOrderEvaluateVo;


/**
 * 预约单评价表 服务接口
 *
 * <AUTHOR>
 * @Date: 2025-06-11
 */
public interface IReservationOrderEvaluateService extends IService<ReservationOrderEvaluate> {

    /**
     * 新增预约单评价
     *
     * @param dto 预约单评价信息
     * @return 是否成功
     */
    boolean evaluate(ReservationOrderEvaluateDto dto);

    /**
     * 分页查询预约单评价列表
     *
     * @param param 查询参数
     * @return 预约单评价列表
     */
    IPage<ReservationOrderEvaluateVo> getPage(ReservationOrderEvaluateQueryParam param);

    /**
     * 根据预约单id查询评价信息
     *
     * @param id 预约单id
     * @return 评价信息
     */
    ReservationOrderEvaluateVo getByOrderId(Long id);
} 