package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.AuthMenuInfo;
import com.medusa.gruul.platform.api.model.dto.AuthMenuInfoDto;
import com.medusa.gruul.platform.api.model.dto.AuthMenuInfoSecondDto;
import com.medusa.gruul.platform.api.model.vo.AuthMenuInfoVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: 平台菜单 服务类
 * @Date: Created in 14:11 2023/8/16
 */
public interface IAuthMenuInfoService extends IService<AuthMenuInfo> {


    /**
     * 查询所有菜单
     *
     * @return
     */
    List<AuthMenuInfoVo> getAllAuthMenuInfoList();

    /**
     * 新增一级菜单
     *
     * @param authMenuInfoDto
     */
    void saveAuthMenuInfo(AuthMenuInfoDto authMenuInfoDto);
    /**
     * 新增二级菜单
     *
     * @param authMenuInfoSecondDtos
     */
    void addSecondList(List<AuthMenuInfoSecondDto> authMenuInfoSecondDtos);

    /**
     * 删除菜单
     *
     * @param id
     */
    void deleteAuthMenuInfo(Long id);

    /**
     * 修改菜单
     * @param authMenuInfoDto
     */
    void updateAuthMenuInfo(AuthMenuInfoDto authMenuInfoDto);
}
