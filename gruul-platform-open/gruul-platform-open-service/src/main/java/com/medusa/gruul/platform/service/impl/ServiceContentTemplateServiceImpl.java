package com.medusa.gruul.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.SpringContextHolder;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.api.entity.PlatformPosition;
import com.medusa.gruul.platform.api.entity.PlatformPositionTemplate;
import com.medusa.gruul.platform.api.entity.ServiceContentTemplate;
import com.medusa.gruul.platform.api.enums.PlatformPositionEnums;
import com.medusa.gruul.platform.model.dto.ServiceContentTemplateDto;
import com.medusa.gruul.platform.model.param.ServiceContentTemplateParam;
import com.medusa.gruul.platform.model.vo.ServiceContentTemplateVo;
import com.medusa.gruul.platform.mapper.ServiceContentTemplateMapper;
import com.medusa.gruul.platform.service.IPlatformPositionService;
import com.medusa.gruul.platform.service.IServiceContentTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 服务项目模板Service实现类
 */
@Service
@Slf4j
public class ServiceContentTemplateServiceImpl extends ServiceImpl<ServiceContentTemplateMapper, ServiceContentTemplate> implements IServiceContentTemplateService {

    @Override
    public IPage<ServiceContentTemplateVo> page(ServiceContentTemplateParam queryDto) {
        Page<ServiceContentTemplate> page = new Page<>(queryDto.getCurrent(), queryDto.getSize());

        return baseMapper.selectServiceContentTemplateList(page, queryDto);
    }

    @Override
    public boolean add(ServiceContentTemplateDto dto) {
        // 检查服务项目名称是否重复
        if (checkServiceNameExists(dto.getServiceName(), null)) {
            throw new ServiceException("服务项目名称已存在", SystemCode.PARAM_VALID_ERROR.getCode());
        }
        
        // 设置默认状态为启用
        if (dto.getStatus() == null) {
            dto.setStatus(1);
        }
        
        ServiceContentTemplate entity = new ServiceContentTemplate();
        BeanUtils.copyProperties(dto, entity);
        // 设置创建人信息
        CurUserDto user = CurUserUtil.getHttpCurUser();
        entity.setCreateUserId(Long.valueOf(user.getUserId()));
        entity.setCreateUserName(user.getNikeName());
        return save(entity);
    }

    @Override
    public boolean update(ServiceContentTemplateDto dto) {
        // 检查ID是否存在
        if (dto.getId() == null) {
            throw new ServiceException("ID不能为空", SystemCode.PARAM_MISS.getCode());
        }
        
        // 检查服务项目是否存在
        ServiceContentTemplate existEntity = getById(dto.getId());
        if (existEntity == null) {
            throw new ServiceException("服务项目模板不存在", SystemCode.DATA_NOT_EXIST.getCode());
        }
        
        // 检查服务项目名称是否重复
        if (checkServiceNameExists(dto.getServiceName(), dto.getId())) {
            throw new ServiceException("服务项目名称已存在", SystemCode.DATA_EXISTED.getCode());
        }
        
        ServiceContentTemplate entity = new ServiceContentTemplate();
        BeanUtils.copyProperties(dto, entity);

        // 设置修改人信息
        CurUserDto user = CurUserUtil.getHttpCurUser();
        entity.setLastModifyUserId(Long.valueOf(user.getUserId()));
        entity.setLastModifyUserName(user.getNikeName());
        
        return updateById(entity);
    }

    @Override
    public boolean delete(Long id) {
        // 检查ID是否存在
        if (id == null) {
            throw new ServiceException("ID不能为空", SystemCode.PARAM_MISS.getCode());
        }
        return removeById(id);
    }

    @Override
    public boolean checkServiceNameExists(String serviceName, Long id) {
        if (serviceName == null){
            throw new ServiceException("缺少参数", SystemCode.PARAM_MISS.getCode());
        }
        LambdaQueryWrapper<ServiceContentTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ServiceContentTemplate::getServiceName, serviceName);
        if (id != null) {
            queryWrapper.ne(ServiceContentTemplate::getId, id);
        }
        return count(queryWrapper)  > 0;
    }


} 