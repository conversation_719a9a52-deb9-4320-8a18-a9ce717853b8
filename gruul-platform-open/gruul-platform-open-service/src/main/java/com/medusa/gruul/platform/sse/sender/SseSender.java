package com.medusa.gruul.platform.sse.sender;

import cn.hutool.core.util.IdUtil;
import com.medusa.gruul.platform.api.enums.QueueEnum;
import com.medusa.gruul.platform.api.model.dto.SseEventDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * SSE消息发送器
 * 用于向RabbitMQ发送SSE推送消息
 */
@Slf4j
@Component
public class SseSender {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送SSE消息到队列
     *
     * @param sseEventDto SSE事件消息
     */
    public void sendSseMessage(SseEventDto sseEventDto) {
        try {
            log.info("发送SSE消息到队列: eventType={}, tenantId={}, targetClientId={}", 
                    sseEventDto.getEventType(), sseEventDto.getTenantId(), sseEventDto.getTargetClientId());
            
            convertAndSend(QueueEnum.PLATFORM_SSE_MESSAGE_SEND, sseEventDto);
            
        } catch (Exception e) {
            log.error("发送SSE消息到队列失败: {}", e.getMessage(), e);
            throw new RuntimeException("发送SSE消息失败", e);
        }
    }

    /**
     * 发送消息到指定队列
     *
     * @param queue 队列枚举
     * @param message 消息内容
     */
    private void convertAndSend(QueueEnum queue, Object message) {
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        rabbitTemplate.convertAndSend(queue.getExchange(), queue.getRouteKey(), message, correlationData);
    }
}
