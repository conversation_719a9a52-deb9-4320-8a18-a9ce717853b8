package com.medusa.gruul.platform.mq;

import com.medusa.gruul.account.api.constant.AccountQueueNameConstant;
import com.medusa.gruul.order.api.constant.OrderConstant;
import com.medusa.gruul.order.api.constant.OrderQueueEnum;
import com.medusa.gruul.platform.api.constant.ExchangeConstant;
import com.medusa.gruul.platform.api.constant.QueueNameConstant;
import com.medusa.gruul.platform.api.enums.QueueEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.annotation.RabbitListenerConfigurer;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistrar;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;
import org.springframework.messaging.handler.annotation.support.DefaultMessageHandlerMethodFactory;
import org.springframework.messaging.handler.annotation.support.MessageHandlerMethodFactory;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description: RabbitConfig.java
 * @Author: alan
 * @Date: 2019/10/6 14:01
 */
@Slf4j
@Configuration
public class RabbitConfig implements RabbitListenerConfigurer {
    @Resource
    private RabbitTemplate rabbitTemplate;

    @Override
    public void configureRabbitListeners(RabbitListenerEndpointRegistrar registrar) {
        registrar.setMessageHandlerMethodFactory(messageHandlerMethodFactory());
    }

    @Bean
    MessageHandlerMethodFactory messageHandlerMethodFactory() {
        DefaultMessageHandlerMethodFactory messageHandlerMethodFactory = new DefaultMessageHandlerMethodFactory();
        messageHandlerMethodFactory.setMessageConverter(consumerJackson2MessageConverter());
        return messageHandlerMethodFactory;
    }

    @Bean
    public MappingJackson2MessageConverter consumerJackson2MessageConverter() {
        return new MappingJackson2MessageConverter();
    }

    @Bean
    public AmqpTemplate amqpTemplate() {
        // 使用jackson 消息转换器
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        rabbitTemplate.setEncoding("UTF-8");
        // 消息发送失败返回到队列中，yml需要配置 publisher-returns: true
        rabbitTemplate.setMandatory(true);
        rabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> {
            String correlationId = message.getMessageProperties().getCorrelationId();
            log.info("消息：{} 发送失败, 应答码：{} 原因：{} 交换机: {}  路由键: {}", correlationId, replyCode, replyText, exchange, routingKey);
        });
        // 消息确认，yml需要配置 publisher-confirms: true
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.info("消息发送到exchange成功,id: {}", correlationData.getId());
            } else {
                log.info("消息发送到exchange失败,原因: {}", cause);
            }
        });
        return rabbitTemplate;
    }

    /**
     * 平台服务交换机
     */
    @Bean
    DirectExchange platformDirect() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(ExchangeConstant.PLATFORM_EXCHANGE)
                .durable(true)
                .build();
    }
    /**
     * 订单消息实际消费队列所绑定的交换机
     */
    @Bean
    DirectExchange orderDirect() {
        return (DirectExchange) ExchangeBuilder.directExchange(OrderConstant.EXCHANGE_NAME).durable(true).build();
    }

    /**
     * 订阅消息绑定的交换机
     * @return
     */
    @Bean
    DirectExchange wxSendMessageDirect(){
        return (DirectExchange) ExchangeBuilder
                .directExchange(ExchangeConstant.WX_SEND_MESSAGE_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     *  模板消息绑定的交换机
     * @return
     */
    @Bean
    DirectExchange wxMpSendMessageDirect(){
        return (DirectExchange) ExchangeBuilder
                .directExchange(ExchangeConstant.WX_MP_SEND_MESSAGE_EXCHANGE)
                .durable(true)
                .build();
    }
    /**
     * 公众号模板消息发送队列
     * @return
     */
    @Bean
    public Queue wxMpSendMessageQueue(){
        return new Queue(QueueEnum.PLATFORM_WX_MP_SEND_MESSAGE.getName(),true);
    }

    /**
     * 公众号模板消息发送队列绑定交换机
     * @param wxMpSendMessageDirect
     * @param wxMpSendMessageQueue
     * @return
     */
    @Bean
    Binding wxMpSendMessageBinding(DirectExchange wxMpSendMessageDirect,Queue wxMpSendMessageQueue){
        return BindingBuilder
                .bind(wxMpSendMessageQueue)
                .to(wxMpSendMessageDirect)
                .with(QueueEnum.PLATFORM_WX_MP_SEND_MESSAGE.getRouteKey());
    }

    /**
     * 微信订阅消息发送队列
     * @return
     */
    @Bean
    public Queue wxSendMessageQueue(){
        return new Queue(QueueEnum.PLATFORM_WX_SEND_MESSAGE.getName(),true);
    }

    /**
     * 微信订阅消息发送队列绑定交换机
     * @param wxSendMessageDirect
     * @param wxSendMessageQueue
     * @return
     */
    @Bean
    Binding wxSendMessageBinding(DirectExchange wxSendMessageDirect,Queue wxSendMessageQueue){
        return BindingBuilder
                .bind(wxSendMessageQueue)
                .to(wxSendMessageDirect)
                .with(QueueEnum.PLATFORM_WX_SEND_MESSAGE.getRouteKey());
    }

    /**
     * 订阅消息发送队列
     */
    @Bean
    public Queue subscribeMsgSendQueue() {
        return new Queue(QueueEnum.PLATFORM_SUBSCRIBE_MSG_SEND.getName(), true);
    }


    /**
     * 订阅消息发送队列绑定交换机
     */
    @Bean
    Binding subscribeMsgSendBinding(DirectExchange platformDirect, Queue subscribeMsgSendQueue) {
        return BindingBuilder
                .bind(subscribeMsgSendQueue)
                .to(platformDirect)
                .with(QueueEnum.PLATFORM_SUBSCRIBE_MSG_SEND.getRouteKey());
    }




    /**
     * 延迟队列队列所绑定的交换机
     */
    @Bean
    CustomExchange platefromDelayDirect() {
        Map<String, Object> args = new HashMap<>(1);
        args.put("x-delayed-type", "direct");
        return new CustomExchange(ExchangeConstant.PLATFORM_DELAY_EXCHANGE, "x-delayed-message", true, false, args);
    }


    @Bean
    public Queue packageDueQueue() {
        return new Queue(QueueEnum.PLATFORM_PACKAGE_DUE.getName());
    }

    @Bean
    public Binding bindingNotify(Queue packageDueQueue, CustomExchange platefromDelayDirect) {
        return BindingBuilder.bind(packageDueQueue).to(platefromDelayDirect).with(QueueEnum.PLATFORM_PACKAGE_DUE.getRouteKey()).noargs();
    }


    /**
     * 用户订单支付成功队列
     */
    @Bean
    public Queue orderPayOkQueue() {
        return new Queue(QueueNameConstant.PLATFORM_SAVE_SHOP_MESSAGE, true);
    }
    /**
     * 将订单完成队列绑定到交换机
     */
    @Bean
    Binding orderPayBinding(DirectExchange orderDirect, Queue orderPayOkQueue) {
        return BindingBuilder.bind(orderPayOkQueue).to(orderDirect)
                .with(OrderQueueEnum.QUEUE_ORDER_PAYED.getRouteKey());
    }

    /**
     * 用户订单发货成功队列
     */
    @Bean
    public Queue orderShipped() {
        return new Queue(QueueNameConstant.PLATFORM_MESSAGE_READ_ALREADY, true);
    }


    /**
     * 将订单发货成功队列绑定到交换机
     */
    @Bean
    Binding orderShippedBinding(DirectExchange orderDirect, Queue orderShipped) {
        return BindingBuilder.bind(orderShipped).to(orderDirect)
                .with(OrderQueueEnum.QUEUE_ORDER_SHIPPED.getRouteKey());
    }

    /**
     * 批量发送发货消息队列
     */
    @Bean
    public Queue batchSaveMessageQueue() {
        return new Queue(QueueNameConstant.PLATFORM_BATCH_SAVE_SHOP_MESSAGE, true);
    }

    /**
     * 将批量发送发货消息队列绑定到交换机
     */
    @Bean
    Binding batchSaveMessageBinding(DirectExchange platformDirect, Queue batchSaveMessageQueue) {
        return BindingBuilder.bind(batchSaveMessageQueue).to(platformDirect)
                .with(QueueEnum.PLATFORM_BATCH_SAVE_SHOP_MESSAGE.getRouteKey());
    }

    @Bean
    public Queue batchSaveDeliveryProxyMessageQueue() {
        return new Queue(QueueNameConstant.PLATFORM_BATCH_SAVE_DELIVERY_PROXY_MESSAGE, true);
    }

    @Bean
    Binding batchSaveDeliveryProxyMessageBinding(DirectExchange platformDirect, Queue batchSaveDeliveryProxyMessageQueue) {
        return BindingBuilder.bind(batchSaveDeliveryProxyMessageQueue).to(platformDirect)
                .with(QueueEnum.PLATFORM_BATCH_SAVE_DELIVERY_PROXY_MESSAGE.getRouteKey());
    }
    @Bean
    public Queue batchSaveCommissionCashMessageQueue() {
        return new Queue(QueueNameConstant.PLATFORM_BATCH_SAVE_COMMISSION_CASH_MESSAGE, true);
    }
    @Bean
    Binding batchSaveCommissionCashMessageBinding(DirectExchange platformDirect, Queue batchSaveCommissionCashMessageQueue) {
        return BindingBuilder.bind(batchSaveCommissionCashMessageQueue).to(platformDirect)
                .with(QueueEnum.PLATFORM_BATCH_SAVE_COMMISSION_CASH_MESSAGE.getRouteKey());
    }
    @Bean
    DirectExchange batchCopyTemplateExchange(){
        return (DirectExchange) ExchangeBuilder
                .directExchange(QueueEnum.PLATFORM_COPY_TEMPLATE_MESSAGE.getExchange())
                .durable(true)
                .build();
    }
    @Bean
    public Queue batchCopyTemplateMessageQueue() {
        return new Queue(QueueEnum.PLATFORM_COPY_TEMPLATE_MESSAGE.getName(), true);
    }

    @Bean
    Binding batchCopyTemplateMessageBinding(DirectExchange batchCopyTemplateExchange, Queue batchCopyTemplateMessageQueue) {
        return BindingBuilder.bind(batchCopyTemplateMessageQueue)
                .to(batchCopyTemplateExchange)
                .with(QueueEnum.PLATFORM_COPY_TEMPLATE_MESSAGE.getRouteKey());
    }

    /**
     * 预约订单延迟队列所绑定的交换机
     */
    @Bean
    CustomExchange reservationOrderDelayDirect() {
        Map<String, Object> args = new HashMap<>(1);
        args.put("x-delayed-type", "direct");
        return new CustomExchange(QueueEnum.PLATFORM_CANCEL_RESERVATION_ORDER.getExchange(), "x-delayed-message", true, false, args);
    }

    @Bean
    public Queue autoCancelReservationOrderQueue() {
        return new Queue(QueueEnum.PLATFORM_CANCEL_RESERVATION_ORDER.getName(), true);
    }

    @Bean
    Binding autoCancelReservationOrderBinding(CustomExchange reservationOrderDelayDirect, Queue autoCancelReservationOrderQueue) {
        return BindingBuilder.bind(autoCancelReservationOrderQueue)
                .to(reservationOrderDelayDirect)
                .with(QueueEnum.PLATFORM_CANCEL_RESERVATION_ORDER.getRouteKey()).noargs();
    }

    /**
     * SSE消息推送交换机
     */
    @Bean
    DirectExchange sseMessageDirect() {
        return (DirectExchange) ExchangeBuilder
                .directExchange(ExchangeConstant.PLATFORM_SSE_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * SSE消息推送队列
     */
    @Bean
    public Queue sseMessageQueue() {
        return new Queue(QueueEnum.PLATFORM_SSE_MESSAGE_SEND.getName(), true);
    }

    /**
     * SSE消息推送队列绑定交换机
     */
    @Bean
    Binding sseMessageBinding(DirectExchange sseMessageDirect, Queue sseMessageQueue) {
        return BindingBuilder
                .bind(sseMessageQueue)
                .to(sseMessageDirect)
                .with(QueueEnum.PLATFORM_SSE_MESSAGE_SEND.getRouteKey());
    }
}
