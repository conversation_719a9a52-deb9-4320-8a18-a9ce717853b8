package com.medusa.gruul.platform.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.api.entity.PlatformPositionTemplate;
import com.medusa.gruul.platform.api.enums.PlatformPositionEnums;
import com.medusa.gruul.platform.mapper.PlatformPositionTemplateMapper;
import com.medusa.gruul.platform.model.param.PlatformPositionTemplateParam;
import com.medusa.gruul.platform.service.IPlatformPositionTemplateService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商家职位模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
public class PlatformPositionTemplateServiceImpl extends ServiceImpl<PlatformPositionTemplateMapper, PlatformPositionTemplate> implements IPlatformPositionTemplateService {

    @Override
    public IPage<PlatformPositionTemplate> getPositionTemplateList(PlatformPositionTemplateParam templateParam) {
        return baseMapper.selectPositionTemplateList(new Page<>(templateParam.getCurrent(), templateParam.getSize()), templateParam);
    }

    @Override
    public boolean savePositionTemplate(PlatformPositionTemplate platformPositionTemplate) {
        // 校验名称是否为空
        if (StringUtils.isBlank(platformPositionTemplate.getPositionName())) {
            throw new ServiceException("职位名称不能为空");
        }
        
        // 校验名称唯一性
        boolean exists = this.checkPositionNameExists(platformPositionTemplate.getPositionName(), null);
        if (exists) {
            throw new ServiceException("职位名称已存在，请修改后重试");
        }
        
        // 设置默认值
        if (platformPositionTemplate.getStatus() == null) {
            platformPositionTemplate.setStatus(PlatformPositionEnums.StatusEnum.ENABLED.getCode()); // 默认启用
        }
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        platformPositionTemplate.setCreateUserId(Long.parseLong(curUser.getUserId()));
        platformPositionTemplate.setCreateUserName(curUser.getNikeName());

        return this.save(platformPositionTemplate);
    }

    @Override
    public boolean updatePositionTemplate(PlatformPositionTemplate platformPositionTemplate) {
        // id
        if (platformPositionTemplate.getId() == null) {
            throw new ServiceException("id不能为空");
        }
        // 校验名称是否为空
        if (StringUtils.isBlank(platformPositionTemplate.getPositionName())) {
            throw new ServiceException("职位名称不能为空");
        }
        
        // 校验名称唯一性
        boolean exists = this.checkPositionNameExists(platformPositionTemplate.getPositionName(), platformPositionTemplate.getId());
        if (exists) {
            throw new ServiceException("职位名称已存在，请修改后重试");
        }
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        platformPositionTemplate.setLastModifyUserId(Long.parseLong(curUser.getUserId()));
        platformPositionTemplate.setLastModifyUserName(curUser.getNikeName());
        return this.updateById(platformPositionTemplate);
    }

    @Override
    public boolean checkPositionNameExists(String positionName, Long id) {
        if (StringUtils.isBlank(positionName)) {
            return false;
        }
        
        LambdaQueryWrapper<PlatformPositionTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlatformPositionTemplate::getPositionName, positionName);
        
        // 排除自身ID
        if (id != null) {
            queryWrapper.ne(PlatformPositionTemplate::getId, id);
        }
        
        return this.count(queryWrapper) > 0;
    }

    @Override
    public boolean removePositionTemplate(Long id) {

        return removeById(id);
    }
} 