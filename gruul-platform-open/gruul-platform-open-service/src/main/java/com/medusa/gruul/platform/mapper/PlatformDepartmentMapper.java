package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.platform.api.entity.PlatformDepartment;
import com.medusa.gruul.platform.api.model.vo.DepartmentVo;
import com.medusa.gruul.platform.model.param.DepartmentParam;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:41 2024/9/12
 */
@Repository
public interface PlatformDepartmentMapper extends BaseMapper<PlatformDepartment> {

    /**
     * 分页查询部门信息
     * @param page
     * @param param
     * @return
     */
    IPage<DepartmentVo>searchDepartment(Page<DepartmentVo>page, @Param("params")DepartmentParam param);

}
