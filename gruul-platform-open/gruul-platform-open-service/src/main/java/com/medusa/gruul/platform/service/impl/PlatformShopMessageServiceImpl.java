package com.medusa.gruul.platform.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.account.api.model.message.AccountCommissionCashMessage;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.LoginTerminalEnum;
import com.medusa.gruul.common.core.constant.enums.MessageStatusEnum;
import com.medusa.gruul.common.core.constant.enums.MessageTypeEnum;
import com.medusa.gruul.common.core.constant.enums.UseTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.common.redis.RedisManager;
import com.medusa.gruul.order.api.model.OrderDeliveryProxyMessage;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.platform.api.entity.*;
import com.medusa.gruul.platform.api.model.dto.SseEventDto;
import com.medusa.gruul.platform.api.model.dto.SubscribeMsgSendDto;
import com.medusa.gruul.platform.api.model.vo.ShopMessageDetailVo;
import com.medusa.gruul.platform.api.model.vo.ShopMessageVo;
import com.medusa.gruul.platform.mapper.PlatformShopMessageMapper;
import com.medusa.gruul.platform.model.dto.MotifyMsgStateDto;
import com.medusa.gruul.platform.model.dto.PlatformShopMessAgeParamDto;
import com.medusa.gruul.platform.model.vo.MessageDataVo;
import com.medusa.gruul.platform.service.*;
import com.medusa.gruul.platform.sse.ClientInfo;
import com.medusa.gruul.platform.sse.util.SseUtil;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 店铺消息配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
@Service
@Log4j2
public class PlatformShopMessageServiceImpl extends ServiceImpl<PlatformShopMessageMapper, PlatformShopMessage> implements IPlatformShopMessageService {

    @Autowired
    private IPlatformShopTemplateDetailService platformShopTemplateDetailService;
    @Autowired
    private IPlatformShopInfoService platformShopInfoService;
    @Autowired
    private IAuthUserRoleService authUserRoleService;
    @Autowired
    private IAuthRoleMessageService authRoleMessageService;


    @Override
    public List<ShopMessageVo> msgAll() {
        PlatformShopInfo info = platformShopInfoService.getInfo();
        PlatformShopTemplateDetail templateDetail = platformShopTemplateDetailService.getById(info.getShopTemplateDetailId());
        if (BeanUtil.isEmpty(templateDetail)) {
            return new ArrayList<>();
        }
        List<PlatformShopMessage> list = this.baseMapper.selectList(new QueryWrapper<PlatformShopMessage>()
                .eq("version", templateDetail.getVersion())
                .eq("use_type", CommonConstants.NUMBER_ONE));
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        Map<Integer, List<PlatformShopMessage>> listMap = list.stream().collect(Collectors.groupingBy(PlatformShopMessage::getMessageType));
        List<ShopMessageVo> vos = new LinkedList<>();
        //获取订单消息
        getVos(listMap.get(CommonConstants.NUMBER_ONE), vos, "订单消息");
        //获取用户消息
        getVos(listMap.get(CommonConstants.NUMBER_THREE), vos, "用户消息");
        return vos;
    }

    /**
     * 发送订阅消息
     *
     * @param msgSendDto com.medusa.gruul.platform.api.model.dto.SubscribeMsgSendDto
     */
    @Override
    public void subscribeMsgSend(SubscribeMsgSendDto msgSendDto) {

    }

    /**
     * 封装数据
     *
     * @param platformShopMessages 指定类型数据
     * @param list                 数组
     * @param title                标题
     */
    private void getVos(List<PlatformShopMessage> platformShopMessages, List<ShopMessageVo> list, String title) {
        if (CollectionUtil.isEmpty(platformShopMessages)) {
            return;
        }
        ShopMessageVo vo = new ShopMessageVo();
        vo.setMsgTitle(title);
        List<ShopMessageDetailVo> vos = new LinkedList<>();
        for (PlatformShopMessage platformShopMessage : platformShopMessages) {
            ShopMessageDetailVo shopMessageDetailVo = BeanUtil.toBean(platformShopMessage, ShopMessageDetailVo.class);
            vos.add(shopMessageDetailVo);
        }
        vo.setShopMessageDetailVos(vos);
        list.add(vo);
    }




    @Override
    public void modifyState(MotifyMsgStateDto msgStateDto) {
        PlatformShopMessage shopMessage = this.getById(msgStateDto.getId());
        if (shopMessage == null) {
            throw new ServiceException("不存在指定消息");
        }
        if (msgStateDto.getMiniOpen() != null && msgStateDto.getMiniOpen() > 0) {
            String miniTemplateId = shopMessage.getMiniTemplateId();
            if (StrUtil.isEmpty(miniTemplateId)) {
                throw new ServiceException("请上传审核小程序之后再开启");
            }
        }
        PlatformShopMessage platformShopMessage = BeanUtil.toBean(msgStateDto, PlatformShopMessage.class);
        this.updateById(platformShopMessage);
    }

    @Override
    public void saveDeliverOrderMessage(OrderVo orderVo) {

        PlatformShopMessage platformShopMessage = new PlatformShopMessage();
        platformShopMessage.setTenantId(orderVo.getTenantId());
        platformShopMessage.setShopId(orderVo.getShopId());
        platformShopMessage.setOrderId(orderVo.getId().toString());
        platformShopMessage.setUseType(UseTypeEnum.SHOP.getStatus());
        platformShopMessage.setTitle("发货通知");
        platformShopMessage.setMessageType(MessageTypeEnum.ORDER_MESSAGE.getStatus());
        platformShopMessage.setStatus(MessageStatusEnum.NO.getStatus());
        platformShopMessage.setContent("您有一条新的付款订单（"+orderVo.getId()+"），请前去发货！");

        this.save(platformShopMessage);

    }

    /**
     * 保存代发货订单发货提醒消息
     * @param message
     */
    @Override
    public void saveDeliverProxyOrderMessage(OrderDeliveryProxyMessage message) {
        PlatformShopMessage platformShopMessage = new PlatformShopMessage();
        platformShopMessage.setTenantId(message.getTenantId());
        platformShopMessage.setShopId(message.getShopId());
        platformShopMessage.setOrderId(message.getId().toString());
        platformShopMessage.setUseType(UseTypeEnum.SHOP.getStatus());
        platformShopMessage.setTitle("代发货发货通知");
        platformShopMessage.setMessageType(MessageTypeEnum.PROXY_SHIPPING_MESSAGE.getStatus());
        platformShopMessage.setStatus(MessageStatusEnum.NO.getStatus());
        platformShopMessage.setContent("您有一条新的代发货订单（"+message.getId()+"），请前去发货！");

        this.save(platformShopMessage);
    }


    @Override
    public PageUtils<PlatformShopMessage> searchMessageInfo(PlatformShopMessAgeParamDto platformShopMessAgeParamDto) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String userId = curUserDto.getUserId();
        LambdaQueryWrapper<AuthUserRole>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AuthUserRole::getUserId,userId);
        List<AuthUserRole> list = authUserRoleService.list(lambdaQueryWrapper);


        List<Integer>messageTypes = new ArrayList<>();
        messageTypes.add(0);

        if(list!=null&&list.size()>0){

            for (AuthUserRole authUserRole : list) {
                Long roleId = authUserRole.getRoleId();
                List<Integer> dataList = authRoleMessageService.getMessageTypeByRoleId(roleId);
                if(dataList!=null&&dataList.size()>0){
                    for (Integer integer : dataList) {
                        if(!messageTypes.contains(integer)){
                            messageTypes.add(integer);
                        }
                    }
                }
            }
        }
        // 超级管理员 所有类型
        if (curUserDto.getIsSuper() == 1) {
            List<AuthRoleMessage> all = authRoleMessageService.list();
            if (CollectionUtil.isNotEmpty( all)){
                messageTypes = all.stream().map(AuthRoleMessage::getMessageType).collect(Collectors.toList());
            }
        }
        LambdaQueryWrapper<PlatformShopMessage>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformShopMessage::getUseType,UseTypeEnum.SHOP.getStatus());
        wrapper.isNull(PlatformShopMessage::getMpMsg);
        wrapper.isNull(PlatformShopMessage::getMiniMsg);
        wrapper.in(PlatformShopMessage::getMessageType,messageTypes);
        if(platformShopMessAgeParamDto.getStatus()!=null){
            wrapper.eq(PlatformShopMessage::getStatus,platformShopMessAgeParamDto.getStatus());
        }
        wrapper.orderByAsc(PlatformShopMessage::getStatus);
        wrapper.orderByDesc(PlatformShopMessage::getCreateTime);
        IPage<PlatformShopMessage> page = this.page(new Page<PlatformShopMessage>(platformShopMessAgeParamDto.getCurrent(), platformShopMessAgeParamDto.getSize()), wrapper);
        searchMessageInfo2();
        return new PageUtils(page);
    }


    @Autowired
    private SseUtil sseUtil;
    public  void searchMessageInfo2() {
        Set<ClientInfo> pcClientSet = sseUtil.getClientInfoByLoginType(LoginTerminalEnum.PC);
        RedisManager redisManager = RedisManager.getInstance();
        pcClientSet.forEach(clientInfo -> {
            String user = redisManager.get(clientInfo.getClientId());
            if (StringUtil.isBlank( user)){
                return;
            }
            CurUserDto curUserDto = JSON.parseObject(user, CurUserDto.class);
            Integer isSuper = 1;
            LambdaQueryWrapper<AuthUserRole>lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(AuthUserRole::getUserId,curUserDto.getUserId());
            List<AuthUserRole> list = authUserRoleService.list(lambdaQueryWrapper);


            List<Integer>messageTypes = new ArrayList<>();
            messageTypes.add(0);

            if(list!=null&&list.size()>0){

                for (AuthUserRole authUserRole : list) {
                    Long roleId = authUserRole.getRoleId();
                    List<Integer> dataList = authRoleMessageService.getMessageTypeByRoleId(roleId);
                    if(dataList!=null&&dataList.size()>0){
                        for (Integer integer : dataList) {
                            if(!messageTypes.contains(integer)){
                                messageTypes.add(integer);
                            }
                        }
                    }
                }
            }
            // 超级管理员 所有类型
            if (isSuper == 1) {
                List<AuthRoleMessage> all = authRoleMessageService.list();
                if (CollectionUtil.isNotEmpty( all)){
                    messageTypes = all.stream().map(AuthRoleMessage::getMessageType).collect(Collectors.toList());
                }
            }
            LambdaQueryWrapper<PlatformShopMessage>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PlatformShopMessage::getUseType,UseTypeEnum.SHOP.getStatus());
            wrapper.isNull(PlatformShopMessage::getMpMsg);
            wrapper.isNull(PlatformShopMessage::getMiniMsg);
            wrapper.in(PlatformShopMessage::getMessageType,messageTypes);
            wrapper.eq(PlatformShopMessage::getStatus,0);
            wrapper.orderByAsc(PlatformShopMessage::getStatus);
            wrapper.orderByDesc(PlatformShopMessage::getCreateTime);
            IPage<PlatformShopMessage> page = this.page(new Page<PlatformShopMessage>(1, 1), wrapper);
            SseEventDto sseEventDto = new SseEventDto(clientInfo.getTenantId(),clientInfo.getClientId(),"newMessage", new PageUtils(page));
            sseUtil.sendToClient(sseEventDto);
        });
    }

    @Override
    public MessageDataVo getMessageDataVo() {

        MessageDataVo messageDataVo = new MessageDataVo();
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String userId = curUserDto.getUserId();
        LambdaQueryWrapper<AuthUserRole>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(AuthUserRole::getUserId,userId);
        List<AuthUserRole> list = authUserRoleService.list(lambdaQueryWrapper);

        List<Integer>messageTypes = new ArrayList<>();
        messageTypes.add(0);

        if(list!=null&&list.size()>0){

            for (AuthUserRole authUserRole : list) {
                Long roleId = authUserRole.getRoleId();
                List<Integer> dataList = authRoleMessageService.getMessageTypeByRoleId(roleId);
                if(dataList!=null&&dataList.size()>0){
                    for (Integer integer : dataList) {
                        if(!messageTypes.contains(integer)){
                            messageTypes.add(integer);
                        }
                    }
                }
            }
        }
        LambdaQueryWrapper<PlatformShopMessage>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformShopMessage::getUseType,UseTypeEnum.SHOP.getStatus());
        wrapper.isNull(PlatformShopMessage::getMpMsg);
        wrapper.isNull(PlatformShopMessage::getMiniMsg);
        wrapper.in(PlatformShopMessage::getMessageType,messageTypes);
        wrapper.eq(PlatformShopMessage::getStatus,CommonConstants.NUMBER_ZERO);
        wrapper.orderByAsc(PlatformShopMessage::getStatus);
        wrapper.orderByDesc(PlatformShopMessage::getCreateTime);

        List<PlatformShopMessage> dataList = this.list(wrapper);
        Map<Integer, List<PlatformShopMessage>> dataMap = dataList.stream().collect(Collectors.groupingBy(PlatformShopMessage::getMessageType));
        List<Integer>dataMessageTypes = new ArrayList<>();

        for (Map.Entry<Integer, List<PlatformShopMessage>> entry : dataMap.entrySet()) {
            Integer key = entry.getKey();
            dataMessageTypes.add(key);
        }
        messageDataVo.setTotal(dataList.size());
        messageDataVo.setMessageTypes(dataMessageTypes);

        return messageDataVo;
    }

    /**
     * 保存佣金提现提醒消息
     * @param message
     */
    @Override
    public void batchSaveCommissionCashMessage(AccountCommissionCashMessage message) {
        PlatformShopMessage platformShopMessage = new PlatformShopMessage();
        platformShopMessage.setTenantId(message.getTenantId());
        platformShopMessage.setShopId(message.getShopId());
        platformShopMessage.setOrderId(message.getId().toString());
        platformShopMessage.setUseType(UseTypeEnum.SHOP.getStatus());
        platformShopMessage.setTitle("佣金提现通知");
        platformShopMessage.setMessageType(MessageTypeEnum.REFLECT_MESSAGE.getStatus());
        platformShopMessage.setStatus(MessageStatusEnum.NO.getStatus());
        platformShopMessage.setContent("您有一条新的提现审核（"+message.getId()+"），请前去审核！");
        this.save(platformShopMessage);
    }


    @Override
    public void deleteMessageInfo(Long id) {
        if(id==null){
            throw new ServiceException("id不能为空！");
        }
        //删除消息
        this.removeById(id);
    }

    @Override
    public void updateStatus(Long id) {
        if(id==null){
            throw new ServiceException("id不能为空！");
        }
        PlatformShopMessage platformShopMessage = this.getById(id);
        platformShopMessage.setStatus(MessageStatusEnum.YES.getStatus());
        this.updateById(platformShopMessage);
    }

    @Override
    @Transactional
    public void updateBatchStatus(String ids) {
        if(StringUtils.isNotEmpty(ids)){
            List<String> updateIds =  Arrays.asList(ids.split(","));
            for (String updateId : updateIds) {
                updateStatus(Long.valueOf(updateId));
            }
        }else{
            throw new ServiceException("id不能为空");
        }
    }


}
