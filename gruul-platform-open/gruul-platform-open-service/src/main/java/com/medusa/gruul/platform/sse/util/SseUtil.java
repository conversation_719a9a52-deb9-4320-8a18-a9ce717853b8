package com.medusa.gruul.platform.sse.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.LoginTerminalEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.IDUtil;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.common.redis.RedisManager;
import com.medusa.gruul.platform.api.model.dto.SseEventDto;
import com.medusa.gruul.platform.sse.ClientInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * SSE连接管理工具类
 */
@Slf4j
@Component
public class SseUtil {

    /**
     * SSE连接超时时间10分
     */
    private static final long SSE_TIMEOUT = 10 * 60 * 1000L;

    /**
     * Redis中客户端的key + clientId>>ClientInfo
     */
    private static final String CLIENT_INFO_KEY_PREFIX = "sse:client:info:";

    /**
     * Redis中租户客户端关系集合 tenantId -> Set<clientId>
     */
    private static final String TENANT_CLIENT_SET_KEY_PREFIX = "sse:tenant:client:set:";

    private static RedisManager redisManager;

    

    /**
     * 连接存储：clientId-> SseEmitter
     */
    private static final Map<String, SseEmitter> CLIENT_CONNECTIONS = new ConcurrentHashMap<>();


    public SseEmitter createConnection(String clientId,LoginTerminalEnum loginType) {
        // 获取租户ID
        String tenantId = TenantContextHolder.getTenantId();
        if (StrUtil.isBlank(clientId)) {
            clientId = getClientId();
        }
        if (StrUtil.isBlank(tenantId) || StrUtil.isBlank(clientId) || loginType == null ) {
            throw new ServiceException("缺少参数");
        }
        return createConnection(tenantId, clientId, loginType);
    }


    /**
     *
     * 创建SSE连接
     *
     * @param tenantId 租户ID
     * @param clientId 客户端ID
     * @param loginType 登录类型
     * @return SseEmitter
     */
    public SseEmitter createConnection(String tenantId, String clientId, LoginTerminalEnum loginType) {
        if (StrUtil.isBlank(tenantId) || StrUtil.isBlank(clientId)) {
            throw new ServiceException("租户ID和客户端ID不能为空");
        }
        if (redisManager==null){
            redisManager = RedisManager.getInstance();
        }
        // 如果已存在
        if (CLIENT_CONNECTIONS.containsKey(clientId)){
            return CLIENT_CONNECTIONS.get(clientId);
        }
 /*       removeConnection(tenantId,clientId);*/

        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);
        
        // 设置连接回调
        emitter.onCompletion(() -> {
            log.info("SSE连接完成: tenantId={}, clientId={}", tenantId, clientId);
            removeConnection(tenantId, clientId);
        });

        emitter.onTimeout(() -> {
            log.warn("SSE连接超时: tenantId={}, clientId={}", tenantId, clientId);
            removeConnection(tenantId, clientId);
        });

        emitter.onError((throwable) -> {
            log.error("SSE连接错误: tenantId={}, clientId={}, error={}", tenantId, clientId, throwable.getMessage());
            removeConnection(tenantId, clientId);
        });

        // 存储连接
        CLIENT_CONNECTIONS.put(clientId, emitter);

        // 保存客户端信息到Redis
        ClientInfo clientInfo = new ClientInfo(tenantId, clientId, loginType);
        saveClientInfoToRedis(clientInfo);

        int count = getAllClientIds().size();
        log.info("新建SSE连接: tenantId={}, clientId={}, loginType={}, 当前连接数={}",
                tenantId, clientId, loginType, count);

      /*  // 发送连接成功消息
        SseEventDto connectEvent = new SseEventDto(tenantId, "callback", "连接成功");
        sendToClient(emitter, connectEvent);*/

        return emitter;
    }


    /**
     * 指定客户端id - 发送消息给
     * @param event 事件消息
     *
     */
    public boolean sendToClient(SseEventDto event) {
        String tenantId = event.getTenantId();
        String clientId = event.getTargetClientId();

        if (StrUtil.isBlank(tenantId) || StrUtil.isBlank(clientId)) {
            log.warn("租户ID或客户端ID为空，无法发送消息");
            return false;
        }

        SseEmitter emitter = CLIENT_CONNECTIONS.get(clientId);
        if (emitter == null) {
            log.warn("客户端{}没有SSE连接", clientId);
            return false;
        }

        return sendToClient(emitter, event);
    }


    /**
     * 发送消息给租户下所有客户端
     */
    public int sendToTenant(SseEventDto event) {
        String tenantId = event.getTenantId();
        LoginTerminalEnum loginType = event.getLoginType();

        if (StrUtil.isBlank(tenantId)) {
            log.warn("租户ID为空，无法发送消息");
            return 0;
        }

        // 获取租户下的所有客户端ID
        Set<String> tenantClientIds = getTenantClientIds(tenantId);
        if (CollectionUtil.isEmpty(tenantClientIds)) {
            log.warn("租户{}下没有SSE连接", tenantId);
            return 0;
        }

        int successCount = 0;
        for (String clientId : tenantClientIds) {
            SseEmitter emitter = CLIENT_CONNECTIONS.get(clientId);
            if (emitter == null) {
                removeConnection(tenantId, clientId);
                continue;
            }

            // 检查登录类型过滤
            if (loginType != null) {
                ClientInfo clientInfo = getClientInfo(clientId);
                if (clientInfo == null || !loginType.equals(clientInfo.getLoginType())) {
                    continue;
                }
            }

            if (sendToClient(emitter, event)) {
                successCount++;
            }
        }

        log.info("向租户{}发送消息，成功发送{}个客户端", tenantId, successCount);
        return successCount;
    }

    /**
     * 发送消息给所有连接
     */
    public int sendToAll(SseEventDto event) {
        int successCount = 0;

        // 获取所有租户ID
        Set<String> allTenantIds = getAllTenantIds();

        for (String tenantId : allTenantIds) {
            // 为每个租户创建一个副本，设置对应的tenantId
            SseEventDto tenantEvent = new SseEventDto(tenantId, event.getEventType(), event.getData());
            tenantEvent.setEventId(event.getEventId());
            tenantEvent.setLoginType(event.getLoginType());
            tenantEvent.setTargetClientId(event.getTargetClientId());

            successCount += sendToTenant(tenantEvent);
        }
        return successCount;
    }

    /**
     * 发送消息给指定SseEmitter
     */
    private boolean sendToClient(SseEmitter emitter, SseEventDto event) {
        try {
            if (StrUtil.isBlank(event.getEventId())) 
                event.setEventId(IDUtil.getId().toString());
            
            
            emitter.send(SseEmitter.event()
                    .id(event.getEventId())
                    .name(event.getEventType())
                    .data(event.getData()));
            return true;
        } catch (IOException e) {
            log.error("发送SSE消息失败: {}", e.getMessage());
            return false;
        }
    }

    public void removeConnection(String clientId) {
        if (StrUtil.isBlank(clientId)){
            clientId = getClientId();
        }
        removeConnection(TenantContextHolder.getTenantId(), clientId);
    }
    /**
     * 移除连接
     */
    public void removeConnection(String tenantId, String clientId) {
        SseEmitter emitter = CLIENT_CONNECTIONS.get(clientId);
        if (emitter != null) {
            // 关闭
            emitter.complete();
            CLIENT_CONNECTIONS.remove(clientId);
        }
        // 删除客户端
        removeClientInfo(clientId);
        int count = getAllClientIds().size();
        log.info("移除SSE连接: tenantId={}, clientId={}, 当前连接数={}", tenantId, clientId, count);
    }

    public static String getClientId() {
        // 使用token作为客户端ID
        ServletRequestAttributes requestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            HttpServletRequest request = requestAttributes.getRequest();
            return request.getHeader(CommonConstants.TOKEN);
        }
       throw new ServiceException("无法获取客户端ID");
    }
    /**
     * 保存客户端信息到Redis
     */
    private void saveClientInfoToRedis(ClientInfo clientInfo) {
        String key = CLIENT_INFO_KEY_PREFIX + clientInfo.getClientId();
        String tenantClientSetKey = TENANT_CLIENT_SET_KEY_PREFIX + clientInfo.getTenantId();

        // 保存客户端信息对象
        redisManager.set(key, JSON.toJSONString(clientInfo));
        // 添加到租户的客户端集合
        redisManager.sadd(tenantClientSetKey, clientInfo.getClientId());
        // 设置过期时间
        redisManager.expire(key, (int) (SSE_TIMEOUT / 1000));
        redisManager.expire(tenantClientSetKey, (int) (SSE_TIMEOUT / 1000));
    }

    /**
     * 从Redis获取客户端信息
     */
    private ClientInfo getClientInfo(String clientId) {
        String key = CLIENT_INFO_KEY_PREFIX + clientId;
        return getRedisObj(key,ClientInfo.class);
    }

    /**
     * 删除客户端信息
     */
    private void removeClientInfo(String clientId) {
        String key = CLIENT_INFO_KEY_PREFIX + clientId;

        // 先获取客户端信息以获得租户ID
        ClientInfo clientInfo = getRedisObj(key,ClientInfo.class);

        // 删除客户端信息
        redisManager.del(key);

        // 从租户的客户端集合中移除客户端ID
        if (clientInfo != null && StrUtil.isNotBlank(clientInfo.getTenantId())) {
            String tenantClientSetKey = TENANT_CLIENT_SET_KEY_PREFIX + clientInfo.getTenantId();
            redisManager.srem(tenantClientSetKey, clientId);
        }
    }

    /**
     * 获取指定租户的客户端ID集合
     */
    private Set<String> getTenantClientIds(String tenantId) {
        String tenantClientSetKey = TENANT_CLIENT_SET_KEY_PREFIX + tenantId;
        Set<String> clientIds = redisManager.smembers(tenantClientSetKey);
        return clientIds != null ? clientIds : new HashSet<>();
    }
    /**
     * 获取所有租户ID
     */
    private Set<String> getAllTenantIds() {
        Set<String> tenantIds = new HashSet<>();
        // 获取所有租户的客户端集合key
        Set<String> tenantKeys = redisManager.keys(TENANT_CLIENT_SET_KEY_PREFIX + "*");
        if (tenantKeys != null) {
            for (String tenantKey : tenantKeys) {
                String tenantId = tenantKey.substring(TENANT_CLIENT_SET_KEY_PREFIX.length());
                tenantIds.add(tenantId);
            }
        }
        return tenantIds;
    }

    /**
     * 根据登录终端获取所有客户端信息
     */
    public Set<ClientInfo> getClientInfoByLoginType(LoginTerminalEnum loginType) {
        String redisKey = CLIENT_INFO_KEY_PREFIX + "*";
        Set<String> keys = redisManager.keys(redisKey);
        return keys.stream()
                .map(key -> getRedisObj(key,ClientInfo.class))
                .filter(clientInfo -> clientInfo.getLoginType().equals(loginType)).collect(Collectors.toSet());
    }
    /**
     * 获取所有客户端ID
     */
    private Set<String> getAllClientIds() {
        Set<String> allClientIds = new HashSet<>();
        // 获取所有租户的客户端集合key
        Set<String> tenantKeys = redisManager.keys(TENANT_CLIENT_SET_KEY_PREFIX + "*");
        if (tenantKeys != null) {
            for (String tenantKey : tenantKeys) {
                Set<String> clientIds = redisManager.smembers(tenantKey);
                if (clientIds != null) {
                    allClientIds.addAll(clientIds);
                }
            }
        }
        return allClientIds;
    }

    public static  <T> T getRedisObj(String key, Class<T> clazz){
        String value = redisManager.get(key);
        if (value == null){
           return  null;
        }
        return JSON.parseObject(value, clazz);
    }

    public int getConnectionCount() {
        return getAllClientIds().size();
    }
    
    public int getTenantConnectionCount(String tenantId) {
        return getTenantClientIds(tenantId).size();
    }

}
