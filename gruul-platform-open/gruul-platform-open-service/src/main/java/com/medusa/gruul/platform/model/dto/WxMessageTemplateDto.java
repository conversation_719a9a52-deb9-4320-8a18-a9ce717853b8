package com.medusa.gruul.platform.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:01 2024/11/6
 */
@Data
@ApiModel(value="WxMessageTemplateDto对象", description="微信消息模板dto")
public class WxMessageTemplateDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "模板标识->1.活动通知;2.生日祝福提醒;3.商品上新;4.卖家发货提醒")
    private String code;

    @ApiModelProperty(value = "模板id")
    private String templateId;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "模板类型->1.订阅消息;2.公众号模板消息")
    private Integer type;

    @ApiModelProperty(value = "微信消息模板内容")
    private List<WxMessageTemplateDetailDto>list;

}
