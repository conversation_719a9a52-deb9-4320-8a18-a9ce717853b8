package com.medusa.gruul.platform.service.impl;

import com.medusa.gruul.platform.api.entity.PlatformRenovationTemplatePlugin;
import com.medusa.gruul.platform.mapper.PlatformRenovationTemplatePluginMapper;
import com.medusa.gruul.platform.service.IPlatformRenovationTemplatePluginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 平台装修模板全局控件属性表
 * @Author: jeecg-boot
 * @Date:   2023-09-06
 * @Version: V1.0
 */
@Service
@Slf4j
public class PlatformRenovationTemplatePluginServiceImpl extends ServiceImpl<PlatformRenovationTemplatePluginMapper, PlatformRenovationTemplatePlugin> implements IPlatformRenovationTemplatePluginService {

}
