package com.medusa.gruul.platform.sse;

import com.medusa.gruul.common.core.constant.enums.LoginTerminalEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客户端信息
 */
@Data
@NoArgsConstructor
public class ClientInfo {
    private String tenantId;
    private String clientId;
    private LoginTerminalEnum loginType;
    private Long connectTime;

    public ClientInfo(String tenantId, String clientId, LoginTerminalEnum loginType) {
        this.tenantId = tenantId;
        this.clientId = clientId;
        this.loginType = loginType;
        this.connectTime = System.currentTimeMillis();
    }
}