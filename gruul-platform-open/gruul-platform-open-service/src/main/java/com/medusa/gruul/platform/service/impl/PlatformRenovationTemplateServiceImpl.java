package com.medusa.gruul.platform.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.platform.api.entity.PlatformRenovationPageAssembly;
import com.medusa.gruul.platform.api.entity.PlatformRenovationTemplate;
import com.medusa.gruul.platform.api.entity.PlatformRenovationTemplatePage;
import com.medusa.gruul.platform.api.entity.PlatformRenovationTemplatePlugin;
import com.medusa.gruul.platform.api.model.vo.PlatformRenovationTemplateAllVo;
import com.medusa.gruul.platform.mapper.PlatformRenovationTemplateMapper;
import com.medusa.gruul.platform.service.IPlatformRenovationPageAssemblyService;
import com.medusa.gruul.platform.service.IPlatformRenovationTemplatePageService;
import com.medusa.gruul.platform.service.IPlatformRenovationTemplatePluginService;
import com.medusa.gruul.platform.service.IPlatformRenovationTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 平台装修模板表
 * @Author: jeecg-boot
 * @Date:   2023-09-06
 * @Version: V1.0
 */
@Service
@Slf4j
public class PlatformRenovationTemplateServiceImpl extends ServiceImpl<PlatformRenovationTemplateMapper, PlatformRenovationTemplate> implements IPlatformRenovationTemplateService {

    @Autowired
    private IPlatformRenovationTemplatePluginService platformRenovationTemplatePluginService;
    @Autowired
    private IPlatformRenovationTemplatePageService platformRenovationTemplatePageService;
    @Autowired
    private IPlatformRenovationPageAssemblyService platformRenovationPageAssemblyService;

    /**
     * 通过id查询装修模板相关的所有信息
     * @param id
     * @return
     */
    @Override
    public PlatformRenovationTemplateAllVo queryRelatedById(Long id) {
        PlatformRenovationTemplateAllVo vo = new PlatformRenovationTemplateAllVo();
        PlatformRenovationTemplate template = this.getById(id);
        BeanUtil.copyProperties(template, vo);
        // 查询模板对应的全局控件属性
        LambdaQueryWrapper<PlatformRenovationTemplatePlugin> pluginWrapper = new LambdaQueryWrapper<>();
        pluginWrapper.eq(PlatformRenovationTemplatePlugin::getTemplateId, template.getId());
        List<PlatformRenovationTemplatePlugin> pluginList = this.platformRenovationTemplatePluginService.list(pluginWrapper);
        vo.setPluginList(pluginList);
        // 查询模板对应的页面
        LambdaQueryWrapper<PlatformRenovationTemplatePage> pageWrapper = new LambdaQueryWrapper<>();
        pageWrapper.eq(PlatformRenovationTemplatePage::getTemplateId, template.getId());
        List<PlatformRenovationTemplatePage> pageList = this.platformRenovationTemplatePageService.list(pageWrapper);
        vo.setPageList(pageList);

        // 查询页面对应的页面组件属性
        List<Long> pageIdList = pageList.stream().map(PlatformRenovationTemplatePage::getId).collect(Collectors.toList());
        LambdaQueryWrapper<PlatformRenovationPageAssembly> assemblyWrapper = new LambdaQueryWrapper<>();
        assemblyWrapper.in(PlatformRenovationPageAssembly::getPageId, pageIdList);
        List<PlatformRenovationPageAssembly> assemblyList = this.platformRenovationPageAssemblyService.list(assemblyWrapper);
        vo.setAssemblyList(assemblyList);

        return vo;
    }
}
