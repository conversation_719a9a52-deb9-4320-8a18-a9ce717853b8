package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.AuthRoleMessage;
import com.medusa.gruul.platform.model.dto.AuthRoleMessageDto;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:04 2025/5/27
 */
public interface IAuthRoleMessageService extends IService<AuthRoleMessage> {
    /**
     * 根据角色id获取消息类型
     * @param roleId
     * @return
     */
    List<Integer> getMessageTypeByRoleId(Long roleId);
}
