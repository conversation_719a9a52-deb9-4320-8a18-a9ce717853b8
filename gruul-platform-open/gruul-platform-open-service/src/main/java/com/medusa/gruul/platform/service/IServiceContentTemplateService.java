package com.medusa.gruul.platform.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.platform.api.entity.ServiceContentTemplate;
import com.medusa.gruul.platform.model.dto.ServiceContentTemplateDto;
import com.medusa.gruul.platform.model.param.ServiceContentTemplateParam;
import com.medusa.gruul.platform.model.vo.ServiceContentTemplateVo;

/**
 * 服务项目模板Service接口
 */
public interface IServiceContentTemplateService extends IService<ServiceContentTemplate> {

    /**
     * 分页查询服务项目模板列表
     *
     * @param queryDto 查询条件
     * @return 分页结果
     */
    IPage<ServiceContentTemplateVo> page(ServiceContentTemplateParam queryDto);

    /**
     * 新增服务项目模板
     *
     * @param dto 服务项目模板DTO
     * @return 是否成功
     */
    boolean add(ServiceContentTemplateDto dto);

    /**
     * 修改服务项目模板
     *
     * @param dto 服务项目模板DTO
     * @return 是否成功
     */
    boolean update(ServiceContentTemplateDto dto);

    /**
     * 删除服务项目模板
     *
     * @param id ID
     * @return 是否成功
     */
    boolean delete(Long id);

    /**
     * 检查服务项目名称是否重复
     *
     * @param serviceName 服务项目名称
     * @param id ID，新增时为null
     * @return 是否重复
     */
    boolean checkServiceNameExists(String serviceName, Long id);
} 