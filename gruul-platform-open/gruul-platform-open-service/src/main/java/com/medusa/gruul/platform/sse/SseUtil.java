/*
package com.medusa.gruul.platform.sse;

import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.common.dto.SseEventDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

*/
/**
 * SSE 工具类
 *//*

@Slf4j
public class SseUtil {

    */
/**
     * 存储客户端连接信息
     *//*

    private static final Map<String, SseEmitter> emitterMap = new ConcurrentHashMap<>();

    */
/**
     * 租户与客户端映射
     *//*

    private static final Map<String, Set<String>> tenantClientMap = new ConcurrentHashMap<>();

    */
/**
     * 默认超时时间：10
     *//*

    private static final long DEFAULT_TIMEOUT = 10 * 60 * 1000L;

    */
/**
     * 创建新的SSE连接
     *//*

    public static SseEmitter create() {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (curUserDto == null){
           throw new RuntimeException("当前用户未登录");
        }
        // shopUserId + 登录类型
        return create(curUserDto.getUserId()+":"+curUserDto.getUserType()+":"+curUserDto.getIsSuper(), TenantContextHolder.getTenantId());
    }

    */
/**
     * 创建新的SSE连接
     *//*

    public static SseEmitter create(String clientId, String tenantId) {
        return create(clientId, tenantId, DEFAULT_TIMEOUT);
    }

    */
/**
     * 创建新的SSE连接
     *//*

    public static SseEmitter create(String clientId, String tenantId, long timeout) {
        // 如果已存在则先关闭
        if (emitterMap.containsKey(clientId)) {
            removeClient(clientId);
        }

        SseEmitter emitter = new SseEmitter(timeout);
        emitterMap.put(clientId, emitter);

        // 添加到租户映射
        if (StringUtil.isNotBlank(tenantId)) {
            tenantClientMap.computeIfAbsent(tenantId, k -> ConcurrentHashMap.newKeySet()).add(clientId);
        }

        // 设置回调
        emitter.onCompletion(() -> {
            removeClient(clientId);
            log.info("SSE连接完成: {}", clientId);
        });
        emitter.onTimeout(() -> {
            removeClient(clientId);
            log.warn("SSE连接超时: {}", clientId);
        });
        emitter.onError(e -> {
            removeClient(clientId);
            log.error("SSE连接错误: {}, {}", clientId, e.getMessage());
        });

        log.info("SSE连接创建: clientId={}, tenantId={}", clientId, tenantId);
        return emitter;
    }

    */
/**
     * 移除客户端连接
     *//*

    private static void removeClient(String clientId) {
        emitterMap.remove(clientId);
        // 从租户映射中移除
        tenantClientMap.values().forEach(clients -> clients.remove(clientId));
    }

    */
/**
     * 发送事件给指定客户端
     *//*

    public static void send(String clientId, SseEventDto event) {
        SseEmitter emitter = emitterMap.get(clientId);
        if (emitter != null) {
            try {
                emitter.send(SseEmitter.event()
                        .id(event.getEventId())
                        .name(event.getEventName())
                        .data(event.getData()));
                log.debug("发送SSE事件成功: clientId={}, event={}", clientId, event.getEventName());
            } catch (IOException e) {
                removeClient(clientId);
                log.error("发送SSE事件失败: clientId={}, error={}", clientId, e.getMessage());
            }
        } else {
            log.warn("客户端不存在: {}", clientId);
        }
    }

    */
/**
     * 广播事件给所有客户端
      *
     *//*

    public static void publish(SseEventDto event) {
        log.info("广播SSE事件: event={}, 客户端数量={}", event.getEventName(), emitterMap.size());
        emitterMap.keySet().forEach(clientId -> send(clientId, event));
    }
    public static void sendToTenant(String tenantId, SseEventDto event){
        sendToTenant(tenantId,null, event);
    }

    */
/**
     * 发送事件给指定租户下的所有客户端
     * userType 指定  pc 和小程序
     *//*

    public static void sendToTenant(String tenantId,Integer userType, SseEventDto event) {
        Set<String> clients = tenantClientMap.get(tenantId);
        if (clients != null && !clients.isEmpty()) {
            log.info("发送SSE事件给租户: tenantId={}, event={}, 客户端数量={}",
                    tenantId, event.getEventName(), clients.size());
            clients.forEach(clientId -> {
                if (userType != null){
                    Integer type = getUserType(clientId);
                    if (userType.equals(type)){
                        send(clientId, event);
                    }
                }else {
                    send(clientId, event);
                }


            } );
        } else {
            log.warn("租户下没有客户端: {}", tenantId);
        }
    }

    */
/**
     * 关闭指定客户端的连接
     *//*

    public static void complete(String clientId) {
        SseEmitter emitter = emitterMap.get(clientId);
        if (emitter != null) {
            emitter.complete();
            removeClient(clientId);
            log.info("关闭SSE连接: {}", clientId);
        }
    }

    */
/**
     * 获取当前连接数
     *//*

    public static int getConnectionCount() {
        return emitterMap.size();
    }

    */
/**
     * 获取指定租户的连接数
     *//*

    public static int getTenantConnectionCount(String tenantId) {
        Set<String> clients = tenantClientMap.get(tenantId);
        return clients != null ? clients.size() : 0;
    }
    public static Set<String> getAllClientIds() {
        return emitterMap.keySet();
    }

    public static String getShopUserId(String clientId) {
        return clientId.split(":")[0];
    }
    public static Integer getUserType(String clientId) {
        return Integer.parseInt(clientId.split(":")[1]);
    }
}
*/
