package com.medusa.gruul.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.platform.api.entity.AccountInfo;
import com.medusa.gruul.platform.api.model.vo.UserInfoVo;
import com.medusa.gruul.platform.model.dto.PlatformAccountInfoParamDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 平台用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-07
 */
@Mapper
public interface AccountInfoMapper extends BaseMapper<AccountInfo> {

    IPage<UserInfoVo> searchUserInfoVo(Page<UserInfoVo> userInfoVoPage, @Param("params") PlatformAccountInfoParamDto paramDto);
}
