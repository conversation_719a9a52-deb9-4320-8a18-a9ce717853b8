package com.medusa.gruul.platform.web.remote;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.platform.api.entity.*;
import com.medusa.gruul.platform.api.model.dto.*;
import com.medusa.gruul.platform.api.model.vo.*;
import com.medusa.gruul.platform.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController(value = "remoteMiniInfoController")
@RequestMapping("/")
@Api(tags = "远程调用接口,仅限后端feign调用-->RemoteMiniInfoService")
public class RemoteMiniInfoController {

    @Autowired
    private ISystemConfService systemConfService;
    @Autowired
    private IPlatformShopInfoService platformShopInfoService;
    @Autowired
    private IAccountInfoService accountInfoService;
    @Autowired
    private ISpecialSettingService specialSettingService;
    @Autowired
    private IPlatformRenovationTemplateService platformRenovationTemplateService;
    @Autowired
    private IPlatformStoreFrontService platformStoreFrontService;
    @Autowired
    private IPlatformDepartmentService platformDepartmentService;
    @Autowired
    private IPlatformEmployeeService platformEmployeeService;
    @Autowired
    private RemoteGoodsService remoteGoodsService;

    @Autowired
    private IWxMessageTemplateService wxMessageTemplateService;
    /**
     * 获取店铺信息
     *
     * @return com.medusa.gruul.platform.api.model.dto.ShopInfoDto
     */
    @GetMapping(value = "/get/shop/info")
    @EscapeLogin
    @ApiOperation(value = "获取店铺信息")
    public Result<ShopInfoDto> getShopInfo() {
        return platformShopInfoService.getShopInfo();
    }

    @GetMapping("/affirm/lessee/{token}")
    @ApiOperation(value = "确认是否商家主账号")
    @EscapeLogin
    public Result<Boolean> affirmLessee(@PathVariable(value = "token") String token) {
        return Result.ok(accountInfoService.affirmLessee(token));
    }

    @GetMapping("/oss/config")
    @EscapeLogin
    @ApiOperation(value = "获取当前平台使用oss配置")
    public Result<OssConfigDto> currentOssConfig() {
        return systemConfService.currentOssConfig();
    }

    @GetMapping("/shop/config")
    @EscapeLogin
    @ApiOperation(value = "获取店铺配置信息(小程序信息,支付配置)")
    public ShopConfigDto getShopConfig() {
        return platformShopInfoService.getShopConfig();
    }

    @GetMapping("/shop/config/appid")
    @EscapeLogin
    @ApiOperation(value = "根据appid获取店铺配置信息(小程序信息,支付配置)")
    public ShopConfigDto getShopConfigAndAppId(@RequestParam(value = "appId", required = true) String appId) {
        return platformShopInfoService.getShopConfigAndAppId(appId);
    }

    /**
     * 获取店铺当前使用的套餐功能状态
     * <p>
     * code == 200 返回正确数据
     *
     * @return com.medusa.gruul.platform.api.model.dto.ShopInfoDto
     */
    @GetMapping(value = "/get/shop/function")
    @EscapeLogin
    @ApiOperation(value = "获取店铺当前使用的套餐功能状态,当前仅限拼团模板数据")
    public Result<ShopPackageFunctionDto> getShopFunction() {
        return platformShopInfoService.getShopFunction();
    }

    @GetMapping("/shop/config/mini")
    @EscapeLogin
    @ApiOperation(value = "获取店铺配置小程序信息信息")
    public MiniInfoVo getShopConfigMini() {
        return platformShopInfoService.miniInfo(CommonConstants.NUMBER_THREE, "");
    }

    @GetMapping("/oss/all/config")
    @EscapeLogin
    @ApiOperation(value = "获取所有oss配置")
    public Result<List<OssConfigDto>> allOssConfig() {
        return systemConfService.allOssConfig();
    }

    @GetMapping("/accountInfo/exist")
    @EscapeLogin
    @ApiOperation(value = "根据店铺id查看用户是否存在")
    public Result<Boolean> userIsExistByShopId(@RequestParam(value = "shopId", required = true)String shopId){
        LambdaQueryWrapper<AccountInfo>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccountInfo::getShopId,shopId);
        List<AccountInfo> list = accountInfoService.list(wrapper);
        Boolean b= false;
        if(list!=null&&list.size()>0){
            b = true;
        }
        return Result.ok(b);
    }

    @GetMapping("/renovation-template/all")
    @EscapeLogin
    @ApiOperation(value = "获取所有平台装修模板")
    public Result<List<PlatformRenovationTemplate>> getAllRenovationTemplate() {
        LambdaQueryWrapper<PlatformRenovationTemplate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByAsc(PlatformRenovationTemplate::getName);
        return Result.ok(platformRenovationTemplateService.list(lambdaQueryWrapper));
    }

    @GetMapping("/renovation-template/all-info/id")
    @EscapeLogin
    @ApiOperation(value = "根据模板id获取装修模板相关的所有信息")
    public Result<PlatformRenovationTemplateAllVo> getRenovationTemplateAllInfoById(@RequestParam(value = "templateId", required = true)Long templateId){
        PlatformRenovationTemplateAllVo infoVo = platformRenovationTemplateService.queryRelatedById(templateId);
        return Result.ok(infoVo);
    }
    @GetMapping("/get/account/registrationId")
    @EscapeLogin
    @ApiOperation(value = "获取含有设备id的用户")
    public Result<List<AccountInfo>> getAccountRegistrationId(@RequestParam(value = "mobileType", required = true)String mobileType) {
        List<AccountInfo> accountInfoList = accountInfoService.getAccountRegistrationId(mobileType);
        return Result.ok(accountInfoList);
    }

    /**
     * 获取店铺特殊配置
     * @return
     */
    @GetMapping("/get/special/setting")
    @EscapeLogin
    @ApiOperation(value = "获取店铺特殊配置")
    List<SpecialSetting>getSpecialSetting(){
        List<SpecialSetting> list = specialSettingService.list();
        return list;
    }

    @GetMapping("/get/special/setting/shopIds")
    @EscapeLogin
    @ApiOperation(value = "获取允许平台查看订单的店铺id")
    List<String>getSpecialSettingShopIds(@RequestParam(value = "shopId", required = true)String shopId){
        return specialSettingService.getSpecialSettingShopIds(shopId);
    }

    @GetMapping("/get/special/setting/byShopId")
    @EscapeLogin
    @ApiOperation(value = "通过店铺id获取特殊配置")
    List<SpecialSetting> getSpecialSettingByShopId(@RequestParam(value = "shopId", required = true)String shopId){
        return specialSettingService.getSpecialSettingByShopId(shopId);
    }

    @GetMapping("/get/department/storeFrontId")
    @EscapeLogin
    @ApiOperation(value = "通过门店id获取部门信息")
    StoreFrontOrderVo getPlatformDepartmentByStoreFrontId(@RequestParam(value = "storeFrontId", required = true)String storeFrontId){
        PlatformStoreFront platformStoreFront = platformStoreFrontService.getById(storeFrontId);
        String departmentCode = platformStoreFront.getDepartmentCode();
        String employeeId = platformStoreFront.getEmployeeId();
        LambdaQueryWrapper<PlatformDepartment>departmentLambdaQueryWrapper = new LambdaQueryWrapper<>();
        departmentLambdaQueryWrapper.eq(PlatformDepartment::getClassCode,departmentCode);
        PlatformDepartment platformDepartment = platformDepartmentService.getOne(departmentLambdaQueryWrapper);
        LambdaQueryWrapper<PlatformEmployee>employeeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        employeeLambdaQueryWrapper.eq(PlatformEmployee::getOutId,employeeId);
        PlatformEmployee platformEmployee = platformEmployeeService.getOne(employeeLambdaQueryWrapper);
        StoreFrontOrderVo storeFrontOrderVo = new StoreFrontOrderVo();
        storeFrontOrderVo.setId(platformStoreFront.getId());
        if(null != platformEmployee){
            storeFrontOrderVo.setEmployeeId(platformEmployee.getOutId());
            storeFrontOrderVo.setEmployeeName(platformEmployee.getEmpFullName());
        }
        if(null != platformDepartment){
            storeFrontOrderVo.setDepartmentName(platformDepartment.getDeptFullName());
            storeFrontOrderVo.setDepartmentCode(platformDepartment.getClassCode());
        }
        storeFrontOrderVo.setStoreFrontId(platformStoreFront.getId() + "");
        storeFrontOrderVo.setStoreFrontCode(platformStoreFront.getClassCode());
        storeFrontOrderVo.setIsCatalog(platformStoreFront.getIsCatalog());
        storeFrontOrderVo.setStoreFrontAddress(platformStoreFront.getAddress());
        storeFrontOrderVo.setStoreFrontName(platformStoreFront.getStoreFullName());
        return storeFrontOrderVo;
    }


    @GetMapping("/get/department/account")
    @EscapeLogin
    @ApiOperation(value = "根据平台用户id获取关联部门，职员")
    StoreFrontOrderVo getDepartmentByAccountId(@RequestParam(value = "accountId", required = true)String accountId){
        AccountInfo accountInfo = accountInfoService.getById(accountId);
        String employeeId = accountInfo.getEmployeeId();
        StoreFrontOrderVo storeFrontOrderVo = new StoreFrontOrderVo();
        if(StringUtil.isNotEmpty(employeeId)){
            LambdaQueryWrapper<PlatformEmployee>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PlatformEmployee::getOutId,employeeId);
            PlatformEmployee platformEmployee = platformEmployeeService.getOne(wrapper);
            if(platformEmployee!=null){
                storeFrontOrderVo.setEmployeeName(platformEmployee.getEmpFullName());
                storeFrontOrderVo.setEmployeeId(platformEmployee.getOutId());
                storeFrontOrderVo.setStockCode(platformEmployee.getStockCode());
                storeFrontOrderVo.setStoreFrontId(platformEmployee.getStoreFrontId());
                storeFrontOrderVo.setStoreFrontCode(platformEmployee.getStoreFrontCode());
                if(StringUtil.isNotEmpty(platformEmployee.getDepartmentCode())){
                    LambdaQueryWrapper<PlatformDepartment>queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(PlatformDepartment::getClassCode,platformEmployee.getDepartmentCode());
                    List<PlatformDepartment> list = platformDepartmentService.list(queryWrapper);
                    if(list!=null&&list.size()>0){
                        PlatformDepartment platformDepartment = list.get(0);
                        storeFrontOrderVo.setDepartmentCode(platformDepartment.getClassCode());
                        storeFrontOrderVo.setDepartmentName(platformDepartment.getDeptFullName());
                    }
                }

            }
        }
        return storeFrontOrderVo;
    }

    @GetMapping("/get/storeFront/classCode")
    @EscapeLogin
    @ApiOperation(value = "通过门店编码获取门店信息")
    StoreFrontOrderVo getStoreFrontByClassCode(@RequestParam(value = "classCode", required = true)String classCode){
        StoreFrontOrderVo storeFrontOrderVo = new StoreFrontOrderVo();
        LambdaQueryWrapper<PlatformStoreFront>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformStoreFront::getClassCode,classCode);
        PlatformStoreFront platformStoreFront = platformStoreFrontService.getOne(wrapper);
        // 门店信息不再直接绑定职员、部门，取消查询这些信息
        /*String departmentCode = platformStoreFront.getDepartmentCode();
        String employeeId = platformStoreFront.getEmployeeId();
        LambdaQueryWrapper<PlatformDepartment>departmentLambdaQueryWrapper = new LambdaQueryWrapper<>();
        departmentLambdaQueryWrapper.eq(PlatformDepartment::getClassCode,departmentCode);
        PlatformDepartment platformDepartment = platformDepartmentService.getOne(departmentLambdaQueryWrapper);
        LambdaQueryWrapper<PlatformEmployee>employeeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        employeeLambdaQueryWrapper.eq(PlatformEmployee::getOutId,employeeId);
        PlatformEmployee platformEmployee = platformEmployeeService.getOne(employeeLambdaQueryWrapper);*/
        storeFrontOrderVo.setId(platformStoreFront.getId());
        /*storeFrontOrderVo.setEmployeeId(platformEmployee.getOutId());
        storeFrontOrderVo.setEmployeeName(platformEmployee.getEmpFullName());
        storeFrontOrderVo.setDepartmentName(platformDepartment.getDeptFullName());
        storeFrontOrderVo.setDepartmentCode(platformDepartment.getClassCode());*/
        storeFrontOrderVo.setStoreFrontCode(platformStoreFront.getClassCode());
        storeFrontOrderVo.setIsCatalog(platformStoreFront.getIsCatalog());
        return storeFrontOrderVo;
    }

    @GetMapping("/get/relationInfo/miniAccountId")
    @EscapeLogin
    @ApiOperation(value = "通过小程序会员id获取关联信息")
    RelationInfoVo getRelationInfoByMiniAccountId(@RequestParam(value = "userId", required = true)String userId){
        RelationInfoVo relationInfoVo = accountInfoService.getRelationInfoByMiniAccountId(userId);
        return relationInfoVo;
    }

    @GetMapping("/get/relationInfo/default/department")
    @EscapeLogin
    @ApiOperation(value = "获取默认部门关系信息")
    RelationInfoVo getRelationInfoDefaultDepartment(){
        RelationInfoVo relationInfoVo = accountInfoService.getRelationInfoDefaultDepartment();
        return relationInfoVo;
    }

    @GetMapping("/get/relationInfo/storeFrontId")
    @EscapeLogin
    @ApiOperation(value = "通过门店id获取关联信息")
    RelationInfoVo getRelationInfoByStoreFrontId(@RequestParam(value = "storeFrontId", required = true)String storeFrontId){
        RelationInfoVo relationInfoVo = accountInfoService.getRelationInfoByStoreFrontId(storeFrontId);
        return relationInfoVo;
    }

    @GetMapping("/get/relationInfo/accountId")
    @EscapeLogin
    @ApiOperation(value = "通过用户id获取关联信息")
    RelationInfoVo getRelationInfoByAccountId(@RequestParam(value = "accountId", required = true)String accountId){
        RelationInfoVo relationInfoVo = accountInfoService.getRelationInfoByAccountId(accountId);
        return relationInfoVo;
    }

    @GetMapping("/get/wxMessageTemplate/code")
    @EscapeLogin
    @ApiOperation(value = "通过标识获取微信消息模板信息")
    WxMessageTemplateVo getWxMessageTemplateByCode(@RequestParam(value = "code", required = true)String code){
        WxMessageTemplateVo wxMessageTemplateVo = wxMessageTemplateService.getWxMessageTemplateByCode(code);
        return wxMessageTemplateVo;
    }

    /**
     * 获取租户小程序配置
     * @param tenantId
     * @return
     */
    @GetMapping("/get/MiniInfoVo/tenantId")
    @EscapeLogin
    @ApiOperation(value = "获取租户小程序配置")
    MiniInfoVo getMiniInfoVoByTenantId(@RequestParam(value = "tenantId", required = true)String tenantId){
        TenantContextHolder.setTenantId(tenantId);
        return platformShopInfoService.miniInfo(CommonConstants.NUMBER_THREE, "");
    }

    @GetMapping("/get/AccountInfo/userId")
    @ApiOperation(value = "根据用户id获取平台用户信息")
    @EscapeLogin
    AccountInfo getAccountInfo(@RequestParam(value = "userId", required = true)String userId){
        AccountInfo accountInfo = accountInfoService.getById(userId);
        return accountInfo;

    }

    @GetMapping("/get/accountInfo/userId")
    @EscapeLogin
    @ApiOperation(value = "根据userId获取平台用户信息")
    AccountInfo getAccountInfoByUserId(@RequestParam(value = "userId", required = true)String userId){
        AccountInfo accountInfo = accountInfoService.getById(userId);
        return accountInfo;
    }
}
