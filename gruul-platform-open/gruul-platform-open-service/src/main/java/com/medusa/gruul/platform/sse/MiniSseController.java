/*
package com.medusa.gruul.platform.sse;

import com.medusa.gruul.common.core.annotation.EscapeLogin;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.dto.SseEventDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

*/
/**
 * SSE推送控制器
 *
 * <AUTHOR>
 *//*

@Slf4j
@RestController
@RequestMapping("/sse")
@Api(tags = "SSE实时推送接口")
public class MiniSseController {


    @GetMapping(value = "/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("建立SSE连接")
    public SseEmitter connect(@RequestParam String clientId,@RequestParam(required = false) String tenantId) {
        if (StringUtil.isNotBlank(clientId) && StringUtil.isNotBlank(tenantId)){
            return SseUtil.create(clientId, tenantId);
        }else {
            return SseUtil.create();
        }
    }
    @EscapeLogin
    @GetMapping("/close")
    @ApiOperation("关闭SSE连接")
    public Result<String> close(@RequestParam String clientId) {
        try {
            SseUtil.complete(clientId);
            return Result.ok("连接已关闭");
        } catch (Exception e) {
            log.error("关闭连接失败", e);
            return Result.failed("关闭连接失败: " + e.getMessage());
        }
    }

    @PostMapping("/publishAll")
    @ApiOperation("广播消息给所有客户端")
    public Result<String> publishAll(@RequestBody SseEventDto event) {
        try {
            SseUtil.publish(event);
            return Result.ok("广播发送成功");
        } catch (Exception e) {
            log.error("广播消息失败", e);
            return Result.failed("广播消息失败: " + e.getMessage());
        }
    }





   */
/* // 业务相关的推送方法
    @PostMapping("/payOkPublish")
    @ApiOperation("推送订单支付成功消息")
    public Result<String> payOkPublish(@RequestParam String userName,String totalAmount) {
        try {
            SseEventDto event = new SseEventDto("order_pay_success",
                    String.format("用户： %s 下单 %s 元", userName,totalAmount));
            String tenantId = TenantContextHolder.getTenantId();
            SseUtil.sendToTenant(tenantId, event);
            return Result.ok("订单支付成功消息推送完成");
        } catch (Exception e) {
            log.error("推送订单支付成功消息失败", e);
            return Result.failed("推送失败: " + e.getMessage());
        }
    }*//*

}*/
