package com.medusa.gruul.platform.sse.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.medusa.gruul.platform.api.constant.QueueNameConstant;
import com.medusa.gruul.platform.api.model.dto.SseEventDto;
import com.medusa.gruul.platform.sse.util.SseUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * SSE消息监听器
 * 接收其他微服务发送的SSE推送请求
 */
@Slf4j
@Component
public class SseMessageListener {

    @Autowired
    private SseUtil sseUtil;

    /**
     * 监听SSE消息推送队列
     *
     * @param sseEventDto SSE事件消息
     * @param properties 消息属性
     * @param channel 消息通道
     */
    @RabbitListener(queues = QueueNameConstant.PLATFORM_SSE_MESSAGE_SEND)
    public void handleSseMessage(SseEventDto sseEventDto, MessageProperties properties, Channel channel) throws IOException {
        log.info("接收到SSE消息推送请求: {}", JSON.toJSONString(sseEventDto));
        
        try {
            // 验证消息参数
            if (sseEventDto == null) {
                log.warn("SSE消息为空，忽略处理");
                channel.basicAck(properties.getDeliveryTag(), false);
                return;
            }

            if (StrUtil.isBlank(sseEventDto.getTenantId())) {
                log.warn("SSE消息租户ID为空，忽略处理: {}", JSON.toJSONString(sseEventDto));
                channel.basicAck(properties.getDeliveryTag(), false);
                return;
            }

            if (StrUtil.isBlank(sseEventDto.getEventType())) {
                log.warn("SSE消息事件类型为空，忽略处理: {}", JSON.toJSONString(sseEventDto));
                channel.basicAck(properties.getDeliveryTag(), false);
                return;
            }

            if (sseEventDto.getData() == null) {
                log.warn("SSE消息内容为空，忽略处理: {}", JSON.toJSONString(sseEventDto));
                channel.basicAck(properties.getDeliveryTag(), false);
                return;
            }



            int successCount = 0;

            // 根据目标客户端ID决定推送方式
            if (StrUtil.isNotBlank(sseEventDto.getTargetClientId())) {
                // 推送给指定客户端
                boolean success = sseUtil.sendToClient(sseEventDto);
                if (success) {
                    successCount = 1;
                }
                log.info("向指定客户端推送SSE消息: tenantId={}, clientId={}, success={}",
                        sseEventDto.getTenantId(), sseEventDto.getTargetClientId(), success);
            } else {
                // 推送给租户下所有客户端（可按登录类型过滤）
                successCount = sseUtil.sendToTenant(sseEventDto);
                log.info("向租户推送SSE消息: tenantId={}, loginType={}, successCount={}",
                        sseEventDto.getTenantId(), sseEventDto.getLoginType(), successCount);
            }

            // 记录推送结果
            if (successCount > 0) {
                log.info("SSE消息推送成功: eventType={}, tenantId={}, successCount={}", 
                        sseEventDto.getEventType(), sseEventDto.getTenantId(), successCount);
            } else {
                log.warn("SSE消息推送失败，没有找到目标客户端: eventType={}, tenantId={}, targetClientId={}, loginType={}", 
                        sseEventDto.getEventType(), sseEventDto.getTenantId(), 
                        sseEventDto.getTargetClientId(), sseEventDto.getLoginType());
            }

            // 手动确认消息
            channel.basicAck(properties.getDeliveryTag(), false);

        } catch (Exception e) {
            log.error("处理SSE消息推送失败: {}", e.getMessage(), e);
            
            // 根据错误类型决定是否重试
            if (isRetryableError(e)) {
                // 拒绝消息并重新入队
                channel.basicNack(properties.getDeliveryTag(), false, true);
                log.info("SSE消息处理失败，消息已重新入队: {}", JSON.toJSONString(sseEventDto));
            } else {
                // 确认消息，避免无限重试
                channel.basicAck(properties.getDeliveryTag(), false);
                log.warn("SSE消息处理失败，消息已丢弃: {}", JSON.toJSONString(sseEventDto));
            }
        }
    }

    /**
     * 判断是否为可重试的错误
     *
     * @param e 异常
     * @return 是否可重试
     */
    private boolean isRetryableError(Exception e) {
        // 网络相关异常可以重试
        if (e instanceof IOException) {
            return true;
        }
        
        // 临时性异常可以重试
        String message = e.getMessage();
        if (message != null) {
            message = message.toLowerCase();
            if (message.contains("timeout") || 
                message.contains("connection") || 
                message.contains("network") ||
                message.contains("temporary")) {
                return true;
            }
        }
        
        // 其他异常不重试
        return false;
    }
}
