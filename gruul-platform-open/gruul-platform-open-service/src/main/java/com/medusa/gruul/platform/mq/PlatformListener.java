package com.medusa.gruul.platform.mq;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.medusa.gruul.account.api.model.message.AccountCommissionCashMessage;
import com.medusa.gruul.common.core.util.Result;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.order.api.model.OrderDeliveryProxyMessage;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.platform.api.constant.QueueNameConstant;
import com.medusa.gruul.platform.api.entity.PlatformShopMessage;
import com.medusa.gruul.platform.api.model.dto.ReservationOrderCancelMessage;
import com.medusa.gruul.platform.api.model.dto.SubscribeMsgSendDto;
import com.medusa.gruul.platform.api.model.dto.WxSendMessageDto;
import com.medusa.gruul.platform.api.model.dto.MessageTemplateCopyDto;
import com.medusa.gruul.platform.service.*;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @description 平台监听队列
 * @data: 2020-02-02
 */
@Slf4j
@Component
public class PlatformListener {

    @Autowired
    private IPlatformShopMessageService platformShopMessageService;
    @Autowired
    private IPlatformShopInfoService platformShopInfoService;
    @Autowired
    private IWxMessageTemplateService wxMessageTemplateService;
    @Autowired
    private IServiceContentService iServiceContentService;
    @Autowired
    private IPlatformPositionService platformPositionService;
    @Autowired
    private IReservationOrderService reservationOrderService;
    /**
     * 公众号模板消息发送队列(只负责接收发送,不完全保证发送成功,只发送一次无论失败成功)
     * @param dto
     */
    @RabbitListener(queues = QueueNameConstant.PLATFORM_WX_MP_SEND_MESSAGE)
    public void wxMpSendMessageReceive(WxSendMessageDto dto){
        log.debug("receive message:" + JSON.toJSONString(dto));
        try {
            wxMessageTemplateService.wxMpSendMessage(dto);
        } catch (Exception e) {
            log.debug("模板消息发送队列错误 : {}  ", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 微信订阅消息发送队列(只负责接收发送,不完全保证发送成功,只发送一次无论失败成功)
     * @param dto
     */
    @RabbitListener(queues = QueueNameConstant.PLATFORM_WX_SEND_MESSAGE)
    public void wxSendMessageReceive(WxSendMessageDto dto){
        log.debug("receive message:" + JSON.toJSONString(dto));
        try {
            wxMessageTemplateService.wxSendMessage(dto);
        } catch (Exception e) {
            log.debug("订阅消息发送队列错误 : {}  ", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 订阅消息发送队列(只负责接收发送,不完全保证发送成功,只发送一次无论失败成功)
     *
     * @param msgSendDto com.medusa.gruul.platform.api.model.dto.SubscribeMsgSendDto
     */
    @RabbitListener(queues = QueueNameConstant.PLATFORM_SUBSCRIBE_MSG_SEND)
    public void subscribeMsgSendReceive(SubscribeMsgSendDto msgSendDto) {
        log.debug("receive message:" + JSON.toJSONString(msgSendDto));
        try {
            platformShopMessageService.subscribeMsgSend(msgSendDto);
        } catch (Exception e) {
            log.debug("订阅消息发送队列错误 : {}  ", e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 套餐延迟队列
     *
     * @param platformInfoId java.lang.Integer
     */
    @RabbitListener(queues = QueueNameConstant.PLATFORM_PACKAGE_DUE)
    public void packageDueReceive(Integer platformInfoId) {
        log.debug("receive message:" + platformInfoId);
        try {
            platformShopInfoService.packageDueReceive(platformInfoId);
        } catch (Exception e) {
            log.debug("套餐延迟队列错误 : {}  ", e.getMessage());
            e.printStackTrace();
        }

    }


    /**
     * 发送发货消息队列
     * @param orderVo
     * @param properties
     * @param channel
     */
    @RabbitListener(queues = QueueNameConstant.PLATFORM_SAVE_SHOP_MESSAGE)
    public void saveShopMessage(OrderVo orderVo, MessageProperties properties, Channel channel) throws IOException {
        log.info("saveShopMessage receive message:" + orderVo);
        if (ObjectUtil.isNull(orderVo)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        platformShopMessageService.saveDeliverOrderMessage(orderVo);
        //手动确认
        channel.basicAck(properties.getDeliveryTag(), true);

    }

    @RabbitListener(queues = QueueNameConstant.PLATFORM_BATCH_SAVE_SHOP_MESSAGE)
    public void batchSaveShopMessage(OrderVo orderVo, MessageProperties properties, Channel channel) throws IOException {
        log.info("batchSaveShopMessage receive message:" + orderVo);
        if (ObjectUtil.isNull(orderVo)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        platformShopMessageService.saveDeliverOrderMessage(orderVo);
        //手动确认
        channel.basicAck(properties.getDeliveryTag(), true);
    }

    /**
     *
     * @param orderVo
     * @param properties
     * @param channel
     * @throws IOException
     */
    @RabbitListener(queues = QueueNameConstant.PLATFORM_MESSAGE_READ_ALREADY)
    public void updateMessageStatus(OrderVo orderVo, MessageProperties properties, Channel channel) throws IOException {
        log.info("updateMessageStatus receive message:" + orderVo);
        if (ObjectUtil.isNull(orderVo)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        LambdaQueryWrapper<PlatformShopMessage>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlatformShopMessage::getOrderId,orderVo.getId());
        List<PlatformShopMessage> list = platformShopMessageService.list(wrapper);
        if(list!=null&&list.size()>0){
            for (PlatformShopMessage platformShopMessage : list) {
                platformShopMessageService.updateStatus(platformShopMessage.getId());
            }
        }
        //手动确认
        channel.basicAck(properties.getDeliveryTag(), true);

    }

    @RabbitListener(queues = QueueNameConstant.PLATFORM_BATCH_SAVE_DELIVERY_PROXY_MESSAGE)
    public void batchSaveDeliveryProxyMessage(OrderDeliveryProxyMessage message, MessageProperties properties, Channel channel) throws IOException {
        log.info("batchSaveDeliveryProxyMessage receive message:" + message);
        if (ObjectUtil.isNull(message)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        platformShopMessageService.saveDeliverProxyOrderMessage(message);
        //手动确认
        channel.basicAck(properties.getDeliveryTag(), true);
    }

    @RabbitListener(queues = QueueNameConstant.PLATFORM_BATCH_SAVE_COMMISSION_CASH_MESSAGE)
    public void batchSaveCommissionCashMessage(AccountCommissionCashMessage message, MessageProperties properties, Channel channel) throws IOException {
        log.info("batchSaveCommissionCashMessage receive message:" + message);
        if (ObjectUtil.isNull(message)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        platformShopMessageService.batchSaveCommissionCashMessage(message);
        //手动确认
        channel.basicAck(properties.getDeliveryTag(), true);
    }

    @Transactional(rollbackFor = Exception.class)
    @RabbitListener(queues = QueueNameConstant.PLATFORM_BATCH_COPY_TEMPLATE_MESSAGE)
    public void batchCopyTemplate(MessageTemplateCopyDto message, MessageProperties properties, Channel channel) throws IOException {
        log.info("batchCopyTemplate receive message:" + message);
        if (ObjectUtil.isNull(message)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }

        log.info("开始复制商家职位、服务项目模板");
        iServiceContentService.copyFromTemplate(message);
        platformPositionService.copyFromTemplate(message);
        log.info("复制商家职位、服务项目模板成功");
        //手动确认
        channel.basicAck(properties.getDeliveryTag(), true);
    }



    @RabbitListener(queues = QueueNameConstant.PLATFORM_CANCEL_RESERVATION_ORDER_MESSAGE)
    public void cancelReservationOrderMessage(ReservationOrderCancelMessage message, MessageProperties properties, Channel channel) throws IOException {
        log.info("cancelReservationOrderMessage receive message:" + message);
        if (ObjectUtil.isNull(message)) {
            //手动确认
            channel.basicAck(properties.getDeliveryTag(), true);
            return;
        }
        TenantContextHolder.setTenantId(message.getTenantId());
        ShopContextHolder.setShopId(message.getShopId());
        Result<String> stringResult = reservationOrderService.cancelReservation(message.getOrderId(),true);
        if (SystemCode.SUCCESS_CODE == stringResult.getCode()){
            log.info("取消预约成功 orderId:{}", message.getOrderId());
        }else {
            log.info("取消预约失败 orderId:{},失败原因:{}", message.getOrderId(), stringResult.getMsg());
        }

        channel.basicAck(properties.getDeliveryTag(), true);
    }
}
