<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.WxMessageTemplateMapper">
    <resultMap id="WxMessageTemplateMap" type="com.medusa.gruul.platform.api.model.vo.WxMessageTemplateVo">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="template_id" property="templateId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
    </resultMap>
    <select id="getWxMessageTemplate" resultMap="WxMessageTemplateMap">
        SELECT
            t1.id,
            t1.code,
            t1.template_id,
            t1.name,
            t1.type,
            t1.status
        FROM
            t_wx_message_template t1
        where
            t1.is_deleted = 0
        <if test="param.code!=null and param.code!=''">
            and  t1.code LIKE CONCAT('%',#{param.code},'%')
        </if>
        <if test="param.name!=null and param.name!=''">
            and  t1.name LIKE CONCAT('%',#{param.name},'%')
        </if>
        <if test="param.type!=null ">
            and  t1.type = #{param.type}
        </if>
        <if test="param.status!=null ">
            and  t1.status = #{param.status}
        </if>
        order by t1.create_time desc
    </select>
</mapper>
