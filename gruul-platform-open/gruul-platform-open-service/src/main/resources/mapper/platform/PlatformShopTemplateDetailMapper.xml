<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.PlatformShopTemplateDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.entity.PlatformShopTemplateDetail">
        <id column="id" property="id"/>
        <result column="is_deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="version" property="version"/>
        <result column="libraries_info_id" property="librariesInfoId"/>
        <result column="libraries_info_name" property="librariesInfoName"/>
        <result column="pc_termina_url" property="pcTerminaUrl"/>
        <result column="pc_termina_version" property="pcTerminaVersion"/>
        <result column="mobile_terminal_url" property="mobileTerminalUrl"/>
        <result column="mobile_terminal_version" property="mobileTerminalVersion"/>
        <result column="server_count" property="serverCount"/>
        <result column="shop_template_id" property="shopTemplateId"/>
        <result column="version_log" property="versionLog"/>
        <result column="pc_url_map" property="pcUrlMap"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_deleted,
        create_time,
        update_time,version_log,pc_url_map,
        id, version, libraries_info_id, libraries_info_name,  pc_termina_url,
        pc_termina_version, mobile_terminal_url, mobile_terminal_version, server_count, shop_template_id
    </sql>
    <select id="selectShopTeamplteNewDetail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_platform_shop_template_detail
        where id = ( SELECT max( id ) FROM t_platform_shop_template_detail WHERE is_deleted = 0 and shop_template_id
        =#{shopTemplateId})
    </select>
    <select id="selectByFilterById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_platform_shop_template_detail
        where id = #{versionId}
    </select>


</mapper>
