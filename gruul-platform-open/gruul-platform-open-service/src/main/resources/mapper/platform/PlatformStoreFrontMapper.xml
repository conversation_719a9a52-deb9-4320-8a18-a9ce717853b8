<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.PlatformStoreFrontMapper">
    <!--平台职员信息结果集 -->
    <resultMap id="StoreFrontVoMap" type="com.medusa.gruul.platform.api.model.vo.StoreFrontVo">
        <id column="id" property="id"/>
        <result column="class_code" property="classCode"/>
        <result column="store_number" property="storeNumber"/>
        <result column="store_full_name" property="storeFullName"/>
        <result column="store_area" property="storeArea"/>
        <result column="department_id" property="departmentId"/>
        <result column="department_name" property="departmentName"/>
        <result column="employee_id" property="employeeId"/>
        <result column="employee_name" property="employeeName"/>
        <result column="stock_id" property="stockId"/>
        <result column="stock_name" property="stockName"/>
        <result column="status_id" property="statusId"/>
        <result column="map_x" property="mapX"/>
        <result column="map_y" property="mapY"/>
        <result column="address" property="address"/>
        <result column="account_id" property="accountId"/>
        <result column="account_name" property="accountName"/>
        <result column="stock_code" property="stockCode"/>
        <result column="is_Catalog" property="isCatalog"/>
        <result column="account_id" property="accountId"/>
        <result column="account_name" property="accountName"/>
    </resultMap>
    <select id="searchStoreFront" resultMap="StoreFrontVoMap">
        select
               t1.id,t1.class_code,t1.store_number,t1.store_full_name,t1.store_area,
               t1.department_id,t3.dept_full_name as department_name,t1.employee_id,t2.emp_full_name as employee_name,
               t1.stock_id,t4.warehouse_full_name as stock_name,t1.status_id,t1.stock_code, t1.is_Catalog,t1.account_id,t1.account_name
        from t_platform_lyd_store_front t1
        left join t_platform_lyd_employee t2 on t2.out_id = t1.employee_id
        left join t_platform_lyd_department t3 on t3.class_code = t1.department_code
        left join t_warehouse t4 on t4.class_code = t1.stock_code and t1.stock_code != ''
        where t1.is_deleted = 0
        <if test="params.firstClassFlag!=null and params.firstClassFlag!='' and params.firstClassFlag == 1 ">
            and  t1.class_code LIKE CONCAT('','','_____')
        </if>
        <if test="params.parentClassCode!=null and params.parentClassCode!=''">
            and  t1.class_code LIKE CONCAT('',#{params.parentClassCode},'_____')
        </if>
        <if test="params.storeNumber!=null and params.storeNumber!=''">
            and  t1.store_number LIKE CONCAT('%',#{params.storeNumber},'%')
        </if>
        <if test="params.storeFullName!=null and params.storeFullName!=''">
            and  t1.store_full_name LIKE CONCAT('%',#{params.storeFullName},'%')
        </if>
        <if test="params.storeArea!=null and params.storeArea!=''">
            and  t1.store_area LIKE CONCAT('%',#{params.storeArea},'%')
        </if>
        <if test="params.departmentName!=null and params.departmentName!=''">
            and  t3.dept_full_name LIKE CONCAT('%',#{params.departmentName},'%')
        </if>
        <if test="params.employeeName!=null and params.employeeName!=''">
            and  t2.emp_full_name LIKE CONCAT('%',#{params.employeeName},'%')
        </if>
        <if test="params.stockName!=null and params.stockName!=''">
            and  t4.warehouse_full_name LIKE CONCAT('%',#{params.stockName},'%')
        </if>
        <if test="params.isCatalog!=null and params.isCatalog!=''">
            and  t1.is_catalog = #{params.isCatalog}
        </if>
    </select>
    <select id="getStoreFrontVoByAccountId" resultMap="StoreFrontVoMap">
        select
            t1.id,t1.store_number,t1.store_full_name,t1.store_area,
            t1.department_id,t1.department_name,t1.employee_id,t1.employee_name,
            t1.stock_id,t1.stock_name,t1.status_id,t1.address,t1.map_x,t1.map_y,
            t2.id as account_id,t2.nike_name as account_name, t1.is_Catalog,t1.account_id,t1.account_name
        from
            t_platform_lyd_store_front t1
            left join t_platform_account_info t2 on t1.class_code = t2.store_front_code
        where
            t1.is_deleted = 0
          and t2.id = #{accountId}
    </select>
    <select id="getMaxCode" resultType="java.lang.String">
        select
            substring(MAX(t1.class_code),length(#{parentCode})+1,5)
        from
            t_platform_lyd_store_front t1
        where
            t1.class_code like concat(#{parentCode},'_____')
    </select>
    <select id="getLastStoreNumber" resultType="java.lang.String">
        SELECT
            max(
                    RIGHT ( concat( '                ', t1.store_number ), 20 ))
        FROM
            t_platform_lyd_store_front t1
        WHERE
            t1.store_number REGEXP '[0-9]'
	AND (
		t1.class_code IS NULL
		OR length ( t1.class_code )= 0
	OR length ( t1.class_code )= 5
	)
    </select>

    <select id="getLastStoreNumberByParentCode" resultType="java.lang.String">
        SELECT
            max(
                    RIGHT ( concat( '                ', t1.store_number ), 20 ))
        FROM
            t_platform_lyd_store_front t1
        WHERE
            t1.store_number REGEXP '[0-9]'
	AND  t1.class_code like concat(#{parentCode},'_____')
    </select>
    <select id="getNextCount" resultType="java.lang.Integer">
        select
            count(*)
        from
            t_platform_lyd_store_front t1
        where
            t1.is_deleted = 0 and t1.class_code LIKE CONCAT('',#{parentCode},'_____')
    </select>
</mapper>
