<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.AuthMenuInfoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.model.vo.AuthMenuInfoVo">
        <id column="id" property="id"/>
        <result column="menu_name" property="menuName"/>
        <result column="menu_code" property="menuCode"/>
        <result column="menu_pid" property="menuPid"/>
        <result column="auth_MenuInfo_id" property="authMenuInfoId"/>
        <result column="level" property="level"/>
        <collection property="authMenuInfoSecondVos" ofType="com.medusa.gruul.platform.api.model.vo.AuthMenuInfoSecondVo"
                    column="id" select="queryAuthMenuInfoSecond"></collection>
    </resultMap>

    <resultMap id="SecondResultMap" type="com.medusa.gruul.platform.api.model.vo.AuthMenuInfoSecondVo">
        <id column="id" property="id"/>
        <result column="menu_pid" property="menuPid"/>
        <result column="menu_name" property="menuName"/>
        <result column="menu_code" property="menuCode"/>
        <result column="auth_MenuInfo_id" property="authMenuInfoId"/>
        <result column="level" property="level"/>
        <collection property="authMenuButtonVos" ofType="com.medusa.gruul.platform.api.model.vo.AuthMenuButtonVo"
                    column="id" select="getAuthMenuButtonVo"></collection>
    </resultMap>

    <resultMap id="AuthMenuButtonVoMap" type="com.medusa.gruul.platform.api.model.vo.AuthMenuButtonVo">
        <id column="id" property="id"/>
        <result column="button_name" property="buttonName"/>
        <result column="button_code" property="buttonCode"/>
        <result column="menu_id" property="menuId"/>
        <result column="menu_name" property="menuName"/>
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id, a.menu_name, a.menu_pid, a.menu_code, a.id as auth_MenuInfo_id, a.level
    </sql>
    <sql id="Second_Column_List">
        a.id, a.menu_name, a.menu_pid, a.menu_code, a.id as auth_MenuInfo_id, a.level
    </sql>
    <sql id="Button_Column_List">
        t1.id,t1.button_name,t1.button_code,t1.menu_id,t2.menu_name
    </sql>
    <select id="getAuthMenuButtonVo" resultMap="AuthMenuButtonVoMap">
        select
        <include refid="Button_Column_List"/>
        from
            t_auth_menu_button t1
            left join t_auth_menu_info t2
            on t1.menu_id = t2.id and t2.is_deleted = 0
        where
            t1.is_deleted = 0
          and t1.menu_id = #{menuId}
    </select>
    <select id="queryAuthMenuInfoSecond" resultMap="SecondResultMap">
        select
        <include refid="Second_Column_List"/>
        from t_auth_menu_info a
        where a.menu_pid = #{id}
        and a.is_deleted = 0
        order by a.menu_sort asc
    </select>
    <select id="queryAllAuthMenuInfoList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_auth_menu_info a
        where a.menu_pid = 0
        and a.is_deleted = 0
        order by a.menu_sort asc
    </select>

</mapper>
