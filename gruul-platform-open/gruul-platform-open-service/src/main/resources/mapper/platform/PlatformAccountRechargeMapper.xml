<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.PlatformAccountRechargeMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.entity.PlatformAccountRecharge">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="account_id" property="accountId"/>
        <result column="recharge_num" property="rechargeNum"/>
        <result column="pay_num" property="payNum"/>
        <result column="pay_type" property="payType"/>
        <result column="finish_time" property="finishTime"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="pay_info" property="payInfo"/>
        <result column="account_amount" property="accountAmount"/>
        <result column="status" property="status"/>
        <result column="pay_source" property="paySource"/>
        <result column="prepay_id" property="prepayId"/>
        <result column="audit_time" property="auditTime"/>
        <result column="invoice_status" property="invoiceStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        is_deleted,
        update_time,
        id,
        account_id,
        recharge_num,
        pay_num,
        pay_type,
        finish_time,
        pay_amount,
        pay_info,
        account_amount,
        status,
        pay_source,
        prepay_id,
        audit_time,invoice_status
    </sql>


</mapper>
