<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.AccountInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.entity.AccountInfo">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="forbid_status" property="forbidStatus"/>
        <result column="bind_mini_id" property="bindMiniId"/>
        <result column="subject_id" property="subjectId"/>
        <result column="password" property="password"/>
        <result column="passwd" property="passwd"/>
        <result column="salt" property="salt"/>
        <result column="city" property="city"/>
        <result column="language" property="language"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="access_token" property="accessToken"/>
        <result column="refresh_token" property="refreshToken"/>
        <result column="nike_name" property="nikeName"/>
        <result column="avatar_url" property="avatarUrl"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="gender" property="gender"/>
        <result column="open_id" property="openId"/>
        <result column="union_id" property="unionId"/>
        <result column="province" property="province"/>
        <result column="country" property="country"/>
        <result column="privilege" property="privilege"/>
        <result column="ref_expires_time" property="refExpiresTime"/>
        <result column="access_expires_time" property="accessExpiresTime"/>
        <result column="account_type" property="accountType"/>
        <result column="region" property="region"/>
        <result column="address" property="address"/>
        <result column="bind_mini_shop_id" property="bindMiniShopId"/>
        <result column="balance" property="balance"/>
        <result column="agent_id" property="agentId"/>
        <result column="comment_text" property="commentText"/>
        <result column="me_agent_id" property="meAgentId"/>
    </resultMap>
    <!--平台用户信息结果集 -->
    <resultMap id="UserInfoVoMap" type="com.medusa.gruul.platform.api.model.vo.UserInfoVo">
        <id column="id" property="id"/>
        <result column="forbid_status" property="forbidStatus"/>
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="passwd" property="passwd"/>
        <result column="shop_name" property="shopName"/>
        <result column="shop_id" property="shopId"/>
        <result column="employee_id" property="employeeId"/>
        <result column="employee_name" property="employeeName"/>
        <result column="mini_account_id" property="miniAccountId"/>
        <result column="mini_account_name" property="miniAccountName"/>
        <result column="store_front_code" property="storeFrontCode"/>
        <result column="store_front_name" property="storeFrontName"/>
        <result column="department_code" property="departmentCode"/>
        <result column="department_name" property="departmentName"/>
        <result column="stock_code" property="stockCode"/>
        <result column="stock_name" property="stockName"/>
        <collection property="roleVos" ofType="com.medusa.gruul.platform.api.model.vo.UserRoleVo"
                    column="id" select="queryUserRoleVo"></collection>
    </resultMap>
    <!--平台用户信息关联角色结果集 -->
    <resultMap id="UserRoleVoMap" type="com.medusa.gruul.platform.api.model.vo.UserRoleVo">
        <result column="role_id" property="roleId"/>
        <result column="role_name" property="roleName"/>
    </resultMap>
    <select id="queryUserRoleVo" resultMap="UserRoleVoMap">
        SELECT
            t1.id as role_id,t1.role_name as  role_name
        FROM
            t_auth_role_info t1
                LEFT JOIN t_auth_user_role t2 ON t1.id = t2.role_id
                AND t2.is_deleted = 0
        where t1.is_deleted = 0 and t2.user_id = #{id}
    </select>
    <select id="searchUserInfoVo" resultMap="UserInfoVoMap">
        SELECT
            t1.id,
            t1.forbid_status,
            t1.nike_name,
            t1.phone,
            t1.passwd,
            t2.name AS shop_name,
            t1.shop_id,
            t1.employee_id,
            t1.employee_name,
            t1.mini_account_id,
            t1.mini_account_name,
            t1.store_front_code,
            t1.store_front_name,
            t1.department_code,
            t1.department_name,
            t1.stock_code,
            t1.stock_name
        FROM
            t_platform_account_info t1
                LEFT JOIN t_shops_partner t2 ON t1.shop_id = t2.shop_id
                AND t2.is_deleted = 0
        WHERE
            t1.is_deleted = 0
        <if test="params.nikeName!=null and params.nikeName!=''">
            and  t1.nike_name LIKE CONCAT('%',#{params.nikeName},'%')
        </if>
        <if test="params.phone!=null and params.phone!=''">
            and  t1.phone LIKE CONCAT('%',#{params.phone},'%')
        </if>
        <if test="params.forbidStatus!=null">
            and  t1.phforbid_statusone = #{params.forbidStatus}
        </if>
        <if test="params.accountType!=null">
            and  t1.account_type = #{params.accountType}
        </if>
        <if test="params.departmentName!=null and params.departmentName!=''">
            and  t1.department_name LIKE CONCAT('%',#{params.departmentName},'%')
        </if>
        <if test="params.employeeName!=null and params.employeeName!=''">
            and  t1.employee_name LIKE CONCAT('%',#{params.employeeName},'%')
        </if>
        <if test="params.stockName!=null and params.stockName!=''">
            and  t1.stock_name LIKE CONCAT('%',#{params.stockName},'%')
        </if>
        <if test="params.storeFrontName!=null and params.storeFrontName!=''">
            and  t1.store_front_name LIKE CONCAT('%',#{params.storeFrontName},'%')
        </if>
        order by t1.id
    </select>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        is_deleted,
        update_time,bind_mini_id,subject_id,forbid_status,
        account_type,region,address,
        id, password, passwd, salt, city, 'language', last_login_time, access_token, refresh_token, nike_name,
        avatar_url, phone, email, gender, open_id, union_id, province, country, privilege,
        ref_expires_time,bind_mini_shop_id,
        access_expires_time,comment_text,agent_id,balance,me_agent_id
    </sql>

</mapper>
