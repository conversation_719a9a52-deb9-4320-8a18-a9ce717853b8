<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.SysShopPackageOrderMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.entity.SysShopPackageOrder">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="up date_time" property="updateTime"/>
        <result column="account_id" property="accountId"/>
        <result column="shop_template_info_id" property="shopTemplateInfoId"/>
        <result column="order_num" property="orderNum"/>
        <result column="package_id" property="packageId"/>
        <result column="package_data" property="packageData"/>
        <result column="package_time" property="packageTime"/>
        <result column="package_price_unit" property="packagePriceUnit"/>
        <result column="package_start_time" property="packageStartTime"/>
        <result column="package_end_time" property="packageEndTime"/>
        <result column="package_price" property="packagePrice"/>
        <result column="amount_payable" property="amountPayable"/>
        <result column="paid_payable" property="paidPayable"/>
        <result column="pay_type" property="payType"/>
        <result column="status" property="status"/>
        <result column="pay_info" property="payInfo"/>
        <result column="relauditor_id" property="relauditorId"/>
        <result column="auditor_name" property="auditorName"/>
        <result column="auditor_status" property="auditorStatus"/>
        <result column="is_agreed" property="isAgreed"/>
        <result column="is_automatic_deduction" property="isAutomaticDeduction"/>
        <result column="is_received" property="isReceived"/>
        <result column="order_type" property="orderType"/>
        <result column="invoice_status" property="invoiceStatus"/>
        <result column="order_source" property="orderSource"/>
        <result column="audit_time" property="auditTime"/>
        <result column="package_name" property="packageName"/>
        <result column="shop_name" property="shopName"/>
        <result column="template_name" property="templateName"/>
        <result column="agent_id" property="agentId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        is_deleted,
        update_time,
        package_name,
        shop_name,
        template_name,
        audit_time,
        id,
        account_id,
        shop_template_info_id,
        order_num,
        package_id,
        package_data,
        order_type,
        package_time,
        package_price_unit,
        package_start_time,
        package_end_time,
        package_price,
        amount_payable,
        paid_payable,
        pay_type,
        status,
        pay_info,
        relauditor_id,
        auditor_name,
        auditor_status,
        is_agreed,
        is_automatic_deduction,
        is_received,
        shop_id,invoice_status,order_source,agent_id
    </sql>


    <select id="selectBoughtEnterpriseVersion" resultType="java.lang.Integer">
        SELECT
        count( tsspo.id )
        FROM
        t_sys_shop_package_order AS tsspo
        LEFT JOIN t_sys_shop_package AS tssp ON tssp.id = tsspo.package_id
        <where>
            tsspo.STATUS = 2
            AND tssp.LEVEL > 1
        </where>
        LIMIT 1
    </select>
</mapper>
