<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.PlatformLibrariesInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.entity.PlatformLibrariesInfo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="version" property="version"/>
        <result column="count" property="count"/>
        <result column="category_type" property="categoryType"/>
        <result column="belong_id" property="belongId"/>
        <result column="remark" property="remark"/>
        <result column="description" property="description"/>
        <result column="is_deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="uniqueness" property="uniqueness"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        id, name, type, status, version, count, category_type, belong_id, remark, description, is_delete,
        update_time,uniqueness
    </sql>

</mapper>
