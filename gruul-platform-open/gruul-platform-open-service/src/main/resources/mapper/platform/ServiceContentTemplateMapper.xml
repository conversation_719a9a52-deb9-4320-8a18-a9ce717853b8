<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.ServiceContentTemplateMapper">

    <select id="selectServiceContentTemplateList" resultType="com.medusa.gruul.platform.model.vo.ServiceContentTemplateVo">
        SELECT
        t.id,
        t.service_name,
        t.partner_model,
        t.status,
        t.remark,
        t.create_user_name as creator_name,
        t.create_time,
        t.last_modify_user_name as updater_name,
        t.update_time
        FROM
        t_platform_service_content_template t
        WHERE
        t.is_deleted = 0
        <if test="params.serviceName != null and params.serviceName != ''">
            AND t.service_name LIKE CONCAT('%', #{params.serviceName}, '%')
        </if>
        <if test="params.status != null">
            AND t.status = #{params.status}
        </if>
        <if test="params.remark != null and params.remark != '' ">
            AND t.remark LIKE CONCAT('%', #{params.remark}, '%')
        </if>
        ORDER BY
        t.id DESC
    </select>
</mapper>