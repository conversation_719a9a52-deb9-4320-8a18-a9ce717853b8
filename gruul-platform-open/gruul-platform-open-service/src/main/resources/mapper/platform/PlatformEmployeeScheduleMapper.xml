<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.PlatformEmployeeScheduleMapper">

    <!-- 职员排班信息结果集 -->
    <resultMap id="EmployeeScheduleVoMap" type="com.medusa.gruul.platform.api.model.vo.EmployeeScheduleVo">
        <id column="id" property="id"/>
        <result column="employee_id" property="employeeId"/>
        <result column="emp_number" property="empNumber"/>
        <result column="emp_full_name" property="empFullName"/>
        <result column="schedule_date" property="scheduleDate"/>
        <result column="work_status" property="workStatus"/>
        <result column="work_status_desc" property="workStatusDesc"/>
        <result column="shift_type" property="shiftType"/>
        <result column="shift_type_desc" property="shiftTypeDesc"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="store_front_id" property="storeFrontId"/>
        <result column="remark" property="remark"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 分页查询职员排班信息 -->
    <select id="searchEmployeeSchedule" resultMap="EmployeeScheduleVoMap">
        SELECT 
            s.id,
            s.employee_id,
            e.emp_number,
            e.emp_full_name,
            s.schedule_date,
            s.work_status,
            CASE s.work_status 
                WHEN 1 THEN '上班'
                WHEN 2 THEN '休息'
                WHEN 3 THEN '请假'
                ELSE ''
            END as work_status_desc,
            s.shift_type,
            CASE s.shift_type 
                WHEN 1 THEN '上午'
                WHEN 2 THEN '下午'
                WHEN 3 THEN '晚上'
                ELSE ''
            END as shift_type_desc,
            s.start_time,
            s.end_time,
            s.store_front_id,
            s.remark,
            s.create_user_name,
            s.create_time
        FROM t_platform_employee_schedule s
        LEFT JOIN t_platform_lyd_employee e ON s.employee_id = e.id AND e.is_deleted = 0
        WHERE s.is_deleted = 0
        <if test="params.employeeId != null">
            AND s.employee_id = #{params.employeeId}
        </if>
        <if test="params.empNumber != null and params.empNumber != ''">
            AND e.emp_number LIKE CONCAT('%', #{params.empNumber}, '%')
        </if>
        <if test="params.empFullName != null and params.empFullName != ''">
            AND e.emp_full_name LIKE CONCAT('%', #{params.empFullName}, '%')
        </if>
        <if test="params.startDate != null">
            AND s.schedule_date &gt;= #{params.startDate}
        </if>
        <if test="params.endDate != null">
            AND s.schedule_date &lt;= #{params.endDate}
        </if>

        <if test="params.workStatus != null">
            AND s.work_status = #{params.workStatus}
        </if>
        <if test="params.shiftType != null">
            AND s.shift_type = #{params.shiftType}
        </if>
        <if test="params.storeFrontId != null and params.storeFrontId != ''">
            AND s.store_front_id = #{params.storeFrontId}
        </if>
        ORDER BY s.id DESC, s.shift_type ASC
    </select>


</mapper>
