<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.PlatformMiniConfigMapper">
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.entity.PlatformMiniConfig">
        <!--@mbg.generated-->
        <!--@Table t_platform_pay_config-->
        <id column="id" property="id"/>
        <result column="app_id" property="appId"/>
        <result column="app_secret" property="appSecret"/>
        <result column="is_deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, app_id, app_secret, is_deleted, create_time, update_time,tenant_id
    </sql>
</mapper>