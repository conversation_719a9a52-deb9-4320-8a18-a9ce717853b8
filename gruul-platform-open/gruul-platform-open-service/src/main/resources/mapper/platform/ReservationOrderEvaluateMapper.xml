<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.ReservationOrderEvaluateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.entity.ReservationOrderEvaluate">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="shop_id" property="shopId" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="user_avatar_url" property="userAvatarUrl" />
        <result column="reservation_order_id" property="reservationOrderId" />
        <result column="success_score" property="successScore" />
        <result column="rate" property="rate" />
        <result column="comment" property="comment" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="last_modify_user_id" property="lastModifyUserId" />
        <result column="last_modify_user_name" property="lastModifyUserName" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="deleted" />
    </resultMap>

    <!-- 分页查询预约单评价列表 -->
    <select id="getPage" resultType="com.medusa.gruul.platform.model.vo.ReservationOrderEvaluateVo">
        SELECT
            e.id,
            e.user_id,
            e.user_name,
            e.user_avatar_url,
            e.reservation_order_id,
            e.success_score,
            e.rate,
            e.comment,
            e.create_time
        FROM
            t_reservation_order_evaluate e
        WHERE e.is_deleted = 0
            <if test="param.userId != null">
                AND e.user_id = #{param.userId}
            </if>
            <if test="param.userName != null and param.userName != ''">
                AND e.user_name LIKE CONCAT('%', #{param.userName}, '%')
            </if>
            <if test="param.reservationOrderId != null">
                AND e.reservation_order_id = #{param.reservationOrderId}
            </if>
            <if test="param.successScore != null">
                AND e.success_score = #{param.successScore}
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                AND e.create_time &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                AND e.create_time &lt;= #{param.endTime}
            </if>
            <if test="param.rate != null and param.rate != ''">
                AND e.rate = #{param.rate}
            </if>
        ORDER BY
            e.id DESC
    </select>

</mapper> 