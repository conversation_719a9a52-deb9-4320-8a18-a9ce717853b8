<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.PlatformServiceInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.entity.PlatformServiceInfo">
        <id column="id" property="id"/>
        <result column="is_deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="version" property="version"/>
        <result column="count" property="count"/>
        <result column="libraries_info_id" property="librariesInfoId"/>
        <result column="remark" property="remark"/>
        <result column="description" property="description"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        is_deleted,
        create_time,
        update_time,
        id, name, type, status, version, count, libraries_info_id, remark, description
    </sql>

</mapper>
