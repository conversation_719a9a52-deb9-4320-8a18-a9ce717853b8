<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.PlatformPositionTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.entity.PlatformPositionTemplate">
        <id column="id" property="id" />
        <result column="position_name" property="positionName" />
        <result column="partner_model" property="partnerModel" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="is_deleted" property="deleted" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_modify_user_id" property="lastModifyUserId" />
        <result column="last_modify_user_name" property="lastModifyUserName" />
    </resultMap>

    <!-- 查询职位模板列表 -->
    <select id="selectPositionTemplateList" resultMap="BaseResultMap">
        SELECT *
        FROM t_platform_position_template

        <where>
            is_deleted = 0
            <if test="params.positionName != null and params.positionName != ''">
                AND position_name LIKE CONCAT('%', #{params.positionName}, '%')
            </if>
            <if test="params.status != null">
                AND status = #{params.status}
            </if>
            <if test="params.remark != null and params.remark != ''">
                AND remark LIKE CONCAT('%', #{params.remark}, '%')
            </if>
        </where>
        ORDER BY id DESC
    </select>
</mapper> 