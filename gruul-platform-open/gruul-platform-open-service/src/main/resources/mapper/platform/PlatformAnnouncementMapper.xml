<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.PlatformAnnouncementMapper">

    <select id="getAnnouncementPage" resultType="com.medusa.gruul.platform.model.vo.PlatformAnnouncementVo">
        SELECT
        id,
        title,
        content,
        effective_start_Date,
        effective_end_Date,
        status,
        create_time,
        update_time
        FROM
            t_platform_announcement
        WHERE
        is_deleted = 0
        <if test="param.title != null and param.title != ''">
            AND title LIKE CONCAT('%', #{param.title}, '%')
        </if>
        <if test="param.status != null">
            AND status = #{param.status}
        </if>
        <if test="param.effectiveStartDate != null">
            AND effective_start_Date >= #{param.effectiveStartDate}
        </if>
        <if test="param.effectiveEndDate != null">
            AND effective_end_Date &lt;= #{param.effectiveEndDate}
        </if>

        <if test="param.today != null">
            AND effective_start_Date &lt;= #{param.today}
            AND effective_end_Date >= #{param.today}
        </if>

        ORDER BY id DESC
    </select>
</mapper>