<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.PlatformPositionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.entity.PlatformPosition">
        <id column="id" property="id" />
        <result column="position_name" property="positionName" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="last_modify_user_id" property="lastModifyUserId" />
        <result column="last_modify_user_name" property="lastModifyUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="deleted" />
    </resultMap>
    <resultMap id="ResultMapVo" type="com.medusa.gruul.platform.api.model.vo.PlatformPositionVo">
        <id column="id" property="id" />
        <result column="position_name" property="positionName" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="is_employee_posts" property="isEmployeePosts" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, position_name, status, remark, create_user_id, create_user_name,
        last_modify_user_id, last_modify_user_name, create_time, update_time, is_deleted
    </sql>

    <select id="pagePositionWithEmployee" resultMap="ResultMapVo">
        select pp.id,pp.position_name,pp.status,pp.remark,
               CASE
                   WHEN pep.position_id IS NULL THEN 0
                   ELSE 1
                END AS is_employee_posts
        from t_platform_position pp
                 left join (select position_id from  t_platform_employee_position where  employee_id =  #{employeeId} and status = 1)
            pep on pp.id = pep.position_id
        where pp.is_deleted = 0 and pp.status = 1
    </select>
</mapper> 