<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.PlatformAccountBalanceRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.entity.PlatformAccountBalanceRecord">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="account_id" property="accountId"/>
        <result column="consumption_type" property="consumptionType"/>
        <result column="order_num" property="orderNum"/>
        <result column="before_amount" property="beforeAmount"/>
        <result column="after_amount" property="afterAmount"/>
        <result column="amount" property="amount"/>
        <result column="type" property="type"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        is_deleted,
        update_time,
        id, account_id, consumption_type, order_num, before_amount, after_amount, amount, type
    </sql>

</mapper>
