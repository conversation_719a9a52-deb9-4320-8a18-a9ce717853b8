<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.AuthUserRoleMapper">
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.model.vo.AuthUserMenuVo">
        <result column="id" property="id"/>
        <result column="menu_name" property="menuName"/>
        <result column="menu_code" property="menuCode"/>
        <result column="menu_pid" property="menuPid"/>
    </resultMap>


    <select id="getAuthUserMenuVo" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT DISTINCT
            t3.id,
            t3.menu_name,
            t3.menu_code,
            t3.menu_pid
        FROM
            t_auth_user_role t1
                LEFT JOIN t_auth_role_menu t2 ON t1.role_id = t2.role_id
                AND t2.is_deleted = 0
                LEFT JOIN t_auth_menu_info t3 ON t2.menu_id = t3.id
                AND t3.is_deleted = 0
        WHERE
            t1.is_deleted = 0
          AND t3.menu_pid = 0
          AND t1.user_id = #{id}
    </select>

    <select id="getAuthUserMenuSecondVo" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT DISTINCT
            t3.id,
            t3.menu_name,
            t3.menu_code,
            t3.menu_pid
        FROM
            t_auth_user_role t1
                LEFT JOIN t_auth_role_menu t2 ON t1.role_id = t2.role_id
                AND t2.is_deleted = 0
                LEFT JOIN t_auth_menu_info t3 ON t2.menu_id = t3.id
                AND t3.is_deleted = 0
        WHERE
            t1.is_deleted = 0
          AND (t2.type is null or t2.type = 0)
          AND t1.user_id = #{arg0}
          AND t3.menu_pid = #{arg1}
          AND t3.menu_pid != 0

    </select>
</mapper>
