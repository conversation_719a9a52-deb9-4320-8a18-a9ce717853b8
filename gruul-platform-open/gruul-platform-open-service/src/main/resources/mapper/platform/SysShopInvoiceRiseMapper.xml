<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.platform.mapper.SysShopInvoiceRiseMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.platform.api.entity.SysShopInvoiceRise">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="account_id" property="accountId"/>
        <result column="head_type" property="headType"/>
        <result column="invoice_rise_name" property="invoiceRiseName"/>
        <result column="invoice_taxpayer_num" property="invoiceTaxpayerNum"/>
        <result column="remark" property="remark"/>
        <result column="default_status" property="defaultStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        is_deleted,
        update_time,
        id,
        account_id,
        head_type,
        invoice_rise_name,
        invoice_taxpayer_num,
        remark,
        default_status
    </sql>
</mapper>
