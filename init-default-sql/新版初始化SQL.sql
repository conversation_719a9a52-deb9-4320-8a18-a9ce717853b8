

-- 注意：每个SQL单独执行，切勿连续执行

-- 开通店铺需要提供的信息：登录用户的名字、登录用户的手机号、店铺名称、店铺座机电话、


-- 新增卖家端用户(只用增加主账号)，id是自增的，密码默认是:admin123，租户id(tenant_id):100001
INSERT INTO `t_platform_account_info` (`id`, `password`, `passwd`, `salt`, `city`, `language`, `last_login_time`, `access_token`, `refresh_token`, `nike_name`, `avatar_url`, `phone`, `email`, `gender`, `create_time`, `is_deleted`, `update_time`, `open_id`, `union_id`, `province`, `country`, `privilege`, `ref_expires_time`, `access_expires_time`, `forbid_status`, `subject_id`, `bind_mini_id`, `account_type`, `region`, `address`, `bind_mini_shop_id`, `balance`, `tenant_id`) VALUES (1, '', 'd0bb86a0318bccabca0dadded5ae2bef', 'c0h40r', NULL, NULL, null, '', '', '郭强', '', '***********', null, 0, '2023-05-10 09:37:17', 0, '2023-05-10 10:27:05', '', '', NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, 0, '0', '广西南宁市', null, 0.00, '100001');


-- 新增店铺信息，id是自增的，shop_template_id:t_platform_shop_template_info 模版id，可能需要修改；account_id：商户id，t_platform_account_info的id，可能需要修改；租户id(tenant_id):100001；pc_user_num(pc用户数):100
INSERT INTO `t_platform_shop_info` (`id`, `logo_url`, `shop_name`, `status`, `due_time`, `is_due`, `shop_template_id`, `account_id`, `agree_on`, `package_id`, `is_deleted`, `create_time`, `update_time`, `create_join`, `is_privatization_deployment`, `business_hours`, `shop_phone`, `bind_mini_id`, `bind_mp_id`, `shop_template_detail_id`, `certificate_path`, `mch_key`, `mch_id`, `pay_type`, `package_order_id`, `mini_bottom_log`, `tenant_id`, `pc_user_num`) VALUES (1, null, '易达商城', 2, '2023-05-10 13:22:39', 0, 1, 1, 1, 2, 0, '2023-05-10 13:24:57', '2023-05-10 13:16:50', NULL, NULL, '[\"08:30:00\",\"23:39:59\"]', '0771-5654338', NULL, NULL, NULL, NULL, null, null, 1, 2, null, '100001', 100);



-- 新增专区信息，id是自增的，租户id(tenant_id):100001
INSERT INTO `t_sale_mode` (`id`, `create_time`, `update_time`, `is_deleted`, `mode_name`, `mode_type`, `sort`, `default_sale`, `tenant_id`) VALUES (1, '2023-05-10 14:58:10', '2023-05-10 16:56:14', 0, '默认专区', NULL, 1, 0, '100001');


-- 新增系统配置信息：oss（图片）存储配置，默认本地存储，租户id(tenant_id):100001
INSERT INTO `t_system_conf` (`id`, `param_key`, `param_value`, `status`, `remark`, `is_deleted`, `create_time`, `update_time`, `tenant_id`) VALUES (1, 'currentOssType', '4', 1, NULL, 0, '2023-05-10 14:29:24', '2023-05-10 18:19:18', '100001');



-- 新增店铺套餐订单表，id是自增的，租户id(tenant_id):100001
INSERT INTO `t_sys_shop_package_order` (`id`, `create_time`, `is_deleted`, `update_time`, `account_id`, `shop_template_info_id`, `order_num`, `package_id`, `package_data`, `order_type`, `package_time`, `package_price_unit`, `package_start_time`, `package_end_time`, `package_price`, `amount_payable`, `paid_payable`, `pay_type`, `status`, `pay_info`, `relauditor_id`, `auditor_name`, `auditor_status`, `is_agreed`, `is_automatic_deduction`, `is_received`, `invoice_status`, `order_source`, `audit_time`, `agent_id`, `package_name`, `shop_name`, `template_name`, `tenant_id`) VALUES (1, '2023-05-10 14:24:58', 0, '2023-05-10 14:25:03', 1, '1', '1458027464310919168', 2, '{\"createTime\":\"2020-08-16T10:43:03\",\"deleted\":false,\"discountsJson\":\"[{\\\"price\\\":\\\"4200\\\",\\\"value\\\":365,\\\"unit\\\":\\\"y\\\"},{\\\"price\\\":\\\"4000\\\",\\\"value\\\":730,\\\"unit\\\":\\\"y\\\"},{\\\"price\\\":\\\"3500\\\",\\\"value\\\":1095,\\\"unit\\\":\\\"y\\\"}]\",\"functionDesc\":\"标准版所有功能,直播功能,区域团长功能,独立提货点,自主设置版权标志\",\"id\":2,\"level\":2,\"name\":\"企业版\",\"openState\":1,\"packagePrice\":4800.00,\"packagePriceUnit\":3,\"remark\":\"适用于在单一城市发展的企业级社区团购商家\",\"templateId\":1,\"templateVersionId\":70,\"updateTime\":\"2020-12-17T22:12:20\"}', 1, 7, 3, '2023-05-10 19:03:31', '2023-05-10 19:03:31', 4800.00, 0.00, 0.00, 5, 2, NULL, NULL, NULL, NULL, 1, 0, NULL, 0, 2, NULL, NULL, '企业版', 'xiaoq', '商超', '100001');


-- 新增订单晒单设置表，id是自增的，租户id(tenant_id):100001
INSERT INTO `t_order_share_setting` (`id`, `create_time`, `update_time`, `is_deleted`, `background`, `title`, `tenant_id`) VALUES (1, '2023-05-10 16:42:21', '2023-05-10 15:16:50', 0, ' ', '{sname}', '100001');


-- 新增订单设置信息，id是自增的，租户id(tenant_id):100001，是否开启负库存下单（open_negative_order）：0（默认不开启）
INSERT INTO `t_order_setting` (`id`, `create_time`, `update_time`, `is_deleted`, `flash_order_overtime`, `normal_order_overtime`, `confirm_overtime`, `finish_overtime`, `comment_overtime`, `open_evaluate`, `afs_apply_number`, `merchant_confirm_overtime`, `user_return_overtime`, `kd_app_id`, `kd_app_key`, `payment_model`, `custom_from`, `order_notify`, `tenant_id`, `open_negative_order`) VALUES (1, '2023-05-10 10:40:03', '2023-05-10 15:54:17', 0, 30, 5, 10, 5, 3, 1, 3, 5, 7, null, null, '1', '[{\"key\":\"备注\",\"type\":\"text\",\"required\":false,\"placeholder\":\"特殊要求请备注\"}]', 1, '100001', 0);



-- 新增引导页信息，id是自增的，租户id(tenant_id):100001，引导页是否开启（is_open）： 0 -->未开启 1-->已开启
INSERT INTO 
`t_shop_guide_page_switch` (`id`, `is_open`, `create_time`, `update_time`, `is_deleted`, `tenant_id`) VALUES (1, 1, '2023-05-10 14:35:45', '2023-05-10 14:19:42', 0, '100001');



-- INSERT INTO t_platform_pay_config  VALUES ('83', NULL, NULL, NULL, '0', '2021-12-17 13:58:05', '2022-01-12 19:53:09', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);



-- INSERT INTO `t_shops_renovation_template` VALUES ('66', '0', null, '1', '1', '2021-12-15 13:30:12', '2021-12-15 13:30:12', null, null, '0', '首页', null);

-- 新增店铺装修模板，id是自增的，租户id(tenant_id):100001

INSERT INTO `t_shops_renovation_template` (`id`, `is_deleted`, `type`, `colour`, `online_status`, `create_time`, `update_time`, `operator_id`, `operator_name`, `is_dev_template`, `name`, `is_copy_template`, `tenant_id`) VALUES (1, '0', NULL, '1', '1', '2023-05-10 13:30:12', '2023-05-10 13:30:12', NULL, NULL, '0', '首页', NULL, '100001');


-- 新增店铺装修页面全局控件属性分两步，id是自增的，租户id(tenant_id):100001，template_id：所属模板ID，从t_shops_renovation_template查出id。

-- 第一步，取id值赋给下一步的字段template_id
select id  from t_shops_renovation_template where tenant_id = '100001';

-- 第二步
INSERT INTO `t_shops_renovation_plugin` (`id`, `template_id`, `plugin_properties`, `is_deleted`, `create_time`, `update_time`, `operator_id`, `operator_name`, `plugin_name_cn`, `is_mandatory`, `is_selection`, `plugin_name_en`, `spare`, `copy_plugin_flag`, `tenant_id`) VALUES (1, 1, '[{\"icon\":\"\",\"value\":\"NavBar\",\"label\":\"底部导航\",\"id\":1682404120208,\"formData\":{\"menuList\":[{\"sortIndex\":0,\"selectedIconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/1e26aab823e84979a6994a0d34918526.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"\",\"linkSelectItem\":\"\",\"isHome\":true,\"text\":\"首页\",\"id\":\"195\",\"iconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/9ad3a62298fd4c2aa75f93ac2b3a517a.png\",\"type\":5,\"linkName\":\"\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/home_page.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/home_page1.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":1,\"selectedIconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/7dae3bdf8ee24833b9600185fcec5fed.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"mall\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"分类\",\"id\":102,\"iconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/f8d2575562af4c9490cb25b0101b680e.png\",\"type\":2,\"linkName\":\"联易达产品及服务\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_mall1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_mall.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":2,\"selectedIconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/5f25150c75f247b590b8d8f100ea5820.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"shopCar\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"购物车\",\"id\":4,\"iconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/bda9aea298bc489084af4134a399d292.png\",\"type\":0,\"linkName\":\"购物车\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_cart1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_cart.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":3,\"selectedIconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/13d654d202bb4fd69e2da28a6c20a90f.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"me\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"我的\",\"id\":3,\"iconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/4c5bcb9fb63f4e8e9e4cd50be247e00a.png\",\"type\":0,\"linkName\":\"个人中心\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/my1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/my.png\",\"getFrom\":\"bottomNav\"}],\"defaultColor\":\"#7A7E83\",\"selectColor\":\"#F64E3F\",\"codeStyle\":1}}]', '0', '2023-05-10 09:47:16', '2023-05-10 14:28:10', NULL, NULL, '底部导航', '0', '1', 'navBar', NULL, NULL, '100001');



-- INSERT INTO `t_shops_renovation_plugin` VALUES ('60', '66', '[{\"icon\":\"\",\"value\":\"NavBar\",\"label\":\"底部导航\",\"id\":1641968992375,\"formData\":{\"menuList\":[{\"sortIndex\":0,\"selectedIconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/1e26aab823e84979a6994a0d34918526.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"\",\"linkSelectItem\":\"\",\"isHome\":true,\"text\":\"首页\",\"id\":\"133\",\"iconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/9ad3a62298fd4c2aa75f93ac2b3a517a.png\",\"type\":5,\"linkName\":\"\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/home_page.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/home_page1.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":1,\"selectedIconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/7dae3bdf8ee24833b9600185fcec5fed.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"mall\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"商超\",\"id\":73,\"iconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/f8d2575562af4c9490cb25b0101b680e.png\",\"type\":2,\"linkName\":\"商超\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_mall1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_mall.png\",\"getFrom\":\"bottomNav\"},{\"text\":\"直播\",\"id\":16,\"iconType\":2,\"iconPath\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/20211225/5efb15c97c1e44da8df6e43aaa144272.gif\",\"selectedIconPath\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/ae7ba664df134a6d9e23c901f396386f.png\",\"linkSelectItem\":\"\",\"sortIndex\":4,\"linkUrl\":\"/pages/index/index\",\"linkName\":\"直播营销\",\"name\":\"live\",\"type\":0,\"isHome\":false,\"defIcon\":\"\",\"actIcon\":\"\",\"isAdd\":false,\"getFrom\":\"bottomNav\"},{\"sortIndex\":2,\"selectedIconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/5f25150c75f247b590b8d8f100ea5820.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"shopCar\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"购物车\",\"id\":4,\"iconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/bda9aea298bc489084af4134a399d292.png\",\"type\":0,\"linkName\":\"购物车\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_cart1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_cart.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":3,\"selectedIconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/13d654d202bb4fd69e2da28a6c20a90f.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"me\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"我的\",\"id\":3,\"iconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/4c5bcb9fb63f4e8e9e4cd50be247e00a.png\",\"type\":0,\"linkName\":\"个人中心\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/my1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/my.png\",\"getFrom\":\"bottomNav\"}],\"defaultColor\":\"#7A7E83\",\"selectColor\":\"#F64E3F\",\"codeStyle\":1}}]', '0', '2021-07-17 09:47:16', '2022-01-12 14:28:10', null, null, '底部导航', '0', '1', 'navBar', null, null);



-- 新增店铺装修模板页面分两步，id是自增的，租户id(tenant_id):100001，template_id：所属模板ID，从t_shops_renovation_template查出id。

-- 第一步，取id值赋给下一步的字段template_id
select id  from t_shops_renovation_template where tenant_id = '100001';

-- 第二步

INSERT INTO `t_shops_renovation_page` (`id`, `template_id`, `is_deleted`, `page_name`, `create_time`, `update_time`, `operator_id`, `operator_name`, `is_def`, `copy_plugin_flag`, `type`, `model_id`, `tenant_id`) VALUES (1, 1, '0', '首页', '2023-05-10 11:52:51', '2023-05-10 11:53:20', NULL, NULL, '1', NULL, NULL, NULL, '100001');


-- 新增店铺装修模板页面组件属性分两步，id是非自增的，租户id(tenant_id):100001，page_id：所属页面ID，从t_shops_renovation_page查出id。

-- 第一步，取id值赋给下一步的字段page_id
select id  from t_shops_renovation_template where tenant_id = '100001';

INSERT INTO `t_shops_renovation_assembly` (`id`, `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`, `tenant_id`) VALUES (5370, 1, '0', '{\"icon\":\"sousuo\",\"value\":\"Search\",\"label\":\"搜索\",\"id\":1660893650163,\"formData\":{\"showStyle\":\"is-style-one\",\"keyWord\":\"商品名称/编码/条形码\",\"hotWord\":[\"易达云\"]}}', '2023-05-10 16:57:11', '2023-05-10 16:57:11', NULL, NULL, '100001');



-- INSERT INTO `t_shops_renovation_assembly` (`id`, `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`) VALUES ('5789', '133', '0', '{\"icon\":\"sousuo\",\"value\":\"Search\",\"label\":\"搜索\",\"id\":1639724505275,\"formData\":{\"showStyle\":\"is-style-one\",\"keyWord\":\"请输入关键词搜索\",\"hotWord\":[\"预设搜索热词\"]}}', '2021-12-17 15:00:58', '2021-12-17 15:00:58', NULL, NULL);
-- INSERT INTO `t_shops_renovation_assembly` (`id`, `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`) VALUES ('5790', '133', '0', '{\"icon\":\"lunbotu\",\"value\":\"GoodSwiper\",\"label\":\"商品轮播图\",\"id\":1639724507587,\"formData\":{\"type\":\"GoodSwiper\",\"radio\":1,\"btmInput\":\"\",\"btnImg\":\"https://qiniu-app.qtshe.com/u391.png\",\"btnlink\":\"\",\"margin\":8,\"swiperList\":[{\"title\":\"\",\"img\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/cd3f47b2ab9b42238eda1d7aa4ba5ebb.png\",\"link\":{\"id\":\"\",\"type\":\"\",\"name\":\"\",\"url\":\"\",\"append\":\"\"},\"linkName\":\"\"},{\"title\":\"\",\"img\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/0fafceff410847868b03a361b7bc608b.png\",\"link\":{\"id\":\"\",\"type\":\"\",\"name\":\"\",\"url\":\"\",\"append\":\"\"},\"linkName\":\"\"}],\"padding\":3,\"sidePadding\":14,\"imageStyle\":1,\"imageAngle\":1,\"indicator\":1,\"height\":315,\"shownum\":3,\"interval\":1}}', '2021-12-17 15:00:59', '2021-12-17 15:00:59', NULL, NULL);


-- 新增用户中心配置，id是自增的，租户id(tenant_id):100001

INSERT INTO `t_account_center` (`id`, `create_time`, `is_deleted`, `update_time`, `head_style`, `custom_style`, `get_cart_text`, `hide_cart_inlet`, `order_info`, `menu_style`, `code_style`, `tenant_id`) VALUES (1, '2023-05-10 11:08:25', 0, '2023-05-10 09:49:11', 1, '{\"backgroundImage\":\"http://rhksl0fkl.hd-bkt.clouddn.com/********/46f3bcd50a7841578ce34e017693f4cf.png\",\"cardColor\":\"#E84513\",\"textColor\":\"#F8F404\"}', '领卡文案', 1, '{\"afterSaleIcon\":{\"name\":\"售后\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/e72e0fb8015c4daf9b341d9e4407e22d.png\"},\"waitIcon\":{\"name\":\"待付款\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/9981a93c017948098be4056195a018fe.png\"},\"waitPickingIcon\":{\"name\":\"待提货\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/a286e9e2ce03433e83373f6f5d5fd90a.png\"},\"deliveryIcon\":{\"name\":\"配送中\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/b46aa7aedff449d8ad6624e5787ab49f.png\"},\"evaluateIcon\":{\"name\":\"评价中心\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/3bb776cebf5c4835a8ce01b8950aa5ac.png\"},\"waitDelivered\":{\"name\":\"待发货\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/2da9a606dd214b2b95327f6ff72db547.png\"}}', 2, 2, '100001');


-- 新增用户中心菜单配置，id是自增的，租户id(tenant_id):100001

INSERT INTO `t_account_center_menu` (`id`, `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES (1, '2023-05-10 09:49:11', 0, '2023-05-10 09:49:11', '资源入驻', 'http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/%E5%9B%BE%E6%A0%87/ziyuanruzhu.png', 2, 5, 0, 1, 1, 'http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/%E5%9B%BE%E6%A0%87/ziyuanruzhu.png', 0, '{\"name\":\"资源入驻\",\"id\":5,\"type\":0,\"url\":\"/pages/resourceApply/resourceApply\"}', '100001');

INSERT INTO `t_account_center_menu` (`id`, `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES (2, '2023-05-10 09:49:11', 0, '2023-05-10 09:49:11', '购物车', 'http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/%E5%9B%BE%E6%A0%87/gouwuche.png', 2, 9, 0, 1, 1, 'http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/%E5%9B%BE%E6%A0%87/gouwuche.png', 0, '{\"name\":\"购物车\",\"id\":4,\"type\":0,\"url\":\"/pages/index/index\",\"append\":\"shopCar\"}', '100001');

INSERT INTO `t_account_center_menu` (`id`, `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES (3, '2023-05-10 09:49:11', 0, '2023-05-10 09:49:11', '地址管理', 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/708afcdfc62443938c9f07ef5d07daf5.png', 2, 11, 0, 1, 1, 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/708afcdfc62443938c9f07ef5d07daf5.png', 0, '{\"name\":\"地址管理\",\"id\":7,\"type\":0,\"url\":\"/pages/address/address\"}', '100001');

INSERT INTO `t_account_center_menu` (`id`, `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES (4, '2023-05-10 09:49:11', 0, '2023-05-10 09:49:11', '设置', 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/80c68ba092174f3ea2067adf8944dbb6.png', 2, 12, 0, 1, 1, 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/80c68ba092174f3ea2067adf8944dbb6.png', 0, '{\"name\":\"设置\",\"id\":9,\"type\":0,\"url\":\"/pages/mySetting/mySetting\"}', '100001');

INSERT INTO `t_account_center_menu` (`id`, `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`, `tenant_id`) VALUES (5, '2023-05-10 09:49:11', 0, '2023-05-10 09:49:11', '客服', 'http://2866458.nnlyd.com/api/oss-open//ossupload//********/b946d7adafad46279e900d2c08df70d5.png', 2, 0, 0, 1, 0, '', 0, '{\"id\":8,\"name\":\"客服\",\"type\":0,\"url\":\"\",\"append\":\"\"}', '100001');


-- 新增商品展示分类，id是自增的，租户id(tenant_id):100001

-- INSERT INTO `t_show_category` (`id`, `create_time`, `update_time`, `is_deleted`, `parent_id`, `sale_mode`, `name`, `level`, `sort`) VALUES ('224', '2022-01-12 20:45:59', '2022-01-12 20:45:59', '0', '0', '73', '数码', '0', '0');
-- INSERT INTO `t_show_category` (`id`, `create_time`, `update_time`, `is_deleted`, `parent_id`, `sale_mode`, `name`, `level`, `sort`) VALUES ('225', '2022-01-12 20:46:12', '2022-01-12 20:46:12', '0', '224', NULL, '手机', '1', '0');

-- 新增产品
-- INSERT INTO `t_product` (`id`, `create_time`, `update_time`, `is_deleted`, `provider_id`, `attribute_id`, `attribute_name`, `freight_template_id`, `sale_mode`, `limit_type`, `name`, `pic`, `wide_pic`, `album_pics`, `video_url`, `product_sn`, `status`, `place`, `csv_url`, `sort`, `sale`, `unit`, `weight`, `service_ids`, `detail`, `is_open_specs`, `attribute`, `sale_describe`, `score`, `distribution_mode`) VALUES ('68', '2022-01-12 20:49:10', '2022-01-12 20:49:10', '0', NULL, NULL, NULL, '0', '73', '0', 'HUAWEI P50 Pocket 4G 全网通 8GB+2', 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/20220112/8716df28dfe647529cd0982d698a89e6.png', NULL, 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/20220112/8716df28dfe647529cd0982d698a89e6.png', '', '2022011200000001', '1', '0', NULL, '0', '0', '克', '0.00', '全场包邮,7天退换,48小时发货,假一赔十,正品保证', '', '1', '', '购买即赠商城积分，积分可抵现~', '5.0', '1');


-- INSERT INTO `t_sku_stock` (`id`, `create_time`, `update_time`, `is_deleted`, `version`, `product_id`, `sku_code`, `specs`, `weight`, `pic`, `price`, `original_price`, `stock`, `low_stock`, `sale`, `per_limit`, `gift_integration`) VALUES ('102', '2022-01-12 20:49:10', '2022-01-12 20:49:10', '0', NULL, '68', '', '', '0.00', 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/20220112/8716df28dfe647529cd0982d698a89e6.png', '8988.00', '8988.00', '10', '0', '0', '10', '0');


-- INSERT INTO `t_sms_template` (`id`, `user_id`, `sms_provider_id`, `sms_template_content`, `sms_template_code`, `sms_template_is_pass`, `sms_remark`, `is_deleted`, `create_time`, `update_time`, `template_type`, `sms_template_type`, `sms_template_name`) VALUES ('7', '1', '4', '您的验证码为 ${code}，请勿告知他人。', '模板id', '2', ' ', '0', '2020-01-04 15:52:26', '2020-01-04 15:52:28', '1', '1', NULL);

-- INSERT INTO `t_sms_sign` (`id`, `user_id`, `sms_provider_id`, `sms_sign`, `sms_sign_is_pass`, `sms_sign_remark`, `is_deleted`, `create_time`, `update_time`, `sms_sign_type`, `sign_type`) VALUES ('6', '1', '1', '短信签名', '1', '  ', '0', '2020-01-04 15:32:56', '2020-01-04 15:32:59', NULL, NULL);

-- INSERT INTO `t_sms_provider` (`id`, `user_id`, `sms_provider_name`, `sms_provider_appId`, `sms_provider_app_secret`, `sms_provider_available_count`, `sms_provider_used_count`, `sms_provider_total_count`, `sms_provider_status`, `is_deleted`, `create_time`, `update_time`) VALUES ('4', '1', '短信供应商名称', 'sms_provider_appId', 'sms_provider_app_secret', '1000', '0', '1000', '0', '0', '2019-12-31 00:53:36', '2019-12-31 00:53:41');
