

INSERT INTO `t_platform_account_info` VALUES ('1', 'admin123', 'd0bb86a0318bccabca0dadded5ae2bef', 'c0h40r', null, null, '2022-01-12 18:50:29', '50_wGRNH93vJFwHQHUB-DO98uHt4vvRiLxFzDRGobURn9FxC_JcQmVrnukSZf2eRiIOQKKWvFZxlHJDwLi7jub0oKMZ9xqAOBeuXIy3lopRx6E', '50_wGRNH93vJFwHQHUB-DO98uHt4vvRiLxFzDRGobURn9FxC_JcQmVrnukSZf2eRiIOQKKWvFZxlHJDwLi7jub0oKMZ9xqAOBeuXIy3lopRx6E', '偏不。', 'https://thirdwx.qlogo.cn/mmopen/vi_32/YwyBuxb7eQFHLc2F4ypTcHd1g1qlzyyE6S7ce4Ysic4d3obfiaF2pic09qhh2c2CZMtiadAFL53viaPyDzu7uLDia4Fw/132', '***********', '<EMAIL>', '0', '2021-12-13 11:37:17', '0', '2022-01-12 18:50:29', 'oaG9L5s2ZJxpYSS_1wozjSfTY3AU', 'oAvsn6wJG6MTV1QM90bt_GDc7d-0', null, null, null, null, null, '0', null, null, null, '0', '浙江省宁波市鄞州区', 'kl', '0.00');


INSERT INTO `t_platform_shop_info` VALUES ('1', 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/1920184934464fc8833918c9bca2e95a.png', '默认店铺', '1', '2021-12-13 13:22:39', '0', '1', '1', '1', '2', '0', '2021-12-13 13:24:57', '2022-01-12 17:38:15', null, null, '[\"08:30:00\",\"23:59:59\"]', '18967889883', null, null, null, null, 'medusa18058505737medusa180585057', '1600472250', '1', '2', 'https://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200407/logo.png');


INSERT INTO `t_sale_mode` VALUES ('73', '2021-12-15 10:23:44', '2021-12-15 10:23:44', '0', '商超', null, '0');


INSERT INTO `t_system_conf` (`id`, `param_key`, `param_value`, `status`, `remark`, `is_deleted`, `create_time`, `update_time`) VALUES ('10', 'storage_tencent_cloud', '0', '1', NULL, '0', '2021-12-17 14:29:24', '2022-01-12 19:01:19');
INSERT INTO `t_system_conf` (`id`, `param_key`, `param_value`, `status`, `remark`, `is_deleted`, `create_time`, `update_time`) VALUES ('11', 'currentOssType', '3', '1', NULL, '0', '2021-12-17 14:29:24', '2022-01-12 19:01:19');
INSERT INTO `t_system_conf` (`id`, `param_key`, `param_value`, `status`, `remark`, `is_deleted`, `create_time`, `update_time`) VALUES ('12', 'storage_qiniouyun', '{}', '1', NULL, '0', '2021-12-22 14:19:11', '2021-12-22 14:19:11');


INSERT INTO `t_sys_shop_package_order` VALUES ('2', '2021-12-17 14:24:58', '0', '2021-12-17 14:25:03', '1', '1', '1458027464310919168', '2', '{\"createTime\":\"2020-08-16T10:43:03\",\"deleted\":false,\"discountsJson\":\"[{\\\"price\\\":\\\"4200\\\",\\\"value\\\":365,\\\"unit\\\":\\\"y\\\"},{\\\"price\\\":\\\"4000\\\",\\\"value\\\":730,\\\"unit\\\":\\\"y\\\"},{\\\"price\\\":\\\"3500\\\",\\\"value\\\":1095,\\\"unit\\\":\\\"y\\\"}]\",\"functionDesc\":\"标准版所有功能,直播功能,区域团长功能,独立提货点,自主设置版权标志\",\"id\":2,\"level\":2,\"name\":\"企业版\",\"openState\":1,\"packagePrice\":4800.00,\"packagePriceUnit\":3,\"remark\":\"适用于在单一城市发展的企业级社区团购商家\",\"templateId\":1,\"templateVersionId\":70,\"updateTime\":\"2020-12-17T22:12:20\"}', '1', '7', '3', '2021-11-09 19:03:31', '2021-11-16 19:03:31', '4800.00', '0.00', '0.00', '5', '2', null, null, null, null, '1', '0', null, '0', '2', null, null, '企业版', 'xiaoq', '商超');


INSERT INTO `t_order_share_setting` VALUES ('1', '2021-12-15 16:42:21', '2021-12-25 09:58:24', '0', ' ', '{sname}');


INSERT INTO `t_order_setting` VALUES ('00000000000000000005', '2021-12-15 10:40:03', '2022-01-12 16:58:07', '0', '30', '25', '1', '1', '3', '1', '3', '1', '7', 'app_id', 'kd_app_key', '1', '[{\"key\":\"买家留言\",\"type\":\"text\",\"required\":true,\"placeholder\":\"请输入45个字内说明\"},{\"key\":\"上传图片\",\"type\":\"image\",\"required\":false,\"placeholder\":\"请上传图片\"}]', '0');


INSERT INTO `t_shop_guide_page_switch` VALUES ('1', '1', '2021-12-18 14:35:45', '2021-12-18 14:35:47', '0');



INSERT INTO t_platform_pay_config  VALUES ('83', NULL, NULL, NULL, '0', '2021-12-17 13:58:05', '2022-01-12 19:53:09', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);


INSERT INTO `t_shops_renovation_template` VALUES ('66', '0', null, '1', '1', '2021-12-15 13:30:12', '2021-12-15 13:30:12', null, null, '0', '首页', null);


INSERT INTO `t_shops_renovation_plugin` VALUES ('60', '66', '[{\"icon\":\"\",\"value\":\"NavBar\",\"label\":\"底部导航\",\"id\":1641968992375,\"formData\":{\"menuList\":[{\"sortIndex\":0,\"selectedIconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/1e26aab823e84979a6994a0d34918526.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"\",\"linkSelectItem\":\"\",\"isHome\":true,\"text\":\"首页\",\"id\":\"133\",\"iconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/9ad3a62298fd4c2aa75f93ac2b3a517a.png\",\"type\":5,\"linkName\":\"\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/home_page.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/home_page1.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":1,\"selectedIconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/7dae3bdf8ee24833b9600185fcec5fed.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"mall\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"商超\",\"id\":73,\"iconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/f8d2575562af4c9490cb25b0101b680e.png\",\"type\":2,\"linkName\":\"商超\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_mall1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_mall.png\",\"getFrom\":\"bottomNav\"},{\"text\":\"直播\",\"id\":16,\"iconType\":2,\"iconPath\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/20211225/5efb15c97c1e44da8df6e43aaa144272.gif\",\"selectedIconPath\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/ae7ba664df134a6d9e23c901f396386f.png\",\"linkSelectItem\":\"\",\"sortIndex\":4,\"linkUrl\":\"/pages/index/index\",\"linkName\":\"直播营销\",\"name\":\"live\",\"type\":0,\"isHome\":false,\"defIcon\":\"\",\"actIcon\":\"\",\"isAdd\":false,\"getFrom\":\"bottomNav\"},{\"sortIndex\":2,\"selectedIconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/5f25150c75f247b590b8d8f100ea5820.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"shopCar\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"购物车\",\"id\":4,\"iconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/bda9aea298bc489084af4134a399d292.png\",\"type\":0,\"linkName\":\"购物车\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_cart1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/shopping_cart.png\",\"getFrom\":\"bottomNav\"},{\"sortIndex\":3,\"selectedIconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/13d654d202bb4fd69e2da28a6c20a90f.png\",\"iconType\":2,\"linkUrl\":\"/pages/index/index\",\"name\":\"me\",\"linkSelectItem\":\"\",\"isHome\":false,\"text\":\"我的\",\"id\":3,\"iconPath\":\"http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/gruul/20200327/4c5bcb9fb63f4e8e9e4cd50be247e00a.png\",\"type\":0,\"linkName\":\"个人中心\",\"defIcon\":\"https://oss-tencent.bgniao.cn/api/my1.png\",\"actIcon\":\"https://oss-tencent.bgniao.cn/api/my.png\",\"getFrom\":\"bottomNav\"}],\"defaultColor\":\"#7A7E83\",\"selectColor\":\"#F64E3F\",\"codeStyle\":1}}]', '0', '2021-07-17 09:47:16', '2022-01-12 14:28:10', null, null, '底部导航', '0', '1', 'navBar', null, null);


INSERT INTO `t_shops_renovation_page` VALUES ('133', '66', '0', '首页', '2021-12-17 15:41:19', '2022-01-12 13:29:09', null, null, '1', null, null, null);


INSERT INTO `t_shops_renovation_assembly` (`id`, `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`) VALUES ('5789', '133', '0', '{\"icon\":\"sousuo\",\"value\":\"Search\",\"label\":\"搜索\",\"id\":1639724505275,\"formData\":{\"showStyle\":\"is-style-one\",\"keyWord\":\"请输入关键词搜索\",\"hotWord\":[\"预设搜索热词\"]}}', '2021-12-17 15:00:58', '2021-12-17 15:00:58', NULL, NULL);
INSERT INTO `t_shops_renovation_assembly` (`id`, `page_id`, `is_deleted`, `properties`, `create_time`, `update_time`, `operator_id`, `operator_name`) VALUES ('5790', '133', '0', '{\"icon\":\"lunbotu\",\"value\":\"GoodSwiper\",\"label\":\"商品轮播图\",\"id\":1639724507587,\"formData\":{\"type\":\"GoodSwiper\",\"radio\":1,\"btmInput\":\"\",\"btnImg\":\"https://qiniu-app.qtshe.com/u391.png\",\"btnlink\":\"\",\"margin\":8,\"swiperList\":[{\"title\":\"\",\"img\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/cd3f47b2ab9b42238eda1d7aa4ba5ebb.png\",\"link\":{\"id\":\"\",\"type\":\"\",\"name\":\"\",\"url\":\"\",\"append\":\"\"},\"linkName\":\"\"},{\"title\":\"\",\"img\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/0fafceff410847868b03a361b7bc608b.png\",\"link\":{\"id\":\"\",\"type\":\"\",\"name\":\"\",\"url\":\"\",\"append\":\"\"},\"linkName\":\"\"}],\"padding\":3,\"sidePadding\":14,\"imageStyle\":1,\"imageAngle\":1,\"indicator\":1,\"height\":315,\"shownum\":3,\"interval\":1}}', '2021-12-17 15:00:59', '2021-12-17 15:00:59', NULL, NULL);



INSERT INTO `t_account_center` (`id`, `create_time`, `is_deleted`, `update_time`, `head_style`, `custom_style`, `get_cart_text`, `hide_cart_inlet`, `order_info`, `menu_style`, `code_style`) VALUES ('1', '2021-12-20 11:08:25', '0', '2022-01-12 14:54:03', '1', '{\"backgroundImage\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/0ee740d296884fb3a6651f2950a6031d.jpg\",\"cardColor\":\"#E84513\",\"textColor\":\"#F8F404\"}', '领卡文案', '1', '{\"afterSaleIcon\":{\"name\":\"售后\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/e72e0fb8015c4daf9b341d9e4407e22d.png\"},\"waitIcon\":{\"name\":\"待付款\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/9981a93c017948098be4056195a018fe.png\"},\"waitPickingIcon\":{\"name\":\"待提货\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/a286e9e2ce03433e83373f6f5d5fd90a.png\"},\"deliveryIcon\":{\"name\":\"配送中\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/b46aa7aedff449d8ad6624e5787ab49f.png\"},\"evaluateIcon\":{\"name\":\"评价中心\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/3bb776cebf5c4835a8ce01b8950aa5ac.png\"},\"waitDelivered\":{\"name\":\"待发货\",\"url\":\"https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/2da9a606dd214b2b95327f6ff72db547.png\"}}', '2', '2');


INSERT INTO `t_account_center_menu` (`id`, `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`) VALUES ('4109', '2022-01-12 14:54:04', '0', '2022-01-12 14:54:04', '资源入驻', 'http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/%E5%9B%BE%E6%A0%87/ziyuanruzhu.png', '2', '5', '0', '1', '1', 'http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/%E5%9B%BE%E6%A0%87/ziyuanruzhu.png', '0', '{\"name\":\"资源入驻\",\"id\":5,\"type\":0,\"url\":\"/pages/resourceApply/resourceApply\"}');
INSERT INTO `t_account_center_menu` (`id`, `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`) VALUES ('4110', '2022-01-12 14:54:04', '0', '2022-01-12 14:54:04', '购物车', 'http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/%E5%9B%BE%E6%A0%87/gouwuche.png', '2', '9', '0', '1', '1', 'http://medusa-small-file.oss-cn-hangzhou.aliyuncs.com/%E5%9B%BE%E6%A0%87/gouwuche.png', '0', '{\"name\":\"购物车\",\"id\":4,\"type\":0,\"url\":\"/pages/index/index\",\"append\":\"shopCar\"}');
INSERT INTO `t_account_center_menu` (`id`, `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`) VALUES ('4111', '2022-01-12 14:54:04', '0', '2022-01-12 14:54:04', '地址管理', 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/708afcdfc62443938c9f07ef5d07daf5.png', '2', '11', '0', '1', '1', 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/708afcdfc62443938c9f07ef5d07daf5.png', '0', '{\"name\":\"地址管理\",\"id\":7,\"type\":0,\"url\":\"/pages/address/address\"}');
INSERT INTO `t_account_center_menu` (`id`, `create_time`, `is_deleted`, `update_time`, `menu_name`, `menu_icon_url`, `style_type`, `sort_index`, `p_id`, `hide_menu`, `allow_use`, `default_icon`, `split_flag`, `link_select_item`) VALUES ('4112', '2022-01-12 14:54:04', '0', '2022-01-12 14:54:04', '设置', 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/80c68ba092174f3ea2067adf8944dbb6.png', '2', '12', '0', '1', '1', 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/********/80c68ba092174f3ea2067adf8944dbb6.png', '0', '{\"name\":\"设置\",\"id\":9,\"type\":0,\"url\":\"/pages/mySetting/mySetting\"}');



INSERT INTO `t_show_category` (`id`, `create_time`, `update_time`, `is_deleted`, `parent_id`, `sale_mode`, `name`, `level`, `sort`) VALUES ('224', '2022-01-12 20:45:59', '2022-01-12 20:45:59', '0', '0', '73', '数码', '0', '0');
INSERT INTO `t_show_category` (`id`, `create_time`, `update_time`, `is_deleted`, `parent_id`, `sale_mode`, `name`, `level`, `sort`) VALUES ('225', '2022-01-12 20:46:12', '2022-01-12 20:46:12', '0', '224', NULL, '手机', '1', '0');


INSERT INTO `t_product` (`id`, `create_time`, `update_time`, `is_deleted`, `provider_id`, `attribute_id`, `attribute_name`, `freight_template_id`, `sale_mode`, `limit_type`, `name`, `pic`, `wide_pic`, `album_pics`, `video_url`, `product_sn`, `status`, `place`, `csv_url`, `sort`, `sale`, `unit`, `weight`, `service_ids`, `detail`, `is_open_specs`, `attribute`, `sale_describe`, `score`, `distribution_mode`) VALUES ('68', '2022-01-12 20:49:10', '2022-01-12 20:49:10', '0', NULL, NULL, NULL, '0', '73', '0', 'HUAWEI P50 Pocket 4G 全网通 8GB+2', 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/20220112/8716df28dfe647529cd0982d698a89e6.png', NULL, 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/20220112/8716df28dfe647529cd0982d698a89e6.png', '', '2022011200000001', '1', '0', NULL, '0', '0', '克', '0.00', '全场包邮,7天退换,48小时发货,假一赔十,正品保证', '', '1', '', '购买即赠商城积分，积分可抵现~', '5.0', '1');


INSERT INTO `t_sku_stock` (`id`, `create_time`, `update_time`, `is_deleted`, `version`, `product_id`, `sku_code`, `specs`, `weight`, `pic`, `price`, `original_price`, `stock`, `low_stock`, `sale`, `per_limit`, `gift_integration`) VALUES ('102', '2022-01-12 20:49:10', '2022-01-12 20:49:10', '0', NULL, '68', '', '', '0.00', 'https://medusa-small-file-**********.cos.ap-shanghai.myqcloud.com/gruul/20220112/8716df28dfe647529cd0982d698a89e6.png', '8988.00', '8988.00', '10', '0', '0', '10', '0');

INSERT INTO `t_sms_template` (`id`, `user_id`, `sms_provider_id`, `sms_template_content`, `sms_template_code`, `sms_template_is_pass`, `sms_remark`, `is_deleted`, `create_time`, `update_time`, `template_type`, `sms_template_type`, `sms_template_name`) VALUES ('7', '1', '4', '您的验证码为 ${code}，请勿告知他人。', '模板id', '2', ' ', '0', '2020-01-04 15:52:26', '2020-01-04 15:52:28', '1', '1', NULL);

INSERT INTO `t_sms_sign` (`id`, `user_id`, `sms_provider_id`, `sms_sign`, `sms_sign_is_pass`, `sms_sign_remark`, `is_deleted`, `create_time`, `update_time`, `sms_sign_type`, `sign_type`) VALUES ('6', '1', '1', '短信签名', '1', '  ', '0', '2020-01-04 15:32:56', '2020-01-04 15:32:59', NULL, NULL);

INSERT INTO `t_sms_provider` (`id`, `user_id`, `sms_provider_name`, `sms_provider_appId`, `sms_provider_app_secret`, `sms_provider_available_count`, `sms_provider_used_count`, `sms_provider_total_count`, `sms_provider_status`, `is_deleted`, `create_time`, `update_time`) VALUES ('4', '1', '短信供应商名称', 'sms_provider_appId', 'sms_provider_app_secret', '1000', '0', '1000', '0', '0', '2019-12-31 00:53:36', '2019-12-31 00:53:41');
