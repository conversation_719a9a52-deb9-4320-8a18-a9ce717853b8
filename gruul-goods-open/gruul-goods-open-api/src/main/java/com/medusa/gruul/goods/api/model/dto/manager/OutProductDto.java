package com.medusa.gruul.goods.api.model.dto.manager;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.core.param.QueryParam;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.model.vo.manager.SkuStockMemberPriceVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;


/**
 * 外部接口新增或修改产品信息DTO
 */
@Data
@ApiModel(value = "外部接口新增或修改产品信息DTO")
public class OutProductDto{

    private Long id;

    @ApiModelProperty(value = "供应商id")
    private Long providerId;

//    @NotNull
    @ApiModelProperty(value = "配送方式(0--商家配送，1--快递配送)")
    private Integer distributionMode;

    @ApiModelProperty(value = "运费模板ID")
    private Long freightTemplateId;

    @ApiModelProperty(value = "属性模版ID")
    private Long attributeId;

    @ApiModelProperty(value = "属性模板名称")
    private String attributeName;

//    @NotNull
    @ApiModelProperty(value = "限购类型(默认统一规格，0--统一规格，1--统一限购，2--规格限购)")
    private Integer limitType;

//    @NotNull
    @ApiModelProperty(value = "销售专区")
    private Long saleMode;

    @NotBlank
    @ApiModelProperty(value = "商品名称")
    private String name;

//    @NotBlank
    @ApiModelProperty(value = "展示图片")
    private String pic;

    @ApiModelProperty(value = "宽屏展示图片")
    private String widePic;

//    @NotBlank
    @ApiModelProperty(value = "画册图片，连产品图片限制为6张，以逗号分割")
    private String albumPics;

    @ApiModelProperty(value = "视频url")
    private String videoUrl;

    @ApiModelProperty(value = "货号")
    private String productSn;

    @ApiModelProperty(value = "状态(默认上架，0--下架，1--上架)")
    private Integer status;

    @ApiModelProperty(value = "商品位置(默认线上，0--线上，1--素材库)")
    private Integer place;

    @ApiModelProperty(value = "电商平台链接")
    private String csvUrl;

    @ApiModelProperty(value = "销量")
    private Integer sale;

    @ApiModelProperty(value = "基本单位")
    private String unit;

    @ApiModelProperty(value = "基本单位id")
    private Long unitId;

//    @NotNull
    @ApiModelProperty(value = "商品重量，默认为克")
    private BigDecimal weight;

    @ApiModelProperty(value = "以逗号分割的产品服务：1->无忧退货；2->快速退款；3->免费包邮")
    private String serviceIds;

    @ApiModelProperty(value = "商品详情")
    private String detail;

//    @NotNull
    @ApiModelProperty(value = "规格是否展开")
    private Boolean openSpecs;

    @ApiModelProperty(value = "销售属性")
    private String attribute;

    @ApiModelProperty(value = "卖点描述")
    private String saleDescribe;

    @ApiModelProperty(value = "评分")
    private BigDecimal score;

    @ApiModelProperty(value = "商品sku信息")
    private List<SkuStockDto> skuStocks;

    @ApiModelProperty(value = "商品辅助单位信息")
    private List<ProductSecUnitDto> productSecUnits;

    @ApiModelProperty(value = "商品属性信息")
    private List<ProductAttributeDto> productAttributes;

    @ApiModelProperty(value = "商品展示分类信息")
    private List<ProductShowCategoryDto> productShowCategorys;

    @ApiModelProperty(value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(value = "商品规格会员价格")
    private  List<SkuStockMemberPriceVo> skuStockMemberPriceVos;

    @ApiModelProperty(value = "会员价类型（默认不使用会员价）--》0：不使用会员价；1：固定金额；2：百分比")
    private Integer memberPriceType;

    @ApiModelProperty(value = "商品规格")
    private String spec;

    /**
     * 商品标识
     */
    @ApiModelProperty(value = "商品标识")
    private String classCode;

    public Product coverProduct() {
        Product product = new Product();
        BeanUtil.copyProperties(this, product);
        return product;
    }
}