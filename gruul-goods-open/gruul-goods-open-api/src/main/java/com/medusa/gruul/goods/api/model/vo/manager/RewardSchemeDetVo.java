package com.medusa.gruul.goods.api.model.vo.manager;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.goods.api.model.dto.manager.RewardSchemeDetNonProductDto;
import com.medusa.gruul.goods.api.model.dto.manager.RewardSchemeDetProductDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:35 2025/3/12
 */
@Data
@ApiModel(value = "RewardSchemeDetVo", description = "奖励方案明细Vo")
public class RewardSchemeDetVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "奖励方案明细id")
    private Long id;

    @ApiModelProperty(value = "奖励方案id")
    private String rewardId;

    @ApiModelProperty(value = "会员类型ID")
    private Long memberTypeId;

    @ApiModelProperty(value = "会员等级ID")
    private String memberLevelId;


    @ApiModelProperty(value = "条件升级等级ID")
    private String upMemberLevelId;

    @ApiModelProperty(value = "条件直推指标")
    private Integer directMemberCount;

    @ApiModelProperty(value = "订单会员类型")
    private List<String>orderMemberTypeList;


    @ApiModelProperty(value = "收益会员等级")
    private List<String> memberLevelList;

    @ApiModelProperty(value = "条件升级等级")
    private List<String> upMemberLevelList;

    @ApiModelProperty(value = "条件直推等级")
    private List<String> directMemberLevelList;

    @ApiModelProperty(value = "条件直推等级")
    private String directMemberLevelIds;

    @ApiModelProperty(value = "奖励方案明细分佣商品")
    private List<RewardSchemeDetProductVo> rewardSchemeDetProductList;

    @ApiModelProperty(value = "奖励方案明细非分佣商品")
    private List<RewardSchemeDetNonProductVo>rewardSchemeDetNonProductList;


    @ApiModelProperty(value = "业务员分佣:0-否，1-是")
    private Integer salesmanFlag;

    @ApiModelProperty(value = "购买类型:1-首单，2-复购")
    private Integer buyType;

    @ApiModelProperty(value = "奖励类型：1->佣金；2->提成；3->平级；4.级差；5.团队；6.循环分佣")
    private Integer rewardType;

    @ApiModelProperty(value = "级差计算方式:1-订单总额，2-分佣率")
    private Integer differCalType;

    @ApiModelProperty(value = "分佣标题")
    private String commissionTitle;

    @ApiModelProperty(value = "一级下级比例")
    private String oneCommissionRate;

    @ApiModelProperty(value = "一级下级固定金额")
    private String oneCommissionAmount;

    @ApiModelProperty(value = "二级下级比例")
    private String twoCommissionRate;

    @ApiModelProperty(value = "二级下级固定金额")
    private String twoCommissionAmount;


    @ApiModelProperty(value = "自身下单分佣:0-否，1-是")
    private Integer selfCommission;

    @ApiModelProperty(value = "分佣金额来源:1-订单实际支付金额，2-佣金金额，3-订单金额")
    private Integer commissionAmountSource;

    @ApiModelProperty(value = "奖励方案明细分佣商品名称")
    private String rewardSchemeDetProductName;

    @ApiModelProperty(value = "奖励方案明细非分佣商品名称")
    private  String rewardSchemeDetNonProductName;

    @ApiModelProperty(value = "订单会员类型ID")
    private String orderMemberTypeIds;

//    @ApiModelProperty(value = "商品id")
//    private String productId;
//
//    @ApiModelProperty(value = "商品编码")
//    private String goodsCode;
//
//    @ApiModelProperty(value = "商品名称")
//    private String productName;
//
//    @ApiModelProperty(value = "规格id")
//    private String skuId;
//
//    @ApiModelProperty(value = "商品规格")
//    private String skuStock;
//
//    @ApiModelProperty(value = "价格类型：1->会员价；2->复购价；3->实售价")
//    private Integer priceType;




//    @ApiModelProperty(value = "明细备注")
//    private String remark;

//    @ApiModelProperty(value = "提成比例")
//    private BigDecimal royaltyRate;
//
//    @ApiModelProperty(value = "提成固定金额")
//    private BigDecimal royaltyAmount;

}
