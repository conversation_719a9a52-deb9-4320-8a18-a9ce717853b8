package com.medusa.gruul.goods.api.model.dto.manager;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.core.param.QueryParam;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;


/**
 * @Description: 商品库存
 * @Author: qsx
 * @Date:   2022-02-28
 * @Version: V1.0
 */
@Data
@ApiModel(value="ProductStockDto对象", description="商品库存")
public class ProductStockDto  extends QueryParam {
    
	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
	private Long id;
	/**创建人Id*/
    @ApiModelProperty(value = "创建人Id")
	private Long createUserId;
	/**修改人Id*/
    @ApiModelProperty(value = "修改人Id")
	private Long updateUserId;
	/**删除状态：0->未删除；1->已删除*/
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
	private Integer isDeleted;
	/**仓库Id*/
    @ApiModelProperty(value = "仓库Id")
	private Long warehouseId;
    @ApiModelProperty(value = "仓库Id")
    private List<Long> warehouseIdList;
	/**规格Id*/
    @ApiModelProperty(value = "规格Id")
	private Long skuId;
    /**商品规格*/
    @ApiModelProperty(value = "商品规格")
    private String specs;
    /**商品展示分类id*/
    @ApiModelProperty(value = "商品展示分类id")
    private List<Long> showCategoryIdList;
    /**搜索字符串-商品名字或者商品编码*/
    @ApiModelProperty(value = "搜索字符串-商品名字或者商品编码")
    private String keyword;
    /**商品状态*/
    @ApiModelProperty(value = "商品状态 -1--全部 0--下架（仓库中），1--上架")
    private int status;
    /**库存状态*/
    @ApiModelProperty(value = "库存状态 1--大于上限 2--小于下限")
    private int stockStatus;
	/**库存*/
    @ApiModelProperty(value = "库存")
	private java.math.BigDecimal stock;
	/**预警库存*/
    @ApiModelProperty(value = "预警库存")
	private Integer lowStock;
	/**产品Id*/
    @ApiModelProperty(value = "产品Id")
	private Long productId;
	/**备注*/
    @ApiModelProperty(value = "备注")
	private String remark;
    /**库存下限*/
    @ApiModelProperty(value = "库存下限")
    private BigDecimal lowerLimit;
    /**库存上限*/
    @ApiModelProperty(value = "库存上限")
    private BigDecimal upperLimit;
    @ApiModelProperty(value = "商品标识")
    private String goodsCode;
    @ApiModelProperty(value = "仓库标识")
    private String stockCode;
    /**商品规格2-通讯行业指颜色*/
    @ApiModelProperty(value = "商品规格2-通讯行业指颜色")
    private String specs2;
    /**关联商品名称*/
    @ApiModelProperty(value = "关联商品名称")
    private String linkGoodsName;
}
