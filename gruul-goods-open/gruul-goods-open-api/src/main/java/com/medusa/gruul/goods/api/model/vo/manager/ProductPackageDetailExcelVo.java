package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:38 2024/9/20
 */
@Data
@ApiModel(value = "ProductPackageDetailExcelVo", description = "权益包明细查询返回信息")
public class ProductPackageDetailExcelVo implements Serializable {

    private final static long serialVersionUID = 1L;

    /**
     * 权益包名称
     */
    @ApiModelProperty(value = "权益包名称")
    private String packageName;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String productName;

    /**
     * 商品规格
     */
    @ApiModelProperty(value = "商品规格")
    private String skuName;

    /**
     * 权益包展示开始时间
     */
    @ApiModelProperty(value = "权益包展示开始时间")
    private String packageShowStartTime;

    /**
     * 权益包展示结束时间
     */
    @ApiModelProperty(value = "权益包展示结束时间")
    private String packageShowEndTime;

    /**
     * 权益包开始时间
     */
    @ApiModelProperty(value = "权益包开始时间")
    private String packageStartTime;
    /**
     * 权益包结束时间
     */
    @ApiModelProperty(value = "权益包结束时间")
    private String packageEndTime;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer useNumber;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal productPrice;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal productAmount;

    /**
     * 实售价
     */
    @ApiModelProperty(value = "实售价")
    private BigDecimal packagePrice;

    /**
     * 是否互斥商品->0.否，1.是
     */
//    private Integer mutexFlag;

    @ApiModelProperty(value = "互斥商品")
    private String mutexFlag;

    /**
     * 无期限->0.否;1.是
     */
//    private Integer notTerm;
    @ApiModelProperty(value = "无期限")
    private String notTerm;

    /**
     * 不限次数->0.否;1.是
     */
//    private Integer notTime;

    @ApiModelProperty(value = "不限次数")
    private String notTime;


    /**
     * 成本价格
     */
    @ApiModelProperty(value = "成本价格")
    private BigDecimal costPrice;

    /**
     * 调后价格
     */
    @ApiModelProperty(value = "调后价格")
    private BigDecimal adjustmentPrice;

    /**
     * 使用天数
     */
    @ApiModelProperty(value = "使用天数")
    private Integer useDays;

    /**
     * 单次频率天数，间隔多少天使用一次
     */
    @ApiModelProperty(value = "单次频率天数")
    private Integer daysRate;
}
