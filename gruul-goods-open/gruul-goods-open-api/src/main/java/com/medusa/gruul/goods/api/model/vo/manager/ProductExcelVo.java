package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


@Data
@ApiModel(value = "ProductExcelVo对象", description = "商品导出返回信息")
public class ProductExcelVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "评分")
    private BigDecimal score;

    private Integer status;

    @ApiModelProperty(value = "状态")
    private String statusDict;

    @ApiModelProperty(value = "专区")
    private String saleModeName;

    // 价格相关
    List<SkuStockMemberPriceVo> skuStockMemberPriceVos;

    @ApiModelProperty(value = "价格区间")
    private String priceRange;


    private LocalDateTime packageStartTime;

    private LocalDateTime packageEndTime;

}