package com.medusa.gruul.goods.api.model.dto.api;

import com.medusa.gruul.goods.api.model.vo.manager.ItemVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: rbw
 * @Description: TODO
 */
@Data
@ApiModel(value = "用户下单选择的优惠券Dto")
public class MiniOrderCouponDto implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("是否代客户下单：0.否；1.是")
    private Integer replaceCreateOrderFlag;

    @ApiModelProperty("用户id")
    private String userId;

    /**
     * 用户下单商品详情
     */
    private List<ItemVo> data;

    @ApiModelProperty(value = "用户优惠券id")
    private Long miniAccountCouponId;


}
