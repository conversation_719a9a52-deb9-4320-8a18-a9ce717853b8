package com.medusa.gruul.goods.api.model.vo.manager;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * @Description: 购货入库明细所有信息，包括购货入库主表信息
 * @Author: qsx
 * @Date:   2022-03-08
 * @Version: V1.0
 */
@Data
@ApiModel(value="ProductBuyInItemAllVo")
public class ProductBuyInItemAllVo implements Serializable {

    /**id*/
    @ApiModelProperty(value = "id")
    private Long id;
    /**创建用户id*/
    @ApiModelProperty(value = "创建用户id")
    private Long createUserId;
    /**修改用户id*/
    @ApiModelProperty(value = "修改用户id")
    private Long updateUserId;
    /**删除状态：0->未删除；1->已删除*/
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
    private Integer isDeleted;
    /**商品Id*/
    @ApiModelProperty(value = "商品Id")
    private Long productId;
    /**商品名*/
    @ApiModelProperty(value = "商品名")
    private String productName;
    /**基本单位*/
    @ApiModelProperty(value = "基本单位")
    private String unit;
    /**规格id*/
    @ApiModelProperty(value = "规格id")
    private Long skuId;
    /**规格名*/
    @ApiModelProperty(value = "规格名")
    private String skuSpecs;
    /**入库单id*/
    @ApiModelProperty(value = "入库单id")
    private Long productBuyInId;
    /**入库数量*/
    @ApiModelProperty(value = "入库数量")
    private java.math.BigDecimal instoreQty;
    /**入库单价*/
    @ApiModelProperty(value = "入库单价")
    private java.math.BigDecimal instorePrice;
    /**入库金额*/
    @ApiModelProperty(value = "入库金额")
    private java.math.BigDecimal instoreAmount;
    /**明细备注*/
    @ApiModelProperty(value = "明细备注")
    private String itemRemarks;


    @ApiModelProperty(value = "商品基本单位id")
    private Long unitId;

    @ApiModelProperty(value = "商品规格2-通讯行业指颜色")
    private String specs2;

    /**仓库id*/
    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;
    /**入库日期*/
    @ApiModelProperty(value = "入库日期")
    private Date buyDate;
    /**入库单号*/
    @ApiModelProperty(value = "入库单号")
    private String buyNo;
    /**入库类型*/
    @ApiModelProperty(value = "入库类型")
    private String buyType;
    /**经办人id*/
    @ApiModelProperty(value = "经办人id")
    private Long handlerId;
    /**经办人名字*/
    @ApiModelProperty(value = "经办人名字")
    private String handlerName;
    /**制单人id*/
    @ApiModelProperty(value = "制单人id")
    private Long preparerId;
    @ApiModelProperty(value = "制单人")
    private String preparerName;
    /**供应商id*/
    @ApiModelProperty(value = "供应商id")
    private Long supplierId;
    @ApiModelProperty(value = "供应商名")
    private String supplierName;
    /**仓库*/
    @ApiModelProperty(value = "仓库")
    private String warehouse;
    /**入库类型名*/
    @ApiModelProperty(value = "入库类型名")
    private String buyTypeName;
    /**备注*/
    @ApiModelProperty(value = "备注")
    private String remarks;
}
