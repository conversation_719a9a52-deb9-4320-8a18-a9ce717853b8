package com.medusa.gruul.goods.api.model.param.manager;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:18 2024/9/2
 */
@Data
@ApiModel(value = "AddPackageProductParam对象", description = "权益包选择商品查询参数")
public class AddPackageProductParam extends QueryParam {
    private static final long serialVersionUID = 1;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "专区名称")
    private String modeName;

    @ApiModelProperty(value = "商品规格ids")
    private List<String> skuIds;

    /**
     * 状态(默认上架，0--下架，1--上架)
     */
    @ApiModelProperty(value = "状态(默认上架，0--下架，1--上架)")
    private String status;

    @ApiModelProperty(value = "展示分类")
    private String showCategoryId;

    @ApiModelProperty(value = "指定参与商家:0-否，1-参与商家，2-不参与商家")
    private Integer shopsFlag;

    @ApiModelProperty(value = "商家ids")
    private List<String>shopIds;

}
