package com.medusa.gruul.goods.api.model.vo.manager;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:47 2024/9/5
 */
@Data
@ApiModel(value = "ProductAllPackageVo对象", description = "权益包商品查询返回信息")
public class ProductAllPackageVo implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "权益包商品id")
    private Long packageId;

    @ApiModelProperty(value = "权益包商品名称")
    private String packageName;

    @ApiModelProperty(value = "包含商品id")
    private Long productId;

    @ApiModelProperty(value = "包含商品名称")
    private String productName;

    @ApiModelProperty(value = "包含商品规格id")
    private Long skuId;

    @ApiModelProperty(value = "包含商品规格名称")
    private String skuName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
     * 使用数量
     */
    @ApiModelProperty(value = "使用数量")
    private Integer useNumber;

    /**
     * 权益包互斥商品唯一id
     */
    @ApiModelProperty(value = "权益包互斥商品唯一id")
    private String mutexGoodId;

    /**
     * 实售价
     */
    @ApiModelProperty(value = "实售价")
    private BigDecimal price;

    /**
     * 指导价（划线价）
     */
    @ApiModelProperty(value = "指导价（划线价）")
    private BigDecimal originalPrice;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 无期限->0.否;1.是
     */
    @ApiModelProperty(value = "无期限->0.否;1.是")
    private Integer notTerm;

    /**
     * 不限次数->0.否;1.是
     */
    @ApiModelProperty(value = "不限次数->0.否;1.是")
    private Integer notTime;
    /**
     * 是否互斥商品->0.否，1.是
     */
    @ApiModelProperty(value = "是否互斥商品->0.否，1.是")
    private Integer mutexFlag;

    /**
     * 成本价格
     */
    @ApiModelProperty(value = "成本价格")
    private BigDecimal costPrice;

    /**
     * 调后价格
     */
    @ApiModelProperty(value = "调后价格")
    private BigDecimal adjustmentPrice;

    /**
     *  使用天数
     */
    @ApiModelProperty(value = "使用天数")
    private Integer useDays;

    /**
     * 单次频率天数，间隔多少天使用一次
     */
    @ApiModelProperty(value = "单次频率天数")
    private Integer daysRate;
}
