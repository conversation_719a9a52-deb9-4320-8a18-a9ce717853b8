package com.medusa.gruul.goods.api.model.dto.manager;

import cn.hutool.core.bean.BeanUtil;
import com.medusa.gruul.common.core.param.QueryParam;
import com.medusa.gruul.goods.api.entity.SkuStock;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 商品sku
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-06
 */
@Data
@ApiModel(value = "新增或修改商品sku DTO")
public class SkuStockWtoDto extends QueryParam {

    private Long id;

    @ApiModelProperty(value = "版本号")
    private Long version;

    @ApiModelProperty(value = "产品id")
    private Long productId;


    @ApiModelProperty(value = "sku编码")
    private String skuCode;


    @ApiModelProperty(value = "商品规格")
    private String specs;

    @ApiModelProperty(value = "商品sku重量")
    private BigDecimal weight;


    @ApiModelProperty(value = "展示图片")
    private String pic;


    @ApiModelProperty(value = "实售价")
    private BigDecimal price;


    @ApiModelProperty(value = "指导价（划线价）")
    private BigDecimal originalPrice;


    @ApiModelProperty(value = "库存")
    private Integer stock;


    @ApiModelProperty(value = "预警库存")
    private Integer lowStock;

    @ApiModelProperty(value = "销量")
    private Integer sale;


    @ApiModelProperty(value = "限购数量")
    private Integer perLimit;
    /**商品名字*/
    @ApiModelProperty(value = "商品名字")
    private String goodsName;
    /**基本单位*/
    @ApiModelProperty(value = "基本单位")
    private String unit;
    /**商品编码*/
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;
    /**仓库Id*/
    @ApiModelProperty(value = "仓库Id")
    private Long warehouseId;
    /**分类Id*/
    @ApiModelProperty(value = "分类Id")
    private Long showCategoryId;
    /**库存下限*/
    @ApiModelProperty(value = "库存下限")
    private BigDecimal lowerLimit;
    /**库存上限*/
    @ApiModelProperty(value = "库存上限")
    private BigDecimal upperLimit;

    @ApiModelProperty(value = "规格入库单价")
    private BigDecimal skuPrice;

    public SkuStock coverSkuStock() {
        SkuStock skuStock = new SkuStock();
        BeanUtil.copyProperties(this, skuStock);
        return skuStock;
    }
}