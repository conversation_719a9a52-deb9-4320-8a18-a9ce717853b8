package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 商品Sku查询
 * @since 2019-10-06
 */
@Data
@ApiModel(value = "SkuStockVo对象", description = "商品Sku查询返回信息")
public class SkuStockVo implements Serializable {

    private Long id;

    @ApiModelProperty(value = "版本号", example = "1")
    private Long version;

    @ApiModelProperty(value = "产品id", example = "1")
    private Long productId;

    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @ApiModelProperty(value = "商品规格")
    private String specs;

    @ApiModelProperty(value = "商品sku重量")
    private BigDecimal weight;

    @ApiModelProperty(value = "展示图片")
    private String pic;

    @ApiModelProperty(value = "实售价")
    private BigDecimal price;

    @ApiModelProperty(value = "指导价（划线价）")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "库存", example = "100")
    private BigDecimal stock;

    @ApiModelProperty(value = "预警库存", example = "10")
    private Integer lowStock;

    @ApiModelProperty(value = "销量", example = "50")
    private Integer sale;

    @ApiModelProperty(value = "限购数量", example = "10")
    private Integer perLimit;
    /**仓库全称*/
    @ApiModelProperty(value = "仓库全称")
    private String warehouseFullName;
    /**商品规格库存*/
    @ApiModelProperty(value = "商品规格库存", example = "100")
    private java.math.BigDecimal productStock;
    /**商品名字*/
    @ApiModelProperty(value = "商品名字")
    private String goodsName;
    /**基本单位*/
    @ApiModelProperty(value = "基本单位")
    private String unit;
    /**商品编码*/
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;
    /**仓库Id*/
    @ApiModelProperty(value = "仓库Id")
    private Long warehouseId;
    /**商品库存Id*/
    @ApiModelProperty(value = "商品库存Id")
    private Long productStockId;
    /**库存下限*/
    @ApiModelProperty(value = "库存下限")
    private BigDecimal lowerLimit;
    /**库存上限*/
    @ApiModelProperty(value = "库存上限")
    private BigDecimal upperLimit;
    /**规格入库单价*/
    @ApiModelProperty(value = "规格入库单价")
    private BigDecimal skuPrice;

    @ApiModelProperty(value = "商品规格2-通讯行业指颜色")
    private String specs2;

    @ApiModelProperty(value = "关联产品id")
    private Long linkProductId;

    @ApiModelProperty(value = "关联skuId")
    private Long linkSkuId;

    @ApiModelProperty(value = "关联产品名称")
    private String linkGoodsName;

    @ApiModelProperty(value = "实际出库数量")
    private Integer reallyOutStock;
}