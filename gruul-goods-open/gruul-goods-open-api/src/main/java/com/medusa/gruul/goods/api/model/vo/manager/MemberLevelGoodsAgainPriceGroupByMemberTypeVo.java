package com.medusa.gruul.goods.api.model.vo.manager;

import com.medusa.gruul.goods.api.entity.MemberLevelGoodsAgainPrice;
import com.medusa.gruul.goods.api.entity.MemberLevelGoodsPrice;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:39 2025/5/22
 */
@Data
@ApiModel(value = "MemberLevelGoodsPriceGroupByMemberTypeVo", description = "复购价根据会员类型分组Vo")
public class MemberLevelGoodsAgainPriceGroupByMemberTypeVo {

    @ApiModelProperty(value = "会员类型id")
    private Long memberTypeId;

    @ApiModelProperty(value = "会员类型名称")
    private String memberTypeName;

    @ApiModelProperty(value = "复购价列表")
    private List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPriceList;

}
