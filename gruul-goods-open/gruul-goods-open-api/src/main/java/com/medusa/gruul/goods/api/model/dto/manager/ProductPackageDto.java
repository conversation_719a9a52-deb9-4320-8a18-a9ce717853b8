package com.medusa.gruul.goods.api.model.dto.manager;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:06 2024/9/3
 */
@Data
@ApiModel(value = "新增或修改权益包商品DTO")
public class ProductPackageDto {

    private Long id;

    /**
     * 产品id
     */
    @NotNull
    @ApiModelProperty(value = "产品id")
    private Long productId;

    /**
     * 规格id
     */
    @NotNull
    @ApiModelProperty(value = "规格id")
    private Long skuId;


    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 使用数量
     */
    @ApiModelProperty(value = "使用数量")
    private Integer useNumber;

    /**
     * 权益包互斥商品唯一id
     */
    @ApiModelProperty(value = "权益包互斥商品唯一id")
    private String mutexGoodId;

    /**
     * 实售价
     */
    @ApiModelProperty(value = "实售价")
    private BigDecimal price;

    /**
     * 指导价（划线价）
     */
    @ApiModelProperty(value = "指导价（划线价）")
    private BigDecimal originalPrice;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;


    @ApiModelProperty(value = "无期限")
    private Boolean notTerm;


    @ApiModelProperty(value = "不限次数")
    private Boolean notTime;

    /**
     * 是否互斥商品->0.否，1.是
     */
    @ApiModelProperty(value = "是否互斥商品->0.否，1.是")
    private Integer mutexFlag;

    /**
     * 成本价格
     */
    @ApiModelProperty(value = "成本价格")
    private BigDecimal costPrice;

    /**
     * 调后价格
     */
    @ApiModelProperty(value = "调后价格")
    private BigDecimal adjustmentPrice;



    /**
     * 使用天数
     */
    @ApiModelProperty(value = "使用天数")
    private Integer useDays;


    /**
     * 单次频率天数，间隔多少天使用一次
     */
    @ApiModelProperty(value = "单次频率天数")
    private Integer daysRate;
}
