package com.medusa.gruul.goods.api.model.param.manager;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: 积分商城查询参数
 * @Date: Created in 10:33 2023/8/21
 */
@Data
public class IntegralProductParam {

    /**
     * 积分活动id
     */
    @ApiModelProperty(value = "积分活动id")
    @NotNull(message = "积分活动id不能为空")
    private Long activityId;

}
