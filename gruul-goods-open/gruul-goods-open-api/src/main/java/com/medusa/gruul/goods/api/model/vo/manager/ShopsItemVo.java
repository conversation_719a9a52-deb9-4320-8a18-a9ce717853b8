package com.medusa.gruul.goods.api.model.vo.manager;

import com.medusa.gruul.goods.api.entity.Warehouse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: 所选商品详情-多店铺
 * @Date: Created in 15:45 2023/9/28
 */
@Data
@ApiModel(value = "所选商品详情-多店铺")
public class ShopsItemVo {

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "所选商品详情")
    List<ItemVo>itemVos;

    @ApiModelProperty(value = "仓库")
    private Warehouse warehouse;
}
