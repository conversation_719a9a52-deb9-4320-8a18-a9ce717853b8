package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: 积分商品列表vo
 * @Date: Created in 14:43 2023/9/8
 */
@Data
@ApiModel(value = "IntegralProductListVo", description = "积分商品列表vo")
public class IntegralProductListVo {

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "商品规格")
    private String specs;

    @ApiModelProperty(value = "商品图片地址")
    private String pic;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "方案名称")
    private String activityName;

    @ApiModelProperty(value = "总数量")
    private BigDecimal allExchangeNum;

    @ApiModelProperty(value = "已兑换数量")
    private BigDecimal alreadyExchangeNum;

    @ApiModelProperty(value = "状态：0.待发布；1.进行中；2.已完成；3.已停用")
    private Integer projectStatus;

    @ApiModelProperty(value = "所需积分")
    private BigDecimal integral;

    @ApiModelProperty(value = "所需金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "变更状态;0.否；1.是")
    private BigDecimal changeFlag;

}
