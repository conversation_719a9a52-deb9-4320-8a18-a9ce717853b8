package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 评价管理导出Excel VO类
 */
@Data
@ApiModel(value = "商品库存导出Excel VO类")
public class ProductStockExcelVo {

  /*  @ApiModelProperty(value = "序号")
    private Long index;*/

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品规格")
    private String specs;

    @ApiModelProperty(value = "商品规格2")
    private String specs2;

    @ApiModelProperty(value = "关联商品名称")
    private String linkGoodsName;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "所属仓库")
    private String warehouseFullName;

    @ApiModelProperty(value = "可用库存量")
    private BigDecimal stock;

    @ApiModelProperty(value = "库存上限")
    private BigDecimal upperLimit;

    @ApiModelProperty(value = "库存下限")
    private BigDecimal lowerLimit;

}
