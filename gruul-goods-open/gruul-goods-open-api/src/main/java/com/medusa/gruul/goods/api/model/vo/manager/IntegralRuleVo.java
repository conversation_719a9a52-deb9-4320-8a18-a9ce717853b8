package com.medusa.gruul.goods.api.model.vo.manager;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: 积分规则Vo
 * @Date: Created in 15:39 2023/8/18
 */
@Data
public class IntegralRuleVo {

    @ApiModelProperty(value = "积分规则id")
    private Long id;

    /**
     * 规则类型，0：积分规则说明：1：消费获积分；2：发展下级获取积分;3:新用户注册获取积分;4:每天登录获取积分;5:购买通惠证获取积分;6.下级下单获积分
     */
    @ApiModelProperty(value = "规则类型，0：积分规则说明：1：消费获积分；2：发展下级获取积分;3:新用户注册获取积分;4:每天登录获取积分;5:购买通惠证获取积分;6.下级下单获积分")
    private Integer ruleType;

    /**
     * 规则名字
     */
    @ApiModelProperty(value = "规则名字")
    private String ruleName;

    /**
     * 积分
     */
    @ApiModelProperty(value = "积分")
    private BigDecimal integral;

    /**
     * 上级获取积分
     */
    @ApiModelProperty(value = "上级获取积分")
    private BigDecimal parentIntegral;

    /**
     * 上上级获取积分
     */
    @ApiModelProperty(value = "上上级获取积分")
    private BigDecimal aboveParentIntegral;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 启用状态：1.是；0.否
     */
    @ApiModelProperty(value = "启用状态：1.是；0.否")
    private Integer enableStatus;

    /**
     * 积分规则说明
     */
    @ApiModelProperty(value = "积分规则说明")
    private String description;

}
