package com.medusa.gruul.goods.api.model.vo.manager;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: 积分活动vo
 * @Date: Created in 18:02 2023/8/18
 */
@Data
@ApiModel(value = "IntegralActivityVo", description = "积分活动vo")
public class IntegralActivityVo {


    @ApiModelProperty(value = "积分活动id")
    private Long id;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 活动图片
     */
    @ApiModelProperty(value = "活动图片")
    private String activityPic;


    /**
     * 方案状态：0.待发布；1.进行中；2.已完成；3.已停用
     */
    @ApiModelProperty(value = "方案状态：0.待发布；1.进行中；2.已完成；3.已停用")
    private Integer projectStatus;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
