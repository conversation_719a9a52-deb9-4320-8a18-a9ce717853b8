package com.medusa.gruul.goods.api.model.vo.manager;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: 可添加商品vo
 * @Date: Created in 9:04 2023/8/22
 */
@Data
@ApiModel(value = "AddProductVo", description = "可添加商品vo")
public class AddProductVo {

    @ApiModelProperty(value = "商品id")
    private String productId;
    @ApiModelProperty(value = "商品名称")
    private String name;
    @ApiModelProperty(value = "规格id")
    private String skuId;
    @ApiModelProperty(value = "规格名称")
    private String skuStock;
    @ApiModelProperty(value = "店铺名称")
    private String shopName;
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;
    @ApiModelProperty(value = "实售价")
    private BigDecimal price;

}
