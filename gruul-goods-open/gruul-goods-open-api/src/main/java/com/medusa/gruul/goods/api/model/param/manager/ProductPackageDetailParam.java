package com.medusa.gruul.goods.api.model.param.manager;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:07 2024/9/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ProductPackageDetailParam对象", description = "权益包明细查询参数")
public class ProductPackageDetailParam extends QueryParam {

    /**
     * 权益包名称
     */
    @ApiModelProperty(value = "权益包名称")
    private String packageName;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "状态(默认上架，0--下架，1--上架 )")
    private Integer packageStatus;

    @ApiModelProperty(value = "创建开始时间")
    private String createBeginTime;

    @ApiModelProperty(value = "创建结束时间")
    private String createEndTime;


}
