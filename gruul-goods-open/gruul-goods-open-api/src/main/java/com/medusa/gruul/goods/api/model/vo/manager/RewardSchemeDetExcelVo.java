package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:
 * @Description: 奖励方案明细导出Excel VO
 * @Date: Created in 2025/7/5
 */
@Data
@ApiModel(value = "RewardSchemeDetExcelVo", description = "奖励方案明细导出Excel VO")
public class RewardSchemeDetExcelVo {

    @ApiModelProperty(value = "序号")
    private Integer index;

    @ApiModelProperty(value = "单据编号")
    private String billNo;

    @ApiModelProperty(value = "单据日期")
    private String billDate;

    @ApiModelProperty(value = "有效期")
    private String expirationTime;

    @ApiModelProperty(value = "方案名称")
    private String name;

    @ApiModelProperty(value = "经手人")
    private String userName;

    @ApiModelProperty(value = "制单人")
    private String createUserName;

    @ApiModelProperty(value = "主表备注")
    private String remark;

    @ApiModelProperty(value = "奖励类型")
    private String rewardType;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "价格类型")
    private String priceType;

    @ApiModelProperty(value = "会员等级")
    private String memberLevelName;

    @ApiModelProperty(value = "明细备注")
    private String detRemark;
}
