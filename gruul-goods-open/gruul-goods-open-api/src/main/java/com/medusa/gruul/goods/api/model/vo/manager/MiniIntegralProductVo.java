package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: 小程序积分商品vo
 * @Date: Created in 19:12 2023/8/23
 */
@Data
public class MiniIntegralProductVo {

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String productId ;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String productName;
    /**
     * 图片地址
     */
    @ApiModelProperty(value = "图片地址")
    private String pic;

    /**
     * 总兑数量
     */
    @ApiModelProperty(value = "总兑数量")
    private BigDecimal allExchangeNum;

    /**
     * 用户可兑数量
     */
    @ApiModelProperty(value = "用户可兑数量")
    private BigDecimal alreadyExchangeNum;

    /**
     * 所需积分
     */
    @ApiModelProperty(value = "所需积分")
    private BigDecimal integral;


    /**
     * 所需金额
     */
    @ApiModelProperty(value = "所需金额")
    private BigDecimal amount;

    /**
     * 库存
     */
    @ApiModelProperty(value = "库存")
    private BigDecimal stock;


}
