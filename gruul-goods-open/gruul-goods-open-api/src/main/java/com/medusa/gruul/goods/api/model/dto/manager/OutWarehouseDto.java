package com.medusa.gruul.goods.api.model.dto.manager;

import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * @Description: 仓库信息
 * @Author: qsx
 * @Date:   2022-02-25
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="接收外部接口仓库对象", description="仓库信息")
public class OutWarehouseDto{

    @ApiModelProperty(value = "id")
	private Long id;
	/**创建用户id*/
    @ApiModelProperty(value = "创建用户id")
	private Long createUserId;
	/**修改用户id*/
    @ApiModelProperty(value = "修改用户id")
	private Long updateUserId;
	/**删除状态：0->未删除；1->已删除*/
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
	private Integer isDeleted;
	/**状态：0->未启用；1->已启用*/
    @ApiModelProperty(value = "状态：-1->未启用；0->已启用")
	private Integer state;
	/**仓库全称*/
    @ApiModelProperty(value = "仓库全称")
	private String warehouseFullName;
	/**仓库编号*/
    @ApiModelProperty(value = "仓库编号")
	private String warehouseNumber;
	/**仓库地址*/
    @ApiModelProperty(value = "仓库地址")
	private String warehouseAddress;
    /**
     * 默认仓库
     */
    @ApiModelProperty(value = "是否默认仓库，0否1是")
    private Integer stockFlag;

    /**
     * 仓库classCode
     */
    @ApiModelProperty(value = "仓库classCode")
    private String classCode;
}
