package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:58 2025/4/1
 */
@Data
@ApiModel(value = "CategoryVo", description = "优惠券添加分类vo")
public class CategoryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分类id")
    private String categoryId;

    @ApiModelProperty(value = "商品专区")
    private String modeName;

    @ApiModelProperty(value = "商品一级分类")
    private String categoryParentName;

    @ApiModelProperty(value = "商品二级分类")
    private String categoryName;

}
