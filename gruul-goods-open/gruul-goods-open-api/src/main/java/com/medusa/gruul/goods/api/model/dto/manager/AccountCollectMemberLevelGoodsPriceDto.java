package com.medusa.gruul.goods.api.model.dto.manager;

import cn.hutool.core.bean.BeanUtil;
import com.medusa.gruul.common.core.param.QueryParam;
import com.medusa.gruul.goods.api.entity.MemberLevelGoodsPrice;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <p>
 * 商品会员价格
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
@Data
@ApiModel(value = "新增或修改商品会员价格DTO")
public class AccountCollectMemberLevelGoodsPriceDto extends QueryParam {

    private Long id;

    @ApiModelProperty(value = "商品id")
    @NotBlank(message = "商品id不能为空！")
    private Long productId;

    @ApiModelProperty(value = "商品名称")
    @NotBlank(message = "商品名称不能为空！")
    private String goodsName;


    @ApiModelProperty(value = "会员等级价格")
    private BigDecimal memberLevelPrice;

    @ApiModelProperty(value = "商品实际售价")
    private BigDecimal productPrice;

    @ApiModelProperty(value = "指导价划线价")
    private BigDecimal originalPrice;


    public MemberLevelGoodsPrice coverMemberGoodsPrice() {
        MemberLevelGoodsPrice memberLevelGoodsPrice = new MemberLevelGoodsPrice();
        BeanUtil.copyProperties(this, memberLevelGoodsPrice);
        return memberLevelGoodsPrice;
    }
}