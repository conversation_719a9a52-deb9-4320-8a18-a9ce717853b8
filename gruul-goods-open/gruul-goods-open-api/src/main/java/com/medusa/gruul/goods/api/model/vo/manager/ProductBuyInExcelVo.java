package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 评价管理导出Excel VO类
 */
@Data
@ApiModel(value = "商品入库导出Excel VO类")
public class ProductBuyInExcelVo {

    @ApiModelProperty(value = "单号")
    private String buyNo;

    @ApiModelProperty(value = "入库日期")
    private String buyDate;

    @ApiModelProperty(value = "所属仓库")
    private String warehouse;

    @ApiModelProperty(value = "类型")
    private String buyTypeName;

    @ApiModelProperty(value = "制单人")
    private String preparerName;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal buyQuantity;

    @ApiModelProperty(value = "入库金额")
    private BigDecimal buyAmount;

    @ApiModelProperty(value = "备注")
    private String remarks;
}
