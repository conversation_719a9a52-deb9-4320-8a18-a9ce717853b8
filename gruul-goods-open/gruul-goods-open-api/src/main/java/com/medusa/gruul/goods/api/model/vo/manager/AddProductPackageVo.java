package com.medusa.gruul.goods.api.model.vo.manager;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:55 2024/9/2
 */
@Data
public class AddProductPackageVo {

    @ApiModelProperty(value = "商品id")
    private String productId;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "规格名称")
    private String specs;

    @ApiModelProperty(value = "实售价")
    private BigDecimal price;

    @ApiModelProperty(value = "指导价（划线价）")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "专区名称")
    private String modeName;

    @ApiModelProperty(value = "规格id")
    private String skuId;

    @ApiModelProperty(value = "商品分类")
    private String categoryName;

    @ApiModelProperty(value = "状态(默认上架，0--下架，1--上架)")
    private Integer status;

    @ApiModelProperty(value = "库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "商品图片")
    private String pic;

    @ApiModelProperty(value = "商家")
    private String shopName;
}
