package com.medusa.gruul.goods.api.model.vo.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: 小程序会员购物车查询返回信息-多店铺
 * @Date: Created in 15:57 2023/9/14
 */
@Data
@ApiModel(value = "ApiShoppingCartByShopsVo对象", description = "小程序会员购物车查询返回信息-多店铺")
public class ApiShoppingCartByShopsVo {

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "店铺下购物车商品列表")
    List<ApiShoppingCartVo>apiShoppingCartVos;

}
