package com.medusa.gruul.goods.api.model.dto.manager;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: plh
 * @Description: 积分活动Dto
 * @Date: Created in 17:54 2023/8/18
 */
@Data
public class IntegralActivityDto {


    @ApiModelProperty(value = "积分活动id")
    private Long id;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    @NotBlank(message = "活动名称不能为空")
    private String activityName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;

    /**
     * 活动图片
     */
    @ApiModelProperty(value = "活动图片")
    private String activityPic;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 积分商品列表
     */
    @ApiModelProperty(value = "积分商品列表")
    @NotNull(message = "积分商品列表不能为空")
    @Valid
    private List<IntegralProductDetDto> list;

}
