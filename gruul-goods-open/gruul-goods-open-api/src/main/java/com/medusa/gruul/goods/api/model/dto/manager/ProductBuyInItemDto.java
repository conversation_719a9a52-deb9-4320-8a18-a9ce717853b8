package com.medusa.gruul.goods.api.model.dto.manager;
import com.medusa.gruul.common.core.param.QueryParam;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * @Description: 购货入库明细
 * @Author: qsx
 * @Date:   2022-03-08
 * @Version: V1.0
 */
@Data
@ApiModel(value="新增或修改t_product_buy_in_detailedDTO")
public class ProductBuyInItemDto extends QueryParam {
    
	/**id*/
    @ApiModelProperty(value = "id")
	private Long id;
	/**创建用户id*/
    @ApiModelProperty(value = "创建用户id")
	private Long createUserId;
	/**修改用户id*/
    @ApiModelProperty(value = "修改用户id")
	private Long updateUserId;
	/**删除状态：0->未删除；1->已删除*/
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
	private Integer isDeleted;
	/**商品Id*/
    @ApiModelProperty(value = "商品Id")
	private Long productId;
	/**规格id*/
    @ApiModelProperty(value = "规格id")
	private Long skuId;
    /**入库单id*/
    @ApiModelProperty(value = "入库单id")
    private Long productBuyInId;
	/**入库数量*/
    @ApiModelProperty(value = "入库数量")
	private java.math.BigDecimal instoreQty;
	/**入库单价*/
    @ApiModelProperty(value = "入库单价")
	private java.math.BigDecimal instorePrice;
	/**入库金额*/
    @ApiModelProperty(value = "入库金额")
	private java.math.BigDecimal instoreAmount;
	/**备注*/
    @ApiModelProperty(value = "备注")
	private String remarks;
    /**上限*/
    @ApiModelProperty(value = "上限")
    private BigDecimal upperLimit;
    /**下限*/
    @ApiModelProperty(value = "下限")
    private BigDecimal lowerLimit;

    @ApiModelProperty(value = "商品基本单位id")
    private Long unitId;

    @ApiModelProperty(value = "商品入库单位id")
    private Long inUnitId;
}
