package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: 积分商品vo
 * @Date: Created in 10:26 2023/8/21
 */
@Data
public class IntegralProductVo {

    @ApiModelProperty(value = "积分商品id")
    private Long id;
    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private Long productId;

    /**
     * 积分活动id
     */
    @ApiModelProperty(value = "积分活动id")
    private Long activityId;

    /**
     * 所需积分
     */
    @ApiModelProperty(value = "所需积分")
    private BigDecimal integral;

    /**
     * 所需金额
     */
    @ApiModelProperty(value = "所需金额")
    private BigDecimal amount;

    /**
     * 总兑数量
     */
    @ApiModelProperty(value = "总兑数量")
    private BigDecimal allExchangeNum;

    /**
     * 用户可兑数量
     */
    @ApiModelProperty(value = "用户可兑数量")
    private BigDecimal userExchangeNum;

    /**
     * 已兑数量
     */
    @ApiModelProperty(value = "已兑数量")
    private BigDecimal alreadyExchangeNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 规格id
     */
    @ApiModelProperty(value = "规格id")
    private String skuId;

    /**
     * 是否可以编辑
     */
    @ApiModelProperty(value = "是否可以编辑：0可编辑；1不可编辑")
    private Integer isEdit;

}
