package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 权益包列表
 */
@Data
@ApiModel(value = "ProductPackageExcelVo", description = "权益包导出返回信息")
public class ProductPackageExcelVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "权益包名称")
    private String name;

    @ApiModelProperty(value = "价格区间")
    private String priceRange;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "使用期限")
    private String durationTime;

    @ApiModelProperty(value = "状态")
    private String statusDict;


}