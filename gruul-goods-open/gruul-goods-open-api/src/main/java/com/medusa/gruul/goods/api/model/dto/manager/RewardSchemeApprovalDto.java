package com.medusa.gruul.goods.api.model.dto.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:14 2025/3/12
 */
@Data
@ApiModel(value = "奖励方案审核Dto")
public class RewardSchemeApprovalDto {

    @ApiModelProperty(value = "奖励方案ids")
    private String ids;

    @ApiModelProperty(value = "审核状态")
    private Integer approvalStatus;


    @ApiModelProperty(value = "审核原因")
    private String approvalReason;
}
