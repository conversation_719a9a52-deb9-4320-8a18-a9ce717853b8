package com.medusa.gruul.goods.api.model.vo.manager;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.data.base.BaseEntity;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.entity.SkuStock;
import com.medusa.gruul.goods.api.entity.Warehouse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * @Description: 商品库存
 * @Author: qsx
 * @Date:   2022-02-28
 * @Version: V1.0
 */
@Data
@ApiModel(value="ProductStockVo对象", description="商品库存")
public class ProductStockVo extends BaseEntity {
    
	/**id*/
    @ApiModelProperty(value = "id")
	private Long id;
	/**创建人Id*/
    @ApiModelProperty(value = "创建人Id")
	private Long createUserId;
	/**修改人Id*/
    @ApiModelProperty(value = "修改人Id")
	private Long updateUserId;
	/**删除状态：0->未删除；1->已删除*/
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
	private Integer isDeleted;
	/**仓库Id*/
    @ApiModelProperty(value = "仓库Id")
	private Long warehouseId;
	/**规格Id*/
    @ApiModelProperty(value = "规格Id")
	private Long skuId;
	/**库存*/
    @ApiModelProperty(value = "库存")
	private BigDecimal stock;

    /**实际出库数量 - 比例*/
    @ApiModelProperty(value = "实际出库数量")
    private Integer reallyOutStock;

	/**预警库存*/
    @ApiModelProperty(value = "预警库存")
	private Integer lowStock;
	/**产品Id*/
    @ApiModelProperty(value = "产品Id")
	private Long productId;
	/**备注*/
    @ApiModelProperty(value = "备注")
	private String remark;
    /**仓库全称*/
    @ApiModelProperty(value = "仓库全称")
    private String warehouseFullName;
    /**商品图片*/
    @ApiModelProperty(value = "商品图片")
    private String pic;
    /**商品规格*/
    @ApiModelProperty(value = "商品规格")
    private String specs;
    /**商品名字*/
    @ApiModelProperty(value = "商品名字")
    private String goodsName;
    /**基本单位*/
    @ApiModelProperty(value = "基本单位")
    private String unit;
    /**库存下限*/
    @ApiModelProperty(value = "库存下限")
    private BigDecimal lowerLimit;
    /**库存上限*/
    @ApiModelProperty(value = "库存上限")
    private BigDecimal upperLimit;
    /**商品名字*/
    @ApiModelProperty(value = "商品编号")
    private String goodsCode;
    /**商品规格2*/
    @ApiModelProperty(value = "商品规格2")
    private String specs2;
    /**关联商品名字*/
    @ApiModelProperty(value = "关联商品名字")
    private String linkGoodsName;
}
