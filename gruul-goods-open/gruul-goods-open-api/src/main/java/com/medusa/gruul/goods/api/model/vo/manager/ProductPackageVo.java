package com.medusa.gruul.goods.api.model.vo.manager;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:04 2024/9/3
 */
@Data
@ApiModel(value = "ProductPackageVo对象", description = "权益包商品查询返回信息")
public class ProductPackageVo implements Serializable {

    private Long id;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long productId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;
    /**
     * 商品编号
     */
    @ApiModelProperty(value = "商品编号")
    private String goodsCode;

    /**
     * 规格名称
     */
    @ApiModelProperty(value = "规格名称")
    private String specs;

    /**
     * 实售价
     */
    @ApiModelProperty(value = "实售价")
    private BigDecimal price;

    /**
     * 指导价（划线价）
     */
    @ApiModelProperty(value = "指导价（划线价）")
    private BigDecimal originalPrice;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
    /**
     * 专区名称
     */
    @ApiModelProperty(value = "专区名称")
    private String modeName;
    /**
     * 规格id
     */
    @ApiModelProperty(value = "规格id")
    private Long skuId;


    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 使用数量
     */
    @ApiModelProperty(value = "使用数量")
    private Integer useNumber;


    /**
     * 权益包互斥商品唯一id
     */
    @ApiModelProperty(value = "权益包互斥商品唯一id")
    private String mutexGoodId;

    @ApiModelProperty(value = "无期限")
    private Boolean notTerm;


    @ApiModelProperty(value = "不限次数")
    private Boolean notTime;

    /**
     * 成本价格
     */
    @ApiModelProperty(value = "成本价格")
    private BigDecimal costPrice;

    /**
     * 调后价格
     */
    @ApiModelProperty(value = "调后价格")
    private BigDecimal adjustmentPrice;


    @ApiModelProperty(value = "使用天数")
    private Integer useDays;


    /**
     * 单次频率天数，间隔多少天使用一次
     */
    @ApiModelProperty(value = "单次频率天数")
    private Integer daysRate;
}
