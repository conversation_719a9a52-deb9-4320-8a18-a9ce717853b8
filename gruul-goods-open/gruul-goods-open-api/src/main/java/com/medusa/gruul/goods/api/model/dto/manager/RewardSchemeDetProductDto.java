package com.medusa.gruul.goods.api.model.dto.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:38 2025/6/20
 */
@Data
@ApiModel(value = "奖励方案明细分佣商品Dto")
public class RewardSchemeDetProductDto {


    /**
     * 商品ID
     */
    @ApiModelProperty(value = "商品ID")
    private Long productId;

    /**
     * 商品规格id
     */
    @ApiModelProperty(value = "商品规格id")
    private Long skuId;

}
