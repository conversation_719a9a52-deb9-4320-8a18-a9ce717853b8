package com.medusa.gruul.goods.api.model.param.manager;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 8:57 2025/3/12
 */
@Data
@ApiModel(value = "RewardSchemeParam对象", description = "奖励方案查询参数")
public class RewardSchemeParam extends QueryParam {

    private static final long serialVersionUID = 1;

    @ApiModelProperty(value = "单据日期开始时间")
    private String billDateStartTime;

    @ApiModelProperty(value = "单据日期结束时间")
    private String billDateEndTime;

    @ApiModelProperty(value = "有效期开始时间")
    private String startTime;

    @ApiModelProperty(value = "有效期结束时间")
    private String endTime;

    @ApiModelProperty(value = "制单人")
    private String createUserName;

    @ApiModelProperty(value = "状态：0->草稿；1->生效中；-1->失效；-2->停止")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "方案名称")
    private String name;

    @ApiModelProperty(value = "审核状态:100->待审核;101->已审核;200->审核不通过")
    private Integer approvalStatus;

    @ApiModelProperty(value = "根据单据编号排序：1.正序；2.倒序")
    private Integer billNoSort;

    @ApiModelProperty(value = "根据单据日期排序：1.正序；2.倒序")
    private Integer billDateSort;
}
