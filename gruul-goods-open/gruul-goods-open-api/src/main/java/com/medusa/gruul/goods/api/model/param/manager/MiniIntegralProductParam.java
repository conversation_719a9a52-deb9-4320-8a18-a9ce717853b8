package com.medusa.gruul.goods.api.model.param.manager;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: 小程序端获取积分商品参数
 * @Date: Created in 19:06 2023/8/23
 */
@Data
public class MiniIntegralProductParam extends QueryParam {

    /**
     * 排序类型：1.倒序；2.升序
     */
    @ApiModelProperty(value = "排序类型：1.倒序；2.升序")
    private Integer isSort;

    /**
     * 排序类型：1.倒序；2.升序
     */
    @ApiModelProperty(value = "字段类型：1.销量；2.价格；3.积分")
    private Integer type;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 类别名称
     */
    @ApiModelProperty(value = "类别名称")
    private String categoryName;


    /**
     * 剩余积分
     */
    @ApiModelProperty(value = "剩余积分")
    private BigDecimal currentIntegral;

    /**
     * 当前日期
     */
    @ApiModelProperty(value = "当前日期")
    private String date;

}
