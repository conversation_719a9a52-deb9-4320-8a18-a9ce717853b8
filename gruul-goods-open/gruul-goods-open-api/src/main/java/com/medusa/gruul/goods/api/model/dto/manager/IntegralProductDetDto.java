package com.medusa.gruul.goods.api.model.dto.manager;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:02 2023/8/21
 */
@Data
public class IntegralProductDetDto {

    @ApiModelProperty(value = "积分商品id")
    private Long id;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    @NotNull(message = "商品id不能为空")
    private Long productId;

    /**
     * 规格id
     */
    @ApiModelProperty(value = "规格id")
    @NotNull(message = "规格id不能为空")
    private String skuId;

    /**
     * 所需积分
     */
    @ApiModelProperty(value = "所需积分")
    @NotNull(message = "所需积分不能为空")
    private BigDecimal integral;

    /**
     * 所需金额
     */
    @ApiModelProperty(value = "所需金额")
    private BigDecimal amount;

    /**
     * 总兑数量
     */
    @ApiModelProperty(value = "总兑数量")
    @NotNull(message = "总兑数量不能为空")
    private BigDecimal allExchangeNum;

    /**
     * 用户可兑数量
     */
    @ApiModelProperty(value = "用户可兑数量")
    @NotNull(message = "用户可兑数量不能为空")
    private BigDecimal userExchangeNum;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
