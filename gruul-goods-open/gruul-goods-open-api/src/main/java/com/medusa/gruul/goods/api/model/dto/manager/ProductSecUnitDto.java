package com.medusa.gruul.goods.api.model.dto.manager;

import cn.hutool.core.bean.BeanUtil;
import com.medusa.gruul.goods.api.entity.ProductSecUnit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <p>
 * 商品辅助单位
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
@Data
@ApiModel(value = "新增或修改辅助单位DTO")
public class ProductSecUnitDto {

    private Long id;

    @ApiModelProperty(value = "商品id")
    private Long productId;

    @ApiModelProperty(value = "辅助单位id")
    private Long secUnitId;

    @ApiModelProperty(value = "基本单位比值")
    private Integer unitd;

    @ApiModelProperty(value = "辅助单位比值")
    private Integer secUnitd;

    public ProductSecUnit coverProductSecUnit() {
        ProductSecUnit productSecUnit = new ProductSecUnit();
        BeanUtil.copyProperties(this, productSecUnit);
        return productSecUnit;
    }
}