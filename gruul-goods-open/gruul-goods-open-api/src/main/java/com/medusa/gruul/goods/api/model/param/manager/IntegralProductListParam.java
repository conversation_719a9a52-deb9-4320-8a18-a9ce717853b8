package com.medusa.gruul.goods.api.model.param.manager;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: plh
 * @Description: 积分商品列表查询参数
 * @Date: Created in 15:00 2023/9/8
 */
@Data
@ApiModel(value = "IntegralProductListParam对象", description = "积分商品列表查询参数")
public class IntegralProductListParam extends QueryParam {

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String productName;

    /**
     * 方案名称
     */
    @ApiModelProperty(value = "方案名称")
    private String activityName;

    /**
     * 分类一级id
     */
    @ApiModelProperty(value = "分类一级id")
    private String categoryParentId;


    /**
     * 分类二级id
     */
    @ApiModelProperty(value = "分类二级id")
    private String categoryId;

    /**
     * 状态(默认上架，0--下架，1--上架)
     */
    @ApiModelProperty(value = "状态(默认上架，0--下架，1--上架)")
    private Integer status;

    /**
     * 显示内容：1:显示积分+现金；2：显示积分
     */
    @ApiModelProperty(value = "显示内容：1:显示积分+现金；2：显示积分")
    private Integer showAll;

    /**
     * 创建时间排序：1:正序；2：倒序
     */
    @ApiModelProperty(value = "创建时间排序：1:正序；2：倒序")
    private Integer createTimeSort;

    /**
     * 总数量排序：1:正序；2：倒序
     */
    @ApiModelProperty(value = "总数量排序：1:正序；2：倒序")
    private Integer allNumSort;

    /**
     * 剩余数量排序：1:正序；2：倒序
     */
    @ApiModelProperty(value = "剩余数量排序：1:正序；2：倒序")
    private Integer residueNumSort;

}
