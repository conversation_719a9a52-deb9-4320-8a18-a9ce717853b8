package com.medusa.gruul.goods.api.model.dto.manager;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.medusa.gruul.common.core.param.QueryParam;
import com.medusa.gruul.common.data.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;


/**
 * @Description: 仓库信息
 * @Author: qsx
 * @Date:   2022-02-25
 * @Version: V1.0
 */
@Data
@TableName("t_warehouse")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="t_warehouse对象", description="仓库信息")
public class WarehouseDto extends QueryParam {

    @ApiModelProperty(value = "id")
	private Long id;
	/**创建用户id*/
    @ApiModelProperty(value = "创建用户id")
	private Long createUserId;
	/**修改用户id*/
    @ApiModelProperty(value = "修改用户id")
	private Long updateUserId;
	/**删除状态：0->未删除；1->已删除*/
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
	private Integer isDeleted;
	/**状态：0->未启用；1->已启用*/
    @ApiModelProperty(value = "状态：0->未启用；1->已启用")
	private Integer state;
	/**仓库全称*/
    @ApiModelProperty(value = "仓库全称")
    @NotNull
	private String warehouseFullName;
	/**仓库编号*/
    @ApiModelProperty(value = "仓库编号")
    @NotNull
	private String warehouseNumber;
	/**仓库地址*/
    @ApiModelProperty(value = "仓库地址")
	private String warehouseAddress;
}
