package com.medusa.gruul.goods.api.model.vo.api;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <p>
 * 商品信息Vo
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-06
 */
@Data
@ApiModel(value = "ApiAliveProductVo对象", description = "商品查询返回信息")
public class ApiAliveProductVo implements Serializable {

    private Long id;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "展示图片")
    private String pic;

    @ApiModelProperty(value = "宽屏展示图片")
    private String widePic;

    @ApiModelProperty(value = "画册图片，连产品图片限制为6张，以逗号分割")
    private String albumPics;

    @ApiModelProperty(value = "配送方式(0--商家配送，1--快递配送 2--同城配送)")
    private Integer distributionMode;


    @ApiModelProperty(value = "货号")
    private Long productSn;

    @ApiModelProperty(value = "规格类型")
    private Integer limitType;

    @ApiModelProperty(value = "最小价格")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "最大价格")
    private BigDecimal maxPrice;

    @ApiModelProperty(value = "库存")
    private Integer inventory;

    @ApiModelProperty(value = "销量")
    private Integer sale;

    @ApiModelProperty(value = "会员等级价格")
    private BigDecimal memberLevelPrice;

    @ApiModelProperty(value = "会员等级价格百分比")
    private BigDecimal memberLevelPercentage;

    @ApiModelProperty(value = "会员价类型")
    private Integer memberPriceType;

    @ApiModelProperty(value = "复购等级价格")
    private BigDecimal memberLevelAgainPrice;

    @ApiModelProperty(value = "复购等级价格百分比")
    private BigDecimal memberLevelAgainPercentage;

    @ApiModelProperty(value = "复购价类型")
    private Integer memberAgainPriceType;

    @ApiModelProperty(value = "专区id")
    private Long saleMode;

    @ApiModelProperty(value = "状态(默认上架，0--下架，1--上架)", example = "1")
    private Integer status;

    /**
     * 商品规格id
     */
    @ApiModelProperty(value = "商品规格id")
    private Long skuId;

    @ApiModelProperty(value = "限购数量", example = "10")
    private Integer perLimit;

    @ApiModelProperty(value = "商品规格数量")
    private Integer skuCount;

    @ApiModelProperty(value = "商品规格列表")
    private List<ApiSkuStockVo> skuStocks;

    /**
     * 购物车商品数量
     */
    @ApiModelProperty(value = "购物车商品数量")
    private Integer goodsNumber;

    /**
     * 卖点描述
     */
    @ApiModelProperty(value = "卖点描述")
    private String saleDescribe;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String shopName;
    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id")
    private String shopId;

    /**
     * 店铺类型
     */
    @ApiModelProperty(value = "店铺类型")
    private Integer productType;


    /**
     * 商品分类
     */
    @ApiModelProperty(value = "商品分类")
    private String categoryName;
    /**
     * 地图X
     */
    @ApiModelProperty(value = "地图X")
    private Double mapX;


    /**
     * 地图Y
     */
    @ApiModelProperty(value = "地图Y")
    private Double mapY;
    /**
     * 距离
     */
    @ApiModelProperty(value = "距离")
    private String distance;
    /**
     * 商家分类
     */
    @ApiModelProperty(value = "商家分类")
    private String shopsCategoryName;

    @ApiModelProperty(value = "店铺主键id")
    private String shopsPartnerId;
}