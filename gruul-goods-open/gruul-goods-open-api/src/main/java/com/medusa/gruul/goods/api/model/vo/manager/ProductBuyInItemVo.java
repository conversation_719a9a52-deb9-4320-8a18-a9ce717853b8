package com.medusa.gruul.goods.api.model.vo.manager;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * @Description: 购货入库明细
 * @Author: qsx
 * @Date:   2022-03-08
 * @Version: V1.0
 */
@Data
@ApiModel(value="新增或修改t_product_buy_in_detailedDTO")
public class ProductBuyInItemVo  implements Serializable {
    
	/**id*/
    @ApiModelProperty(value = "id")
	private Long id;
	/**创建用户id*/
    @ApiModelProperty(value = "创建用户id")
	private Long createUserId;
	/**修改用户id*/
    @ApiModelProperty(value = "修改用户id")
	private Long updateUserId;
	/**删除状态：0->未删除；1->已删除*/
    @ApiModelProperty(value = "删除状态：0->未删除；1->已删除")
	private Integer isDeleted;
	/**商品Id*/
    @ApiModelProperty(value = "商品Id")
	private Long productId;
    /**商品名*/
    @ApiModelProperty(value = "商品名")
    private String productName;
    /**基本单位*/
    @ApiModelProperty(value = "基本单位")
    private String unit;
	/**规格id*/
    @ApiModelProperty(value = "规格id")
	private Long skuId;
    /**规格名*/
    @ApiModelProperty(value = "规格名")
    private String skuSpecs;
    /**入库单id*/
    @ApiModelProperty(value = "入库单id")
    private Long productBuyInId;
	/**入库数量*/
    @ApiModelProperty(value = "入库数量")
	private java.math.BigDecimal instoreQty;
	/**入库单价*/
    @ApiModelProperty(value = "入库单价")
	private java.math.BigDecimal instorePrice;
	/**入库金额*/
    @ApiModelProperty(value = "入库金额")
	private java.math.BigDecimal instoreAmount;
	/**备注*/
    @ApiModelProperty(value = "备注")
	private String remarks;

    @ApiModelProperty(value = "商品基本单位id")
    private Long unitId;

    @ApiModelProperty(value = "商品规格2-通讯行业指颜色")
    private String specs2;
}
