package com.medusa.gruul.goods.api.model.dto.manager;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: plh
 * @Description: 积分商品dto
 * @Date: Created in 10:20 2023/8/21
 */
@Data
public class IntegralProductDto {

    /**
     * 积分活动id
     */
    @ApiModelProperty(value = "积分活动id")
    @NotNull(message = "积分活动id不能为空")
    private Long activityId;

    /**
     * 积分商品列表
     */
    @ApiModelProperty(value = "积分商品列表")
    @NotNull(message = "积分商品列表不能为空")
    @Valid
    private List<IntegralProductDetDto> list;
}
