package com.medusa.gruul.goods.api.model.vo.manager;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 供应商
 *
 * <AUTHOR>
 * @since 2019-10-06
 */
@Data
@ApiModel(value = "SupplierExcelVo", description = "供应商查询返回信息")
public class SupplierExcelVo {

/*
    @ApiModelProperty(value = "ID")
    private Long id;
*/

    /**
     * 供应商识别号
     */
    @ApiModelProperty(value = "ID")
    private String supplierSn;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String name;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String mobile;


    /**
     * 完整地址
     */
    @ApiModelProperty(value = "地址")
    private String area;

    /**
     * 产品信息
     */
    @ApiModelProperty(value = "产品信息")
    private String productInfo;

    /**
     * 评分
     */
    @ApiModelProperty(value = "评分）")
    private BigDecimal score;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "申请时间")
    private String createTime;

//    /**
//     * 注册来源（0--后台注册，1--小程序）
//     */
//    @ApiModelProperty(value = "注册来源，0--后台注册，1--小程序")
//    private Integer comeFrom;
    /**
     * 状态(默认待审核，0--已关闭，1--已审核，2--待审核，3--禁用中)
     */
    private Integer status;

    @ApiModelProperty(value = "状态")
    private String statusDict;


}