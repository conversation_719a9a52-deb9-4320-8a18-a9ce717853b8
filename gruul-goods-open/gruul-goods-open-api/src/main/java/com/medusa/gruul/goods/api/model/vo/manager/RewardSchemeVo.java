package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:01 2025/3/12
 */
@Data
@ApiModel(value = "RewardSchemeVo", description = "奖励方案列表返回Vo")
public class RewardSchemeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "奖励方案id")
    private Long id;

    @ApiModelProperty(value = "单据编号")
    private String billNo;

    @ApiModelProperty(value = "单据日期")
    private String billDate;

    @ApiModelProperty(value = "有效期-开始时间")
    private String startTime;

    @ApiModelProperty(value = "有效期-结束时间")
    private String endTime;

    @ApiModelProperty(value = "经手人id")
    private String userId;

    @ApiModelProperty(value = "经手人名称")
    private String userName;

    @ApiModelProperty(value = "方案名称")
    private String name;

    @ApiModelProperty(value = "状态：0->草稿；1->生效中；-1->失效；-2->停止")
    private Integer status;

    @ApiModelProperty(value = "审核状态:100->待审核;101->已审核;200->审核不通过")
    private Integer approvalStatus;

    @ApiModelProperty(value = "审核原因")
    private String approvalReason;

    @ApiModelProperty(value = "审核时间")
    private String approvalTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "奖励方案明细")
    private List<RewardSchemeDetVo>list;

}
