package com.medusa.gruul.goods.api.model.vo.manager;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * sku 规格商品列表
 */
@Data
@ApiModel(value = "SkuProductVo对象", description = "规格商品查询返回信息")
public class SkuProductVo implements Serializable {

    private Long id;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "商品分拣分类名称")
    private String sortingCategoryName;

    @ApiModelProperty(value = "供应商id")
    private Long providerId;

    @ApiModelProperty(value = "供应商名称")
    private String providerName;

    @ApiModelProperty(value = "配送方式(0--商家配送，1--快递配送 2--同城配送)")
    private Integer distributionMode;

    @ApiModelProperty(value = "运费模板ID")
    private Long freightTemplateId;

    @ApiModelProperty(value = "属性模版ID")
    private Long attributeId;

    @ApiModelProperty(value = "属性模板名称")
    private String attributeName;

    @ApiModelProperty(value = "限购类型(默认统一规格，0--统一规格，1--统一限购，2--规格限购)")
    private Integer limitType;

    @ApiModelProperty(value = "销售专区(默认商超系统，0--商超系统，2--限时秒杀)")
    private Long saleMode;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "展示图片")
    private String pic;

    @ApiModelProperty(value = "宽屏展示图片")
    private String widePic;

    @ApiModelProperty(value = "画册图片，连产品图片限制为6张，以逗号分割")
    private String albumPics;

    @ApiModelProperty(value = "视频url")
    private String videoUrl;

    @ApiModelProperty(value = "货号")
    private String productSn;

    @ApiModelProperty(value = "状态(默认上架，0--下架，1--上架)")
    private Integer status;

    @ApiModelProperty(value = "商品位置(默认线上，0--线上，1--素材库)")
    private Integer place;

    @ApiModelProperty(value = "电商平台链接")
    private String csvUrl;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "销量")
    private Integer sale;

    @ApiModelProperty(value = "基本单位id")
    private Long unitId;

    @ApiModelProperty(value = "基本单位")
    private String unit;

    @ApiModelProperty(value = "商品辅助单位信息")
    private List<ProductSecUnitVo> productSecUnits;

    @ApiModelProperty(value = "商品重量，默认为克")
    private BigDecimal weight;

    @ApiModelProperty(value = "以逗号分割的产品服务：1->无忧退货；2->快速退款；3->免费包邮")
    private String serviceIds;

    @ApiModelProperty(value = "商品详情")
    private String detail;

    @ApiModelProperty(value = "规格是否展开")
    private Boolean openSpecs;

    @ApiModelProperty(value = "销售属性")
    private String attribute;

    @ApiModelProperty(value = "卖点描述")
    private String saleDescribe;

    @ApiModelProperty(value = "评分")
    private BigDecimal score;


    @ApiModelProperty(value = "商品规格")
    private String skuStock;

    @ApiModelProperty(value = "商品规格Id")
    private Long skuId;

    @ApiModelProperty(value = "商品Id")
    private Long productId;

    @ApiModelProperty(value = "商品sku信息")
    private List<SkuStockVo> skuStocks;

    @ApiModelProperty(value = "商品属性信息")
    private List<ProductAttributeVo> productAttributes;

    @ApiModelProperty(value = "商品展示分类信息")
    private List<ProductShowCategoryVo> productShowCategorys;

    @ApiModelProperty(value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(value = "上限")
    private BigDecimal upperLimit;

    @ApiModelProperty(value = "下限")
    private BigDecimal lowerLimit;
    /**规格入库单价*/
    @ApiModelProperty(value = "规格入库单价")
    private String skuPrice;

    @ApiModelProperty(value = "会员价类型（默认不使用会员价）--》0：不使用会员价；1：固定金额；2：百分比")
    private Integer memberPriceType;

    @ApiModelProperty(value = "商品规格会员价格")
    private  List<SkuStockMemberPriceVo> skuStockMemberPriceVos;

    @ApiModelProperty(value = "商品条码")
    private String barCode;

    /**
     * 规则类型，100：固定金额，101：百分比
     */
    @ApiModelProperty(value = "规则类型，100：固定金额，101：百分比")
    private Integer ruleType;

    /**
     * 上级金额或者百分比
     */
    @ApiModelProperty(value = "上级金额或者百分比")
    private BigDecimal parentReceive;

    /**
     * 上上级金额或者百分比
     */
    @ApiModelProperty(value = "上上级金额或者百分比")
    private BigDecimal aboveParentReceive;


    /**
     * 商品类型->1.普通商品，2.权益包商品，3.组合商品
     */
    @ApiModelProperty(value = "商品类型->1.普通商品，2.权益包商品，3.组合商品")
    private Integer productType;
    /**
     * 权益包展示开始时间
     */
    @ApiModelProperty(value = "权益包展示开始时间")
    private String packageShowStartTime;

    /**
     * 权益包展示结束时间
     */
    @ApiModelProperty(value = "权益包展示结束时间")
    private String packageShowEndTime;

    /**
     * 权益包开始时间
     */
    @ApiModelProperty(value = "权益包开始时间")
    private String packageStartTime;
    /**
     * 权益包结束时间
     */
    @ApiModelProperty(value = "权益包结束时间")
    private String packageEndTime;
    /**
     * 商品标识
     */
    @ApiModelProperty(value = "商品标识")
    private String classCode;
    /**
     * 赠送优惠券id
     */
    @ApiModelProperty(value = "赠送优惠券id")
    private List<String> couponIds;
    /**
     * 权益包商品
     */
    @ApiModelProperty(value = "权益包商品")
    private List<ProductPackageVo>packageProducts;


}