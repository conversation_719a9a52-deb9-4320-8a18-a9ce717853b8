package com.medusa.gruul.goods.api.model.vo.manager;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: plh
 * @Description: 积分商品详情vo
 * @Date: Created in 17:51 2023/9/7
 */
@Data
@ApiModel(value = "IntegralProductDetVo", description = "积分商品详情vo")
public class IntegralProductDetVo {

    @ApiModelProperty(value = "商品id")
    private String productId;
    @ApiModelProperty(value = "商品名称")
    private String name;
    @ApiModelProperty(value = "店铺名称")
    private String shopName;
    @ApiModelProperty(value = "店铺名称id")
    private String shopId;
    @ApiModelProperty(value = "规格id")
    private String skuId;
    @ApiModelProperty(value = "规格名称")
    private String skuStock;
    @ApiModelProperty(value = "商品编码")
    private String goodsCode;
    @ApiModelProperty(value = "实售价")
    private BigDecimal price;
    @ApiModelProperty(value = "总兑数量")
    private BigDecimal allExchangeNum;
    @ApiModelProperty(value = "限制兑换数")
    private BigDecimal userExchangeNum;
    @ApiModelProperty(value = "所需积分")
    private BigDecimal integral;
    @ApiModelProperty(value = "所需金额")
    private BigDecimal amount;
    @ApiModelProperty(value = "备注")
    private String remark;
}
