package com.medusa.gruul.goods.api.model.vo.manager;

import cn.hutool.core.bean.BeanUtil;
import com.medusa.gruul.goods.api.entity.ProductSecUnit;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * <p>
 * 商品辅助单位
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
@Data
@ApiModel(value = "ProductSecUnit对象", description = "商品辅助单位查询返回信息")
public class ProductSecUnitVo implements Serializable {

    private Long id;

    @ApiModelProperty(value = "商品id")
    private Long productId;

    @ApiModelProperty(value = "辅助单位id")
    private Long secUnitId;

    @ApiModelProperty(value = "基本单位比值")
    private Integer unitd;

    @ApiModelProperty(value = "辅助单位比值")
    private Integer secUnitd;

    @ApiModelProperty(value = "单位名称")
    private String unit;

}
