package com.medusa.gruul.goods.service.manager.impl;

import com.medusa.gruul.goods.api.entity.ProductSecUnit;
import com.medusa.gruul.goods.api.model.vo.manager.ProductSecUnitVo;
import com.medusa.gruul.goods.mapper.manager.ProductSecUnitMapper;
import com.medusa.gruul.goods.service.manager.IProductSecUnitService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
import java.util.Map;

/**
 * @Description: 商品辅助单位
 * @Author: jeecg-boot
 * @Date:   2022-02-24
 * @Version: V1.0
 */
@Service
public class ProductSecUnitServiceImpl extends ServiceImpl<ProductSecUnitMapper, ProductSecUnit> implements IProductSecUnitService {

    /**
     * 通过商品ids查询辅助单位列表
     * @param paramMap
     * @return
     */
    @Override
    public List<ProductSecUnitVo> queryProductSecUnitByProductIds(Map<String, Object> paramMap){
        return this.baseMapper.queryProductSecUnitByProductIds(paramMap);
    }

}
