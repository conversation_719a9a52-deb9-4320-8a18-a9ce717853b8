package com.medusa.gruul.goods.web.controller.manager;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.goods.api.entity.ProductStock;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.medusa.gruul.goods.api.model.dto.manager.OutWarehouseDto;
import com.medusa.gruul.goods.api.model.dto.manager.WarehouseDto;
import com.medusa.gruul.goods.service.manager.IWarehouseService;
import com.medusa.gruul.goods.service.manager.IProductStockService;
import com.medusa.gruul.goods.web.enums.WarehouseEnum;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import lombok.extern.slf4j.Slf4j;
import com.medusa.gruul.common.core.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 仓库信息
 * @Author: qsx
 * @Date:   2022-02-25
 * @Version: V1.0
 */
@Slf4j
@Api(tags="仓库信息")
@RestController
@RequestMapping("/warehouse")
public class WarehouseController {
	@Autowired
	private IWarehouseService warehouseService;
	@Autowired
    private IProductStockService productStockService;
    @Autowired
    private RemoteShopsService remoteShopsService;
    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;
	
	/**
	 * 分页列表查询
	 *
	 * @param warehouseDto
	 * @return
	 */
	/**@AutoLog(value = "仓库信息-分页列表查询")*/
	@ApiOperation(value="仓库信息-分页列表查询", notes="仓库信息-分页列表查询")
	@PostMapping(value = "/list")
	public Result<?> queryPageList(@RequestBody WarehouseDto warehouseDto) {
        LambdaQueryWrapper<Warehouse> lambdaQueryWrapper=new LambdaQueryWrapper<Warehouse>();
        if(StrUtil.isNotBlank(warehouseDto.getWarehouseFullName())){
            lambdaQueryWrapper.like(Warehouse::getWarehouseFullName,warehouseDto.getWarehouseFullName());
        }
        if(StrUtil.isNotBlank(warehouseDto.getWarehouseNumber())){
            lambdaQueryWrapper.like(Warehouse::getWarehouseNumber,warehouseDto.getWarehouseNumber());
        }
        if(StrUtil.isNotBlank(warehouseDto.getWarehouseAddress())){
            lambdaQueryWrapper.like(Warehouse::getWarehouseAddress,warehouseDto.getWarehouseAddress());
        }
        lambdaQueryWrapper.orderByAsc(Warehouse::getWarehouseNumber);
        PageUtils<Warehouse> pageWarehouse = new PageUtils(warehouseService.page(new Page<>(warehouseDto.getCurrent(), warehouseDto.getSize()),lambdaQueryWrapper) );
//        PageUtils<Warehouse> pageWarehouse = new PageUtils(warehouseService.warehouseList( warehouseDto));
        return Result.ok(pageWarehouse);
	}

    /**
     * 仓库信息-获取职员可绑定的仓库
     * @param warehouseDto
     * @return
     */
    @ApiOperation(value="仓库信息-获取职员可绑定的仓库", notes="仓库信息-获取职员可绑定的仓库")
    @PostMapping(value = "/queryAddWareHouse")
    public Result<?> queryAddWareHouse(@RequestBody WarehouseDto warehouseDto) {
        LambdaQueryWrapper<Warehouse> lambdaQueryWrapper=new LambdaQueryWrapper<Warehouse>();
        lambdaQueryWrapper.isNotNull(Warehouse::getClassCode);
        if(StrUtil.isNotBlank(warehouseDto.getWarehouseFullName())){
            lambdaQueryWrapper.like(Warehouse::getWarehouseFullName,warehouseDto.getWarehouseFullName());
        }
        if(StrUtil.isNotBlank(warehouseDto.getWarehouseNumber())){
            lambdaQueryWrapper.like(Warehouse::getWarehouseNumber,warehouseDto.getWarehouseNumber());
        }
        if(StrUtil.isNotBlank(warehouseDto.getWarehouseAddress())){
            lambdaQueryWrapper.like(Warehouse::getWarehouseAddress,warehouseDto.getWarehouseAddress());
        }
        lambdaQueryWrapper.orderByAsc(Warehouse::getWarehouseNumber);
        PageUtils<Warehouse> pageWarehouse = new PageUtils(warehouseService.page(new Page<>(warehouseDto.getCurrent(), warehouseDto.getSize()),lambdaQueryWrapper) );
//        PageUtils<Warehouse> pageWarehouse = new PageUtils(warehouseService.warehouseList( warehouseDto));
        return Result.ok(pageWarehouse);
    }

     /**
      * 分页列表查询
      *
      * @param warehouseDto
      * @return
      */
     /**@AutoLog(value = "仓库信息-分页模糊查询")*/
     @ApiOperation(value="仓库信息-分页模糊查询", notes="仓库信息-分页模糊查询")
     @PostMapping(value = "/page")
     public Result<?> page(@RequestBody WarehouseDto warehouseDto) {
         LambdaQueryWrapper<Warehouse> lambdaQueryWrapper=new LambdaQueryWrapper<Warehouse>();
         if(StrUtil.isNotBlank(warehouseDto.getKeyword())){
             lambdaQueryWrapper.like(Warehouse::getWarehouseFullName,warehouseDto.getKeyword())
                     .or().like(Warehouse::getWarehouseNumber,warehouseDto.getKeyword());
         }
         List<Warehouse> warehouseList  = warehouseService.list(lambdaQueryWrapper );
//         PageUtils<Warehouse> pageWarehouse = new PageUtils(warehouseService.page(new Page<>(warehouseDto.getCurrent(), warehouseDto.getSize()),lambdaQueryWrapper) );
         return Result.ok(warehouseList);
     }

     /**
      * 分页列表查询（启用的仓库）
      * @param warehouseDto
      * @return
      */
     /**@AutoLog(value = "仓库信息-分页模糊查询")*/
     @ApiOperation(value="仓库信息-分页模糊查询", notes="仓库信息-分页模糊查询")
     @PostMapping(value = "/pageEnableList")
     public Result<?> pageEnableList(@RequestBody WarehouseDto warehouseDto) {
         LambdaQueryWrapper<Warehouse> lambdaQueryWrapper=new LambdaQueryWrapper<Warehouse>();
         lambdaQueryWrapper.eq(Warehouse::getState, WarehouseEnum.ENABLE.getStatus());
         if(StrUtil.isNotBlank(warehouseDto.getKeyword())){
             lambdaQueryWrapper.and(wq -> wq
                     .like(Warehouse::getWarehouseFullName, warehouseDto.getKeyword())
                     .or()
                     .like(Warehouse::getWarehouseNumber,warehouseDto.getKeyword()));
         }
         PageUtils<Warehouse> pageWarehouse = new PageUtils(warehouseService.page(new Page<>(warehouseDto.getCurrent(), warehouseDto.getSize()),lambdaQueryWrapper) );
         return Result.ok(pageWarehouse);
     }

    /**
     * 根据店铺订单获取仓库
     * @param shopId
     * @return
     */
    @ApiOperation(value="根据店铺订单获取仓库", notes="根据店铺订单获取仓库")
    @GetMapping(value = "/getWarehouseByShopId")
     public Result<?>getWarehouseByShopId(@RequestParam(value = "shopId",required = true) String  shopId){
        String oldShopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(shopId);
        LambdaQueryWrapper<Warehouse> lambdaQueryWrapper=new LambdaQueryWrapper<Warehouse>();
        lambdaQueryWrapper.eq(Warehouse::getState, WarehouseEnum.ENABLE.getStatus());
        List<Warehouse> list = warehouseService.list(lambdaQueryWrapper);
        ShopContextHolder.setShopId(oldShopId);
        return Result.ok(list);
    }

    /**
     * 获取所有仓库
     * @return
     */
    @ApiOperation(value="获取所有仓库", notes="获取所有仓库")
    @GetMapping(value = "/getAllWarehouse")
    public Result<?>getAllWarehouse(){
        ShopsPartner shopsPartner1 = remoteShopsService.getShopsPartner();
        Integer mainFlag = shopsPartner1.getMainFlag();
        String oldShopId = ShopContextHolder.getShopId();
        if(mainFlag==1){
            ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        }
        List<SpecialSetting> list = remoteMiniInfoService.getSpecialSetting();
        List<String>shopIds = new ArrayList<>();
        shopIds.add(oldShopId);
        LambdaQueryWrapper<Warehouse> lambdaQueryWrapper=new LambdaQueryWrapper<Warehouse>();
        lambdaQueryWrapper.eq(Warehouse::getState, WarehouseEnum.ENABLE.getStatus());
        //主店铺查询允许平台发货的商户
        if(mainFlag==1){
            if(list!=null&&list.size()>0){
                for (SpecialSetting specialSetting : list) {
                    if(specialSetting.getPlatformProxyShipping()==1&&specialSetting.getShopAllowPlatformSearchOrder()==1&&!specialSetting.getShopId().equals(oldShopId)){
                        shopIds.add(specialSetting.getShopId());
                    }
                }
            }
        }
        lambdaQueryWrapper.in(Warehouse::getShopId,shopIds);
        lambdaQueryWrapper.orderByDesc(Warehouse::getShopId);
        List<Warehouse> warehouseList = warehouseService.list(lambdaQueryWrapper);
        if(warehouseList!=null&&warehouseList.size()>0){
            for (Warehouse warehouse : warehouseList) {
                ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(warehouse.getShopId()));
                warehouse.setShopName(shopsPartner.getName());
            }
        }
        if(mainFlag==1){
            ShopContextHolder.setShopId(oldShopId);
        }
        return Result.ok(warehouseList);
    }
	
	/**
	 * 添加
	 *
	 * @param warehouse
	 * @return
	 */
	/**@AutoLog(value = "仓库信息-添加")*/
	@ApiOperation(value="仓库信息-添加", notes="仓库信息-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody Warehouse warehouse) {
        int numberCount = warehouseService.count(new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getWarehouseNumber, warehouse.getWarehouseNumber()));
        int nameCount = warehouseService.count(new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getWarehouseFullName, warehouse.getWarehouseFullName()));
        String str="仓库名字重复，添加失败";
        if(numberCount == 0 && nameCount==0){
            warehouseService.save(warehouse);
            str="添加成功";
        }else if(numberCount !=0){
            str="仓库编号重复，添加失败";
        }
		return Result.ok(str);
	}


	
	/**
	 * 编辑
	 *
	 * @param warehouse
	 * @return
	 */
	/**@AutoLog(value = "仓库信息-编辑")*/

	@ApiOperation(value="仓库信息-编辑", notes="仓库信息-编辑")
	@PostMapping(value = "/edit")
	public Result<?> edit(@RequestBody Warehouse warehouse) {
        int numberCount = warehouseService.count(new LambdaQueryWrapper<Warehouse>().ne(Warehouse::getId,warehouse.getId()).eq(Warehouse::getWarehouseNumber, warehouse.getWarehouseNumber()));
        int nameCount = warehouseService.count(new LambdaQueryWrapper<Warehouse>().ne(Warehouse::getId,warehouse.getId()).eq(Warehouse::getWarehouseFullName, warehouse.getWarehouseFullName()));
        String str="修改成功";
        boolean success=true;
        //如果编号重复看是否是同一个仓库
        if(numberCount >= 1 ){
            success=false;
            str="仓库编号重复，修改失败";
        }
        //如果名字重复看是否是同一个仓库
        if(nameCount >= 1 ){
            success=false;
            str="仓库名字重复，修改失败";
        }
        //判断是否能修改
        if(success){
            warehouseService.updateById(warehouse);
        }
		return Result.ok(str);
	}
	
	/**
	 * 通过id删除
	 * @param warehouse
	 * @return
	 */
	/**@AutoLog(value = "仓库信息-通过id删除")*/
	@ApiOperation(value="仓库信息-通过id删除", notes="仓库信息-通过id删除")
	@PostMapping(value = "/delete")
	public Result<?> delete(@RequestBody Warehouse warehouse) {
        //看该仓库在库存表里是否有应用，如果有则不允许删除
        int num=productStockService.count(new LambdaQueryWrapper<ProductStock>().eq(ProductStock::getWarehouseId, warehouse.getId()));
        String str="该仓库存在库存记录，不允许删除！";
        if( num==0){
            warehouseService.removeById(warehouse.getId());
            str="删除成功!";
        }
		return Result.ok(str);
	}

     /**
      * 修改仓库状态
      * @param warehouse
      * @return
      */
     /**@AutoLog(value = "仓库信息-修改仓库状态：0->未启用，1->已启用")*/
     @ApiOperation(value="仓库信息-修改仓库状态：0->未启用，1->已启用", notes="仓库信息-修改仓库状态：0->未启用，1->已启用")
     @PostMapping(value = "/updateState")
     public Result<?> updateState(@RequestBody Warehouse warehouse) {

         Long id = warehouse.getId();
         Warehouse warehouseWto = warehouseService.getById(id);
         if(warehouseWto.getStockFlag()==1){

             String str="默认仓库不能停用！";

             return Result.ok(str);
         }else{
             warehouse.setState(warehouse.getState());
             warehouseService.updateById(warehouseWto);
             String str="停用成功！";
             if(WarehouseEnum.ENABLE.getStatus()==warehouse.getState()){
                 str="启用成功！";
             }
             return Result.ok(str);
         }


     }

    @ApiOperation(value="仓库信息-修改默认仓库", notes="仓库信息-修改默认仓库")
    @PostMapping(value = "/updateStock")
    public Result<?> updateStock(@RequestBody Warehouse warehouse) {

        try {
            warehouseService.updateStock(warehouse.getId());
            return Result.ok("设置成功");
        }catch (Exception e){
            return Result.failed(e.getMessage());
        }

    }
	
	/**
	 * 通过id查询
	 *
	 * @param warehouse
	 * @return
	 */
	/**@AutoLog(value = "仓库信息-通过id查询")*/

	@ApiOperation(value="仓库信息-通过id查询", notes="仓库信息-通过id查询")
	@PostMapping(value = "/queryById")
	public Result<?> queryById(@RequestBody Warehouse warehouse) {
		Warehouse warehouseWto = warehouseService.getById(warehouse.getId());
		return Result.ok(warehouseWto);
	}


}
