package com.medusa.gruul.goods.service.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.account.api.enums.ExternalAccountEnum;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.DeleteEnum;
import com.medusa.gruul.common.core.constant.enums.SendStatusEnum;
import com.medusa.gruul.common.core.constant.enums.SourceTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.HuToolExcelUtils;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.entity.Supplier;
import com.medusa.gruul.goods.api.model.dto.manager.SupplierDto;
import com.medusa.gruul.goods.api.model.param.manager.SupplierParam;
import com.medusa.gruul.goods.api.model.vo.manager.SupplierExcelVo;
import com.medusa.gruul.goods.api.model.vo.manager.SupplierVo;
import com.medusa.gruul.goods.mapper.manager.ProductMapper;
import com.medusa.gruul.goods.mapper.manager.SupplierMapper;
import com.medusa.gruul.goods.mq.Sender;
import com.medusa.gruul.goods.service.manager.ISupplierService;
import com.medusa.gruul.goods.api.enums.ProductStatusEnum;
import com.medusa.gruul.goods.web.enums.SupplierComFromEnum;
import com.medusa.gruul.goods.web.enums.SupplierStatusEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 供应商 服务实现类
 *
 * <AUTHOR>
 * @since 2019-10-06
 */
@Service
public class SupplierServiceImpl extends ServiceImpl<SupplierMapper, Supplier> implements ISupplierService {

    @Autowired
    private SupplierMapper supplierMapper;

    @Autowired
    private ProductMapper productMapper;

    @Resource
    private RemoteMiniAccountService remoteMiniAccountService;

    @Resource
    private Sender sender;

    /**
     * 获取所有供应商
     *
     * @return 供应商list对象
     */
    @Override
    public List<SupplierVo> getAllSupplierList() {
        return supplierMapper.queryAllSupplierList();
    }

    /**
     * 供应商分页列表
     *
     * @param supplierParam
     * @return 供应商分页对象
     */
    @Override
    public IPage<SupplierVo> getSupplierList(SupplierParam supplierParam) {
        IPage<SupplierVo> page = new Page<>(supplierParam.getCurrent(), supplierParam.getSize());
        return page.setRecords(supplierMapper.querySupplierList(page, supplierParam));
    }

    /**
     * 添加供应商
     *
     * @param supplierDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSupplier(SupplierDto supplierDto) {
        //判断供应商手机号是否存在
        List<Supplier> sameSuppliers = supplierMapper.selectList(new QueryWrapper<Supplier>()
                .eq("mobile", supplierDto.getMobile()));

        ifExistSupplierMobile(sameSuppliers);
        //状态默认待审核
        if (supplierDto.getStatus() == null) {
            supplierDto.setStatus(SupplierStatusEnum.AUDITING.getStatus());
        }
        //评分默认5.0
        if (supplierDto.getScore() == null) {
            supplierDto.setScore(BigDecimal.valueOf(5.0));
        }
        //后台注册
        supplierDto.setComeFrom(SupplierComFromEnum.PC.getComeFrom());
        //供应商识别号生成
        String supplierSn;
        Integer count = this.baseMapper.queryAllCount() + CommonConstants.NUMBER_ONE;
        do {
            //生成一个16位的供货商识别号
            String date = DateUtil.format(new Date(), new SimpleDateFormat("yyyyMMdd"));
            String newCount = new DecimalFormat("000000").format(count);
            supplierSn = "GY" + date + newCount;
            //验证改id是否已经被使用
            Supplier supplierSearch = this.baseMapper.selectOne(new QueryWrapper<Supplier>().eq("supplier_sn", supplierSn));
            if (BeanUtil.isEmpty(supplierSearch)) {
                break;
            }
            count = count + 1;
        } while (true);
        supplierDto.setSupplierSn(supplierSn);
        //新增
        Supplier supplier = supplierDto.coverSupplier();
        int insert = supplierMapper.insert(supplier);
        supplierDto.setId(supplier.getId());
        if (insert == 0) {
            throw new ServiceException("新增失败！", SystemCode.DATA_ADD_FAILED_CODE);
        }
    }

    /**
     * 修改供应商
     *
     * @param supplierDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSupplier(SupplierDto supplierDto) {
        //判断原供应商是否已被删除
        Supplier oldSupplier = supplierMapper.selectById(supplierDto.getId());
        if (BeanUtil.isEmpty(oldSupplier)) {
            throw new ServiceException("供应商已被删除！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //判断供应商手机号是否存在（排除自己）
        List<Supplier> sameSuppliers = supplierMapper.selectList(new QueryWrapper<Supplier>()
                .eq("mobile", supplierDto.getMobile())
                .ne("status", SupplierStatusEnum.CLOSED.getStatus())
                .ne("id", supplierDto.getId())
        );
        ifExistSupplierMobile(sameSuppliers);
        //更新
        Supplier supplier = supplierDto.coverSupplier();
        int update = supplierMapper.updateById(supplier);
        if (update == 0) {
            throw new ServiceException("更新失败！", SystemCode.DATA_UPDATE_FAILED_CODE);
        }
    }

    /**
     * 审核供应商 再调用微信订阅消息发送通知给客户
     *
     * @param supplierDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkSupplier(SupplierDto supplierDto) {
        //判断原供应商是否已被删除
        Supplier oldSupplier = supplierMapper.selectById(supplierDto.getId());
        if (BeanUtil.isEmpty(oldSupplier)) {
            throw new ServiceException("供应商已被删除！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        //判断供应商手机号是否存在（排除自己）
        List<Supplier> sameSuppliers = supplierMapper.selectList(new QueryWrapper<Supplier>()
                .eq("mobile", supplierDto.getMobile())
                .ne("status", SupplierStatusEnum.CLOSED.getStatus())
                .ne("id", supplierDto.getId())
        );
        ifExistSupplierMobile(sameSuppliers);
        //更新
        BeanUtil.copyProperties(supplierDto, oldSupplier);
        int update = supplierMapper.updateById(oldSupplier);
        if (update == 0) {
            throw new ServiceException("审核失败！", SystemCode.DATA_UPDATE_FAILED_CODE);
        }
        String templateId = oldSupplier.getTemplateId();
        if(StringUtil.isNotEmpty(templateId)){
            //审核后 调用消息订阅去发送审核消息通知
            List<Integer> infos = new ArrayList<>(CommonConstants.NUMBER_ONE);
            infos.add(CommonConstants.NUMBER_FOUR);
            AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(oldSupplier.getUserId(), infos);
            if(BeanUtil.isEmpty(accountInfoDto)){
                throw new ServiceException("用户信息不存在！", SystemCode.DATA_EXISTED_CODE);
            }else{
                String openId = accountInfoDto.getMiniAccountOauths().getOpenId();
                sender.sendSupplierMessage(supplierDto, openId, templateId);

            }
        }
    }

    /**
     * 删除供应商
     *
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSupplierList(Long[] ids) {
        //删除供应商下所有商品
        new LambdaUpdateChainWrapper<>(productMapper)
                .in(Product::getProviderId, Arrays.asList(ids))
                .set(Product::getStatus, ProductStatusEnum.SELL_OFF.getStatus())
                .set(Product::getProviderId, null).update();
        //删除供应商
        supplierMapper.deleteBatchIds(Arrays.asList(ids));
    }


    /**
     * 判断供应商手机号是否已存在
     *
     * @param sameSuppliers
     */
    private void ifExistSupplierMobile(List<Supplier> sameSuppliers) {
        if (CollectionUtil.isNotEmpty(sameSuppliers)) {
            Supplier supplier = sameSuppliers.get(0);
            if (!BeanUtil.isEmpty(supplier)) {
                switch (supplier.getStatus()) {
                    case 1:
                        throw new ServiceException("该手机号已是供应商！", SystemCode.DATA_EXISTED_CODE);
                    case 2:
                        throw new ServiceException("该手机号已在申请中！", SystemCode.DATA_EXISTED_CODE);
                    case 3:
                        throw new ServiceException("该手机号已是供应商！", SystemCode.DATA_EXISTED_CODE);
                    default:
                        break;
                }
            }
        }
    }

    /**
     * 根据供应商id数组查询供应商信息
     *
     * @param supplierIds
     * @return List<SupplierVo>
     */
    @Override
    public List<SupplierVo> getDataSetSupplierList(List<Long> supplierIds) {
        return supplierMapper.queryDataSetSupplierList(supplierIds);
    }

    /**
     * 外部系统供应商保存
     *
     * @param supplierDto
     */
    @Override
    public Supplier outSaveSupplier(SupplierDto supplierDto){
        Supplier supplier = null;
        Supplier oldSupplier = supplierMapper.selectById(supplierDto.getId());
        if(null != oldSupplier){
            //更新
            if(StrUtil.isNotEmpty(supplierDto.getMobile())){
                //判断供应商手机号是否存在（排除自己）
                List<Supplier> sameSuppliers = supplierMapper.selectList(new QueryWrapper<Supplier>()
                        .eq("mobile", supplierDto.getMobile())
                        .ne("status", SupplierStatusEnum.CLOSED.getStatus())
                        .ne("id", supplierDto.getId())
                );
                ifExistSupplierMobile(sameSuppliers);
            }
            //更新
            supplier = supplierDto.coverSupplier();
            int update = supplierMapper.updateById(supplier);
            if (update == 0) {
                throw new ServiceException("更新失败！", SystemCode.DATA_UPDATE_FAILED_CODE);
            }

        }else{
            //新增
            if(StrUtil.isNotEmpty(supplierDto.getMobile())){
                //判断供应商手机号是否存在
                List<Supplier> sameSuppliers = supplierMapper.selectList(new QueryWrapper<Supplier>()
                        .eq("mobile", supplierDto.getMobile()));

                ifExistSupplierMobile(sameSuppliers);
            }

            //状态默认待审核
            if (supplierDto.getStatus() == null) {
                supplierDto.setStatus(SupplierStatusEnum.AUDITING.getStatus());
            }
            //评分默认5.0
            if (supplierDto.getScore() == null) {
                supplierDto.setScore(BigDecimal.valueOf(5.0));
            }
            //后台注册
            supplierDto.setComeFrom(SupplierComFromEnum.YD.getComeFrom());
            //新增
            supplier = supplierDto.coverSupplier();
            supplier.setSourceType(SourceTypeEnum.OTHER.getStatus());
            int insert = supplierMapper.insert(supplier);
            supplierDto.setId(supplier.getId());
            if (insert == 0) {
                throw new ServiceException("新增失败！", SystemCode.DATA_ADD_FAILED_CODE);
            }

        }

        return supplier;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PageUtils<List<SupplierVo>> externalSupplierList(Integer page, Integer size) {
        LambdaQueryWrapper<Supplier>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ne(Supplier::getSendStatus,SendStatusEnum.YES.getStatus()).eq(Supplier::getStatus,SupplierStatusEnum.AUDITED.getStatus()).eq(Supplier::getDeleted,DeleteEnum.NO.getStatus());
        Page<Supplier> iPage = this.baseMapper.selectPage(new Page<>(page, size), lambdaQueryWrapper);
        List<Supplier> records = iPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return new PageUtils(new ArrayList(0), (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
        }
        List<SupplierVo> vos = new LinkedList<>();
        setPcSupplierListVos(records,vos);
        //将发送的数据状态改为已发送
        List<Long> idList=records.stream().map(Supplier::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            updateSendStatus(idList, ExternalAccountEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(vos, (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
    }

    /**
     * 组装vo数据
     * @param records
     * @param vos
     */
    private void setPcSupplierListVos(List<Supplier> records, List<SupplierVo> vos) {
        for (Supplier supplier : records) {
            SupplierVo vo = new SupplierVo();
            BeanUtils.copyProperties(supplier,vo);
            vos.add(vo);
        }

    }

    @Override
    public void updateSendStatus(List<Long> supplierIds, String sendStatus) {
        this.baseMapper.updateSendStatus(supplierIds,sendStatus);
    }
    @Override
    public void export(SupplierParam param) {

        // 设置导出最大数量
        Page page = HuToolExcelUtils.exportParamToMax(param);
        List<SupplierExcelVo> list = this.baseMapper.exportList(page, param);
        final  HashMap<Integer, String> statusDict = new HashMap<Integer, String>(){{
            put(0,"已关闭");
            put(1,"已审核");
            put(2,"待审核");
            put(3,"禁用中");
        }};
        HuToolExcelUtils.exportData(list, "供应商列表", source -> {
            if (source.getStatus() != null) {
                source.setStatusDict(statusDict.get(source.getStatus()));
            }
            if (source.getArea()== null){
                source.setArea("无");
            }
            return source;
        });
    }
}
