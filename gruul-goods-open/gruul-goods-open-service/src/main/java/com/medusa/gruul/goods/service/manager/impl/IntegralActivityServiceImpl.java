package com.medusa.gruul.goods.service.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.LocalDateTimeUtils;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.dto.CurPcUserInfoDto;
import com.medusa.gruul.goods.api.constant.IntegralGoodsExchangeNumRedisKey;
import com.medusa.gruul.goods.api.entity.IntegralActivity;
import com.medusa.gruul.goods.api.entity.IntegralProduct;
import com.medusa.gruul.goods.api.enums.ProjectStatusEnum;
import com.medusa.gruul.goods.api.model.dto.manager.IntegralActivityDto;
import com.medusa.gruul.goods.api.model.dto.manager.IntegralProductDetDto;
import com.medusa.gruul.goods.api.model.dto.manager.IntegralProductDto;
import com.medusa.gruul.goods.api.model.param.manager.IntegralActivityParam;
import com.medusa.gruul.goods.api.model.vo.manager.*;
import com.medusa.gruul.goods.mapper.manager.IntegralActivityMapper;
import com.medusa.gruul.goods.service.manager.IIntegralActivityService;
import com.medusa.gruul.goods.service.manager.IIntegralProductService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: plh
 * @Description: 积分活动服务实现类
 * @Date: Created in 18:15 2023/8/18
 */
@Service
@Log4j2
public class IntegralActivityServiceImpl extends ServiceImpl<IntegralActivityMapper, IntegralActivity> implements IIntegralActivityService {

    @Autowired
    private IIntegralProductService integralProductService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PageUtils<IntegralActivityVo> searchIntegralActivity(IntegralActivityParam integralActivityParam) {
        LambdaQueryWrapper<IntegralActivity>wrapper = new LambdaQueryWrapper<>();
        List<Integer>list = new ArrayList<>();
        list.add(ProjectStatusEnum.TO_BE_RELEASED.getStatus());
        list.add(ProjectStatusEnum.IN_PROGRESS.getStatus());
        wrapper.in(IntegralActivity::getProjectStatus,list);
        wrapper.isNotNull(IntegralActivity::getStartTime);
        wrapper.isNotNull(IntegralActivity::getEndTime);
        List<IntegralActivity> IntegralActivityList = this.baseMapper.selectList(wrapper);
        if(IntegralActivityList!=null&&IntegralActivityList.size()>0){
            for (IntegralActivity integralActivity : IntegralActivityList) {
                integralActivity = updateProjectStatus(integralActivity);
                this.updateById(integralActivity);
            }
        }
        IPage<IntegralActivityVo> integralActivityIPage = this.baseMapper.selectIntegralActivityPage(new Page<>(integralActivityParam.getCurrent(), integralActivityParam.getSize()), integralActivityParam);
        return new PageUtils<>(integralActivityIPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IntegralActivity addIntegralActivity(IntegralActivityDto integralActivityDto) {
        if(integralActivityDto.getActivityName().length()>32){
            throw new ServiceException("方案名称过长，不能大于32位！");
        }
        IntegralActivity integralActivity = new IntegralActivity();
        BeanUtils.copyProperties(integralActivityDto,integralActivity);
        CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
        integralActivity.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
        integralActivity.setCreateUserName(pcUserInfoDto.getNikeName());
        integralActivity = updateProjectStatus(integralActivity);
        this.baseMapper.insert(integralActivity);
        List<IntegralProductDetDto> list = integralActivityDto.getList();
        IntegralProductDto integralProductDto = new IntegralProductDto();
        integralProductDto.setActivityId(integralActivity.getId());
        integralProductDto.setList(list);
        integralProductService.addIntegralProduct(integralProductDto);
        return integralActivity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IntegralActivity editIntegralActivity(IntegralActivityDto integralActivityDto) {
        IntegralActivity integralActivity = this.getById(integralActivityDto.getId());
        if(integralActivity==null){
            throw new ServiceException("积分活动不存在！");
        }
        if(!integralActivity.getProjectStatus().equals(ProjectStatusEnum.TO_BE_RELEASED.getStatus())){
            throw new ServiceException("进行中，已完成，已停用的积分方案，不允许进行编辑！");
        }
        if(integralActivityDto.getActivityName().length()>32){
            throw new ServiceException("方案名称过长，不能大于32位！");
        }

        BeanUtils.copyProperties(integralActivityDto,integralActivity);
        integralActivity = updateProjectStatus(integralActivity);
        CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
        integralActivity.setLastModifyUserId(Long.valueOf(pcUserInfoDto.getUserId()));
        integralActivity.setLastModifyUserName(pcUserInfoDto.getNikeName());
        this.baseMapper.updateById(integralActivity);
        LambdaQueryWrapper<IntegralProduct>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IntegralProduct::getActivityId,integralActivity.getId());
        integralProductService.remove(wrapper);
        List<IntegralProductDetDto> list = integralActivityDto.getList();
        IntegralProductDto integralProductDto = new IntegralProductDto();
        integralProductDto.setActivityId(integralActivity.getId());
        integralProductDto.setList(list);
        integralProductService.addIntegralProduct(integralProductDto);
        return integralActivity;
    }



    @Override
    public void deactivate(Long id) {
        IntegralActivity integralActivity = this.getById(id);
        if(integralActivity==null){
            throw new ServiceException("积分活动不存在！");
        }
        integralActivity.setProjectStatus(ProjectStatusEnum.DEACTIVATED.getStatus());
        this.baseMapper.updateById(integralActivity);
        //删除停用积分活动商品redis可兑换数
        this.deleteRedisByActivityId(integralActivity.getId());
    }

    /**
     * 根据积分活动id删除对应积分商品可兑换数redis
     * @param activityId
     */
    private void deleteRedisByActivityId(Long activityId){
        List<IntegralProductVo> list = integralProductService.getList(activityId);
        if(list!=null&&list.size()>0){
            IntegralGoodsExchangeNumRedisKey integralGoodsExchangeNumRedisKey = new IntegralGoodsExchangeNumRedisKey();
            for (IntegralProductVo integralProductVo : list) {
                integralGoodsExchangeNumRedisKey.del(integralProductVo.getId().toString());
            }
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(Long id) {
        IntegralActivity integralActivity = this.getById(id);
        if(integralActivity==null){
            throw new ServiceException("积分活动不存在！");
        }
        IntegralActivity newIntegralActivity = new IntegralActivity();
        CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
        newIntegralActivity.setCreateUserId(Long.valueOf(pcUserInfoDto.getUserId()));
        newIntegralActivity.setCreateUserName(pcUserInfoDto.getNikeName());
        newIntegralActivity.setActivityName(integralActivity.getActivityName());
        newIntegralActivity.setActivityPic(integralActivity.getActivityPic());
        newIntegralActivity.setProjectStatus(ProjectStatusEnum.TO_BE_RELEASED.getStatus());
        newIntegralActivity.setRemark(integralActivity.getRemark());
        this.baseMapper.insert(newIntegralActivity);
        LambdaQueryWrapper<IntegralProduct>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IntegralProduct::getActivityId,id);
        List<IntegralProduct> list = integralProductService.list(wrapper);
        if(list!=null&&list.size()>0){
            IntegralProductDto integralProductDto = new IntegralProductDto();
            integralProductDto.setActivityId(newIntegralActivity.getId());
            List<IntegralProductDetDto>integralProductDetDtoList = new ArrayList<>();
            for (IntegralProduct integralProduct : list) {
                IntegralProductDetDto integralProductDetDto = new IntegralProductDetDto();
                BeanUtils.copyProperties(integralProduct,integralProductDetDto);
                integralProductDetDto.setId(null);
                integralProductDetDtoList.add(integralProductDetDto);
            }
            integralProductDto.setList(integralProductDetDtoList);
            integralProductService.copyIntegralProduct(integralProductDto);
        }
    }


    @Override
    public IntegralActivityDetVo getDetail(Long id) {
        String shopId = ShopContextHolder.getShopId();
        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        IntegralActivity integralActivity = this.baseMapper.selectById(id);
        IntegralActivityDetVo integralActivityDetVo = new IntegralActivityDetVo();
        BeanUtils.copyProperties(integralActivity,integralActivityDetVo);
        List<IntegralProductDetVo> list = integralProductService.getIntegralProductDetVoByActivityId(id);
        integralActivityDetVo.setList(list);
        ShopContextHolder.setShopId(shopId);
        return integralActivityDetVo;
    }



    private IntegralActivity updateProjectStatus(IntegralActivity integralActivity){
        LocalDateTime startTime = integralActivity.getStartTime();
        LocalDateTime endTime = integralActivity.getEndTime();
        LocalDateTime now = LocalDateTime.now();
        if(now.isBefore(startTime)){
            integralActivity.setProjectStatus(ProjectStatusEnum.TO_BE_RELEASED.getStatus());
        }else{
            if(now.isAfter(endTime)){
                integralActivity.setProjectStatus(ProjectStatusEnum.COMPLETED.getStatus());
                //已完成的积分活动删除对应积分商品redis
                this.deleteRedisByActivityId(integralActivity.getId());
            }else{
                integralActivity.setProjectStatus(ProjectStatusEnum.IN_PROGRESS.getStatus());
            }
        }
        return integralActivity;
    }
}
