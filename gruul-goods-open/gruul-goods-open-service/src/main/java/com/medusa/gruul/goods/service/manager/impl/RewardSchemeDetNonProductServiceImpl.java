package com.medusa.gruul.goods.service.manager.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.goods.api.entity.RewardSchemeDetNonProduct;
import com.medusa.gruul.goods.mapper.manager.RewardSchemeDetNonProductMapper;
import com.medusa.gruul.goods.service.manager.IRewardSchemeDetNonProductService;
import org.springframework.stereotype.Service;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:33 2025/6/20
 */
@Service
public class RewardSchemeDetNonProductServiceImpl extends ServiceImpl<RewardSchemeDetNonProductMapper, RewardSchemeDetNonProduct> implements IRewardSchemeDetNonProductService {

}
