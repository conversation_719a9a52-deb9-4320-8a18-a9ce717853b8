package com.medusa.gruul.goods.service.manager.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.goods.api.entity.RewardScheme;
import com.medusa.gruul.goods.api.entity.RewardSchemeDet;
import com.medusa.gruul.goods.api.model.param.manager.RewardSchemeDetParam;
import com.medusa.gruul.goods.api.model.vo.manager.RewardSchemeDataVo;
import com.medusa.gruul.goods.api.model.vo.manager.RewardSchemeDetVo;
import com.medusa.gruul.goods.mapper.manager.RewardSchemeDetMapper;
import com.medusa.gruul.goods.mapper.manager.RewardSchemeMapper;
import com.medusa.gruul.goods.service.manager.IRewardSchemeDetService;
import com.medusa.gruul.goods.service.manager.IRewardSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:31 2025/3/10
 */
@Service
public class RewardSchemeDetServiceImpl extends ServiceImpl<RewardSchemeDetMapper, RewardSchemeDet> implements IRewardSchemeDetService {




    @Override
    public List<RewardSchemeDataVo> getRewardSchemeData(String completeTime, Integer type) {
        List<RewardSchemeDataVo> dataList = this.baseMapper.getRewardSchemeData(completeTime,type);
        return dataList;
    }

    @Override
    public List<RewardSchemeDetVo> getRewardSchemeDetVo(RewardSchemeDetParam param) {
        List<RewardSchemeDetVo> rewardSchemeDetList = this.baseMapper.getRewardSchemeDetVo(param);
        return rewardSchemeDetList;
    }

}
