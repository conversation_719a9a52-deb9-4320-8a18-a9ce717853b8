package com.medusa.gruul.goods.service.manager.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.enums.ExternalAccountEnum;
import com.medusa.gruul.common.core.constant.enums.SourceTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.goods.api.entity.Warehouse;
import com.medusa.gruul.goods.api.model.dto.manager.OutWarehouseDto;
import com.medusa.gruul.goods.api.model.dto.manager.WarehouseDto;
import com.medusa.gruul.goods.api.model.vo.manager.WarehouseVo;
import com.medusa.gruul.goods.mapper.manager.WarehouseMapper;
import com.medusa.gruul.goods.service.manager.IWarehouseService;
import com.medusa.gruul.goods.web.enums.WarehouseEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 仓库信息
 * @Author: jeecg-boot
 * @Date:   2022-02-25
 * @Version: V1.0
 */
@Service
public class WarehouseServiceImpl extends ServiceImpl<WarehouseMapper, Warehouse> implements IWarehouseService {

    @Override
    public IPage<Warehouse> warehouseList(WarehouseDto warehouseDto) {
        IPage<Warehouse> page = new Page<>(warehouseDto.getCurrent(), warehouseDto.getSize());
        IPage<Warehouse> iPage =this.baseMapper.selectWarehouseList((new Page<>(warehouseDto.getCurrent(), warehouseDto.getSize())),warehouseDto);
        List<Warehouse> records = iPage.getRecords();
        return page.setRecords(records);
    }

    @Override
    public List<Warehouse> getByIdList(List<Long> warehouseIdList) {
           return this.getBaseMapper().selectList(new LambdaQueryWrapper<Warehouse>().in(Warehouse::getId, warehouseIdList));
    }

    /**
     * 接收外部系统仓库信息
     * @param outWarehouseDto
     * @return
     */
    @Override
    public Warehouse newAdd(OutWarehouseDto outWarehouseDto){
        Warehouse warehouse = new Warehouse();
        if(null == outWarehouseDto.getId()){
            //新增
            int numberCount = this.count(new LambdaQueryWrapper<Warehouse>()
                    .and(wrapper -> wrapper.eq(Warehouse::getWarehouseNumber, outWarehouseDto.getWarehouseNumber()).or()
                            .eq(Warehouse::getWarehouseFullName, outWarehouseDto.getWarehouseFullName()))
            );
            if(numberCount > 0){
                throw new ServiceException("仓库名称或者编号已存在！", SystemCode.DATA_EXISTED.getCode());
            }
            BeanUtil.copyProperties(outWarehouseDto, warehouse);
            //易达系统：0是启用，-1是未启用
            //int status = warehouse.getState() == CommonConstants.NUMBER_ZERO.intValue() ? CommonConstants.NUMBER_ONE.intValue() : CommonConstants.NUMBER_ZERO.intValue();
            warehouse.setState(WarehouseEnum.ENABLE.getStatus());
            warehouse.setSourceType(SourceTypeEnum.OTHER.getStatus());
            this.save(warehouse);
        }else{
            //更新
            Warehouse dbData = this.baseMapper.selectById(outWarehouseDto.getId());
            if(null == dbData){
                throw new ServiceException("id对应的记录不存在！", SystemCode.DATA_NOT_EXIST.getCode());
            }
            BeanUtil.copyProperties(outWarehouseDto, warehouse);
            warehouse.setId(dbData.getId());
            //易达系统：0是启用，-1是未启用
            //int status = warehouse.getState() == CommonConstants.NUMBER_ZERO.intValue() ? CommonConstants.NUMBER_ONE.intValue() : CommonConstants.NUMBER_ZERO.intValue();
            warehouse.setState(WarehouseEnum.ENABLE.getStatus());
            this.updateById(warehouse);
        }

        return warehouse;
    }

    @Override
    public PageUtils<List<WarehouseVo>> externalWarehouseList(Integer page, Integer size) {

        LambdaQueryWrapper<Warehouse> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Warehouse::getState, WarehouseEnum.ENABLE.getStatus()).and(i->i.isNull(Warehouse::getSendStatus).or().eq(Warehouse::getSendStatus,0));
        Page<Warehouse> iPage = this.baseMapper.selectPage(new Page<>(page, size), lambdaQueryWrapper);
        List<Warehouse> records = iPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return new PageUtils(new ArrayList(0), (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
        }
        List<WarehouseVo> vos = new LinkedList<>();
        setPcWarehouseVos(records,vos);
        //将发送的数据状态改为已发送
        List<Long> idList=records.stream().map(Warehouse::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            updateSendStatus(idList, ExternalAccountEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(vos, (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
    }

    @Override
    public void updateSendStatus(List<Long> warehouseIds, String sendStatus) {
        this.baseMapper.updateSendStatus(warehouseIds,sendStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStock(Long id) {

        List<Warehouse> list = this.list();
        if(list!=null&&list.size()>0){
            for (Warehouse warehouse : list) {
                warehouse.setStockFlag(0);
                this.updateById(warehouse);
            }
        }
        Warehouse warehouse = this.getById(id);
        if(WarehouseEnum.DEACTIVATE.getStatus()==warehouse.getState()){
            throw new ServiceException("停用仓库不能设置为默认仓库");
        }
        warehouse.setStockFlag(1);
        this.updateById(warehouse);
    }


    /**
     * 组装仓库数据
     * @param records
     * @param vos
     */
    private void setPcWarehouseVos(List<Warehouse> records, List<WarehouseVo> vos) {
        for (Warehouse warehouse : records) {
            WarehouseVo vo = new WarehouseVo();
            BeanUtils.copyProperties(warehouse,vo);
            vos.add(vo);
        }
    }


}
