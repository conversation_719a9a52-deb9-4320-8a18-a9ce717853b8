package com.medusa.gruul.goods.service.api.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.feign.RemoteMiniAccountService;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.constant.GoodsProductRedisKey;
import com.medusa.gruul.goods.api.constant.GoodsSkuStockRedisKey;
import com.medusa.gruul.goods.api.constant.ShoppingCartRedisKey;
import com.medusa.gruul.goods.api.entity.MemberLevelGoodsAgainPrice;
import com.medusa.gruul.goods.api.entity.MemberLevelGoodsPrice;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.entity.ShoppingCart;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.dto.api.ApiShoppingCartDto;
import com.medusa.gruul.goods.api.model.dto.manager.SkuStockDto;
import com.medusa.gruul.goods.api.model.vo.api.ApiShoppingCartByShopsVo;
import com.medusa.gruul.goods.api.model.vo.api.ApiShoppingCartProductVo;
import com.medusa.gruul.goods.api.model.vo.api.ApiShoppingCartVo;
import com.medusa.gruul.goods.api.model.vo.manager.ItemVo;
import com.medusa.gruul.goods.mapper.api.ApiMemberGoodsAgainPriceMapper;
import com.medusa.gruul.goods.mapper.api.ApiMemberGoodsPriceMapper;
import com.medusa.gruul.goods.mapper.api.ApiShoppingCartMapper;
import com.medusa.gruul.goods.mq.Sender;
import com.medusa.gruul.goods.mq.ShoppingCartMessage;
import com.medusa.gruul.goods.service.api.IApiShoppingCartService;
import com.medusa.gruul.goods.service.manager.IProductService;
import com.medusa.gruul.goods.util.SortUtil;
import com.medusa.gruul.goods.api.enums.ProductStatusEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import com.medusa.gruul.shops.api.enums.ProhibitStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 小程序购物车信息 服务实现类
 *
 * <AUTHOR>
 * @since 2019-10-15
 */
@Service
public class ApiShoppingCartServiceImpl extends ServiceImpl<ApiShoppingCartMapper, ShoppingCart> implements IApiShoppingCartService {

    @Resource
    private Sender sender;

    @Resource
    private RemoteMiniAccountService remoteMiniAccountService;
    @Resource
    private RemoteMiniInfoService remoteMiniInfoService;
    @Resource
    private RemoteShopsService remoteShopsService;

    @Autowired
    private IProductService productService;
    @Resource
    private RemoteGoodsService remoteGoodsService;
    @Autowired
    private IApiShoppingCartService shoppingCartService;
    @Autowired
    private ApiMemberGoodsPriceMapper apiMemberGoodsPriceMapper;
    @Autowired
    private ApiMemberGoodsAgainPriceMapper apiMemberGoodsAgainPriceMapper;

    @Autowired
    private RemoteOrderService remoteOrderService;

    /**
     * 根据用户id查询购物车信息
     * @return 商品信息
     */
    @Override
    public List<ApiShoppingCartVo> getShoppingCartListByUserId() {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String userId = curUserDto.getUserId();
        return getData(userId);
    }

    /**
     * 根据用户id查询购物车信息-多店铺
     * @return
     */
    @Override
    public List<ApiShoppingCartByShopsVo> getShoppingCartByShopsListByUserId() {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String userId = curUserDto.getUserId();
        return getDataByShops(userId);
    }


    /**
     * 加入购物车 先更新缓存数据 再发消息更新数据表数据
     *
     * @param apiShoppingCartDto
     */
    @Override
    public void addShoppingCart(ApiShoppingCartDto apiShoppingCartDto) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        apiShoppingCartDto.setUserId(curUserDto.getUserId());
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        Integer goodsNumber = apiShoppingCartDto.getGoodsNumber();
        //缓存中更新购物车信息--新增时的数量也已传入的数量为准（跟更新购物车一样），不再跟原来购物车的数量相加，这里把skuId传入实际的值即能实现
        updateShoppingCartCache(apiShoppingCartDto, apiShoppingCartDto.getSkuId(), shoppingCartRedisKey);
        //消息队列发送
        ShoppingCartMessage shoppingCartMessage = new ShoppingCartMessage();
        //防止缓存更新把数量变更掉，在缓存更新完之后重新赋值
        apiShoppingCartDto.setGoodsNumber(goodsNumber);
        shoppingCartMessage.setApiShoppingCartDto(apiShoppingCartDto);
        shoppingCartMessage.setUserId(apiShoppingCartDto.getUserId());
        String tenantId = TenantContextHolder.getTenantId();
        shoppingCartMessage.setTenantId(tenantId);
        sender.sendShoppingCartMessage(shoppingCartMessage);
    }

    /**
     * 清空用户购物车失效商品
     *
     * @param ids 失效商品数组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cleanEffectShoppingCart(Long[] ids) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String userId = curUserDto.getUserId();
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        List<Long> idList = Arrays.asList(ids);
        //用户购物车缓存key值 用户id
        String userKey = userId;
        idList.forEach(id -> {
            //清除缓存购物车商品数据
            shoppingCartRedisKey.hdel(userKey, JSON.toJSONString(id));
            //删除数据库数据
            this.baseMapper.delete(new QueryWrapper<ShoppingCart>().eq("user_id", userId).eq("sku_id", id));
        });
    }

    /**
     * 切换购物车商品选中状态
     *
     * @param ids 切换的商品数组
     * @param selectStatus
     */
    @Override
    public void changeSelectStatus(Long[] ids, Integer selectStatus,Integer priceType,Long memberTypeId) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String userId = curUserDto.getUserId();
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        List<Long> idList = Arrays.asList(ids);
        //用户购物车缓存key值 用户id
        String userKey = userId;
        idList.forEach(id -> {
            String key = id + ":" + memberTypeId;
            ApiShoppingCartVo apiShoppingCartVo = JSON.parseObject(shoppingCartRedisKey.hget(userKey, JSON.toJSONString(key)), ApiShoppingCartVo.class);
            if(!BeanUtil.isEmpty(apiShoppingCartVo)){
                apiShoppingCartVo.setSelectStatus(selectStatus);
            }
            shoppingCartRedisKey.hset(userKey, JSON.toJSONString(key), JSON.toJSONString(apiShoppingCartVo));
        });
    }

    /**
     * 修改购物车信息
     *
     * @param params 被修改前与修改后的购物车商品信息
     */
    @Override
    public void updateShoppingCart(Map<String, ApiShoppingCartDto> params) {
        ApiShoppingCartDto oldApiShoppingCartDto = params.get("oldApiShoppingCartDto");
        ApiShoppingCartDto newApiShoppingCartDto = params.get("newApiShoppingCartDto");
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        oldApiShoppingCartDto.setUserId(curUserDto.getUserId());
        newApiShoppingCartDto.setUserId(curUserDto.getUserId());
        Integer goodsNumber = newApiShoppingCartDto.getGoodsNumber();
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        //缓存中更新购物车信息
        updateShoppingCartCache(newApiShoppingCartDto, oldApiShoppingCartDto.getSkuId(), shoppingCartRedisKey);
        //消息队列发送
        ShoppingCartMessage shoppingCartMessage = new ShoppingCartMessage();
        //防止缓存更新把数量变更掉，在缓存更新完之后重新赋值
        newApiShoppingCartDto.setGoodsNumber(goodsNumber);
        shoppingCartMessage.setApiShoppingCartDto(newApiShoppingCartDto);
        shoppingCartMessage.setUserId(oldApiShoppingCartDto.getUserId());
        shoppingCartMessage.setSkuId(oldApiShoppingCartDto.getSkuId());
        shoppingCartMessage.setMemberTypeId(oldApiShoppingCartDto.getMemberTypeId());
        String tenantId = TenantContextHolder.getTenantId();
        shoppingCartMessage.setTenantId(tenantId);
        sender.sendShoppingCartMessage(shoppingCartMessage);
    }

    /**
     * 更新购物车缓存数据公用方法
     *
     * @param apiShoppingCartDto
     * @param shoppingCartRedisKey
     */
    public void updateShoppingCartCache(ApiShoppingCartDto apiShoppingCartDto, Long skuId, ShoppingCartRedisKey shoppingCartRedisKey) {
        //用户购物车缓存key值 用户id
        String userKey = apiShoppingCartDto.getUserId();
        String addKey = apiShoppingCartDto.getSkuId() + ":" + apiShoppingCartDto.getMemberTypeId();
        String delKey = skuId + ":" + apiShoppingCartDto.getMemberTypeId();

        ApiShoppingCartVo apiShoppingCartVo = JSON.parseObject(shoppingCartRedisKey.hget(userKey, JSON.toJSONString(addKey)), ApiShoppingCartVo.class);
        if (!BeanUtil.isEmpty(apiShoppingCartVo)) {
            //如果存在相同规格的商品，查询出来的商品数量累加
            List<SkuStockDto> skuStockDtos = apiShoppingCartDto.getSkuStocks();
            if (CollectionUtil.isNotEmpty(skuStockDtos)) {
                skuStockDtos.forEach(bean -> {
                    //判断新老数据sku是否一致（区分修改是否更换sku）
                    if (apiShoppingCartDto.getSkuId().equals(bean.getId())) {
                        int quantity = apiShoppingCartDto.getGoodsNumber();
                        if (!apiShoppingCartDto.getSkuId().equals(skuId)) {
                            quantity = quantity + apiShoppingCartVo.getGoodsNumber();
                        }
                        //限购数量验证
                        if (bean.getPerLimit() < quantity && !CommonConstants.NUMBER_ZERO.equals(bean.getPerLimit())) {
                            throw new ServiceException("数量超过限购数量！", SystemCode.DATA_UPDATE_FAILED.getCode());
                        } else {
                            apiShoppingCartDto.setGoodsNumber(quantity);
                        }
                    }
                });
            }
        }
        //设置购物车商品变动时间 为购物车列表排序用
        apiShoppingCartDto.setSortTime(new Date());
        //判断是加入购物车还是修改购物车规格 修改的时候要先删除原先的购物车数据  再更新（skuId为0代表的是加入购物车）
        if (!Long.valueOf(CommonConstants.NUMBER_ZERO).equals(skuId) && !apiShoppingCartDto.getSkuId().equals(skuId)) {
            //删除缓存里面的原商品
            shoppingCartRedisKey.hdel(userKey, JSON.toJSONString(delKey));
        }
        shoppingCartRedisKey.hset(userKey, JSON.toJSONString(addKey), JSON.toJSONString(apiShoppingCartDto));
    }

    /**
     * 处理消息队列更新购物车数据库数据
     *
     * @param shoppingCartMessage
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShoppingCartDatabase(ShoppingCartMessage shoppingCartMessage) {
        String tenantId = shoppingCartMessage.getTenantId();
        TenantContextHolder.setTenantId(tenantId);
        ShoppingCart addShoppingCart = shoppingCartMessage.getApiShoppingCartDto().coverShoppingCart();
        String userId = shoppingCartMessage.getUserId();
        Long skuId = shoppingCartMessage.getSkuId();
        Long memberTypeId = shoppingCartMessage.getMemberTypeId();
        //等于0说明是加入购物车，不等于0说明是修改购物车数据
        if (!Long.valueOf(CommonConstants.NUMBER_ZERO).equals(skuId)) {
            if(memberTypeId!=null){
                this.baseMapper.delete(new QueryWrapper<ShoppingCart>().eq("user_id", userId).eq("sku_id", skuId).eq("member_type_id",memberTypeId));
            }else{
                this.baseMapper.delete(new QueryWrapper<ShoppingCart>().eq("user_id", userId).eq("sku_id", skuId));
            }
        }
        //新增购物车数据
        ShoppingCart shoppingCart = this.baseMapper.selectOne(new QueryWrapper<ShoppingCart>().eq("user_id", addShoppingCart.getUserId()).eq("sku_id", addShoppingCart.getSkuId())
                .eq("member_type_id",addShoppingCart.getMemberTypeId()));
        if (!BeanUtil.isEmpty(shoppingCart)) {
            //判断新老数据sku是否一致（区分修改是否更换sku）
            int quantity = addShoppingCart.getGoodsNumber();
            if (skuId!=null&&!addShoppingCart.getSkuId().equals(skuId)) {
                //如果存在相同规格的商品，查询出来的商品数量累加
                quantity = quantity + shoppingCart.getGoodsNumber();
            }
            //如果存在相同规格的商品，查询出来的商品数量累加
            shoppingCart.setGoodsNumber(quantity);
            int update = this.baseMapper.updateById(shoppingCart);
            if (update == 0) {
                throw new ServiceException("更新失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
            }
        } else {
            //不存在相同规格的商品，数据库插入一条商品数据
            int insert = this.baseMapper.insert(addShoppingCart);
            if (insert == 0) {
                throw new ServiceException("新增失败！", SystemCode.DATA_ADD_FAILED.getCode());
            }
        }
    }

    /**
     * 批量删除购物车信息
     *
     * @param apiShoppingCartDtos
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteShoppingCartList(List<ApiShoppingCartDto> apiShoppingCartDtos) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String userId = curUserDto.getUserId();
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        //用户购物车缓存key值 用户id
        String userKey = userId;
        apiShoppingCartDtos.forEach(bean -> {
            bean.setUserId(userId);

            String delKey = bean.getSkuId() + ":" + bean.getMemberTypeId();

            //清除缓存购物车商品数据
            shoppingCartRedisKey.hdel(userKey, JSON.toJSONString(delKey));
            //删除数据库数据
            this.baseMapper.delete(new QueryWrapper<ShoppingCart>()
                    .eq("user_id", bean.getUserId())
                    .eq("sku_id", bean.getSkuId())
                    .eq("member_type_id",bean.getMemberTypeId())
            );
        });
    }

    /**
     * 订单结算删除结算的购物车商品数据
     *
     * @param skuIds
     * @param userId
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteShoppingCartByOrder(List<Long> skuIds, String userId) {
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        //用户购物车缓存key值 用户id
        String userKey = userId;
        if (CollectionUtil.isNotEmpty(skuIds)) {
            skuIds.forEach(skuId -> {
                //清除缓存购物车商品数据
                shoppingCartRedisKey.hdel(userKey, JSON.toJSONString(skuId));
                //删除数据库数据
                this.baseMapper.delete(new QueryWrapper<ShoppingCart>().eq("user_id", userId).eq("sku_id", skuId));
            });
            return true;
        } else {
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean deleteShoppingCartByJson(String jsonStr, String userId) {
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        //用户购物车缓存key值 用户id
        String userKey = userId;
        List<Map<String, String>>dataList= (List) JSONArray.parseArray(jsonStr,Map.class);
        if(!dataList.isEmpty()){
            for (Map<String, String> map : dataList) {
                String skuId = map.get("skuId");
                String memberTypeId = map.get("memberTypeId");
                String delKey = skuId + ":" + memberTypeId;
                //清除缓存购物车商品数据
                shoppingCartRedisKey.hdel(userKey, JSON.toJSONString(delKey));
                //删除数据库数据
                this.baseMapper.delete(new QueryWrapper<ShoppingCart>().eq("user_id", userId).eq("sku_id", skuId).eq("member_type_id",memberTypeId));
            }
            return true;
        }else{
            return false;
        }
    }

    /**
     * 根据用户id获取购物车商品信息
     *
     * @param userId 用户id
     * @return 购物车商品list
     */
    private List<ApiShoppingCartVo> getData(String userId) {
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        GoodsProductRedisKey goodsProductRedisKey = new GoodsProductRedisKey();
        GoodsSkuStockRedisKey goodsSkuStockRedisKey = new GoodsSkuStockRedisKey();
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        //查询会员信息
        AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1));
        //查询会员价格表数据，一次查询所有，太耗费内存，改为针对单个商品进行查询
        //List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(accountInfoDto.getMiniAccountunt().getMemberLevelId());
        //用户购物车缓存key值 用户id
        String userKey = userId;
        //判断缓存key是否存在
        List<String> stringList = shoppingCartRedisKey.hvals(userKey);
        if (CollectionUtil.isNotEmpty(stringList)) {
            //查询用户的购物车里的商品
            /*LambdaQueryWrapper<ShoppingCart> shoppingCartLambdaQueryWrapper=new LambdaQueryWrapper<ShoppingCart>();
            shoppingCartLambdaQueryWrapper.eq(ShoppingCart::getUserId,userId);
            List<ShoppingCart> shoppingCartList=shoppingCartService.list(shoppingCartLambdaQueryWrapper);
            //获取购物车里的商品id
            List<Long> productIdList= shoppingCartList.stream().map(ShoppingCart::getProductId).collect(Collectors.toList());
            LambdaQueryWrapper<Product> lambdaQueryWrapper=new LambdaQueryWrapper<Product>();
            lambdaQueryWrapper.in(Product::getId,productIdList);
            List<Product> productList=productService.list(lambdaQueryWrapper);
            //获取购物车里的会员价类型
            Map<Long,Integer>memberPriceTypeMap = productList.stream().collect(Collectors.toMap(Product::getId, Product::getMemberPriceType));*/
            //存在：获取缓存数据
            List<ApiShoppingCartVo> apiShoppingCartVos = JSON.parseArray(String.valueOf(stringList), ApiShoppingCartVo.class);
            List<ApiShoppingCartVo> apiShoppingCartVoList = new ArrayList<>(apiShoppingCartVos.size());
            //取缓存商品基础信息（包括商品状态）goodsProductRedisKey.set
            if (CollectionUtil.isNotEmpty(apiShoppingCartVos)) {

                //获取购物车里的商品id
                List<Long> productIdList= apiShoppingCartVos.stream().map(ApiShoppingCartVo::getProductId).collect(Collectors.toList());
                LambdaQueryWrapper<Product> lambdaQueryWrapper=new LambdaQueryWrapper<Product>();
                lambdaQueryWrapper.in(Product::getId,productIdList);
                List<Product> productList=productService.list(lambdaQueryWrapper);
                //获取购物车里的会员价类型
                Map<Long,Integer> memberPriceTypeMap = productList.stream().collect(Collectors.toMap(Product::getId, Product::getMemberPriceType));


                apiShoppingCartVos.forEach(apiShoppingCartVo -> {
                    String productId = String.valueOf(apiShoppingCartVo.getProductId());
                    ApiShoppingCartProductVo apiShoppingCartProductVo = JSON.parseObject(goodsProductRedisKey.get(productId), ApiShoppingCartProductVo.class);
                    BeanUtil.copyProperties(apiShoppingCartProductVo, apiShoppingCartVo);
                    //获取会员价类型
                    Integer memberPriceType =memberPriceTypeMap.get(Long.parseLong(productId));
                    //商品sku库存赋值
                    if(CollectionUtil.isNotEmpty(apiShoppingCartVo.getSkuStocks())){
                        apiShoppingCartVo.getSkuStocks().forEach(skuStockVo -> {
                            //查询商品会员等级价格表数据
                            LambdaQueryWrapper<MemberLevelGoodsPrice> queryWrapper = new LambdaQueryWrapper<>();
                            queryWrapper.eq(MemberLevelGoodsPrice::getMemberLevelId, accountInfoDto.getMiniAccountunt().getMemberLevelId())
                                    .eq(MemberLevelGoodsPrice::getProductId, apiShoppingCartVo.getProductId());
                            List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = this.apiMemberGoodsPriceMapper.selectList(queryWrapper);
                            if (CollectionUtil.isNotEmpty(memberLevelGoodsPriceList)) {
                                //筛选该商品是否属于会员价格表的商品
                                //List<MemberLevelGoodsPrice> memberLevelGoodsPrices = memberLevelGoodsPriceList.stream().filter(i -> i.getProductId().equals(apiShoppingCartVo.getProductId())).collect(Collectors.toList());
                                //获取商品规格会员价
                                List<MemberLevelGoodsPrice> memberLevelGoodsPrices = memberLevelGoodsPriceList.stream().filter(memberLevelGoodsPrice->  memberLevelGoodsPrice.getSkuId().equals(skuStockVo.getId())).collect(Collectors.toList());
                                if (CollUtil.isNotEmpty(memberLevelGoodsPrices)) {
                                    //把价格替换成会员价
                                    if (memberLevelGoodsPrices.get(0).getMemberLevelPrice()!=null){
                                        if(null != memberPriceType && ProductStatusEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus() == memberPriceType){
                                            skuStockVo.setPrice(memberLevelGoodsPrices.get(0).getMemberLevelPrice());
                                        }else if(null != memberPriceType && ProductStatusEnum.MEMBER_PRICE_PERCENTAGE.getStatus() == memberPriceType){
                                            skuStockVo.setPrice(skuStockVo.getPrice().multiply(memberLevelGoodsPrices.get(0).getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP));
                                        }
                                    }
                                }
                            }
                            String stock = goodsSkuStockRedisKey.get(String.valueOf(skuStockVo.getId()));
                            if(StringUtil.isNotEmpty(stock)){
                                skuStockVo.setStock(new BigDecimal(stock));
                            }else{
                                skuStockVo.setStock(null);
                            }
                        });
                    }
                    apiShoppingCartVoList.add(apiShoppingCartVo);
                });
            }
            //按购物车车商品的更新时间倒序显示
            SortUtil.sortByMethod(apiShoppingCartVoList, "getSortTime", true);
            return apiShoppingCartVoList;
        }
        return null;
    }

    private List<ApiShoppingCartByShopsVo> getDataByShops(String userId) {
        ShoppingCartRedisKey shoppingCartRedisKey = new ShoppingCartRedisKey();
        GoodsProductRedisKey goodsProductRedisKey = new GoodsProductRedisKey();
        GoodsSkuStockRedisKey goodsSkuStockRedisKey = new GoodsSkuStockRedisKey();
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();

        //获取主店铺配置
        ShopsPartner shopsPartnerMain = remoteShopsService.getShopsPartnerMain();
        List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSettingByShopId(shopsPartnerMain.getShopId());
        Boolean b = false;
        if(specialSettingList!=null&&specialSettingList.size()>0){
            SpecialSetting specialSetting = specialSettingList.get(0);
            if(specialSetting.getShowMemberPriceFlag() == 1 ){
                b = true;
            }
        }




        //查询会员信息
        AccountInfoDto accountInfoDto = remoteMiniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1));
        //查询会员价格表数据，一次查询所有，太耗费内存，改为针对单个商品进行查询
        //List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(accountInfoDto.getMiniAccountunt().getMemberLevelId());
        //用户购物车缓存key值 用户id
        String userKey = userId;
        //判断缓存key是否存在
        List<String> stringList = shoppingCartRedisKey.hvals(userKey);
        if (CollectionUtil.isNotEmpty(stringList)) {
            //查询用户的购物车里的商品
            /*LambdaQueryWrapper<ShoppingCart> shoppingCartLambdaQueryWrapper=new LambdaQueryWrapper<ShoppingCart>();
            shoppingCartLambdaQueryWrapper.eq(ShoppingCart::getUserId,userId);
            List<ShoppingCart> shoppingCartList=shoppingCartService.list(shoppingCartLambdaQueryWrapper);
            //获取购物车里的商品id
            List<Long> productIdList= shoppingCartList.stream().map(ShoppingCart::getProductId).collect(Collectors.toList());
            LambdaQueryWrapper<Product> lambdaQueryWrapper=new LambdaQueryWrapper<Product>();
            lambdaQueryWrapper.in(Product::getId,productIdList);
            List<Product> productList=productService.list(lambdaQueryWrapper);
            //获取购物车里的会员价类型
            Map<Long,Integer>memberPriceTypeMap = productList.stream().collect(Collectors.toMap(Product::getId, Product::getMemberPriceType));*/
            //存在：获取缓存数据
            List<ApiShoppingCartVo> apiShoppingCartVos = JSON.parseArray(String.valueOf(stringList), ApiShoppingCartVo.class);
            List<ApiShoppingCartVo> apiShoppingCartVoList = new ArrayList<>(apiShoppingCartVos.size());
            //取缓存商品基础信息（包括商品状态）goodsProductRedisKey.set
            if (CollectionUtil.isNotEmpty(apiShoppingCartVos)) {

                //获取购物车里的商品id
                List<Long> productIdList = apiShoppingCartVos.stream().map(ApiShoppingCartVo::getProductId).collect(Collectors.toList());
                LambdaQueryWrapper<Product> lambdaQueryWrapper = new LambdaQueryWrapper<Product>();
                lambdaQueryWrapper.in(Product::getId, productIdList);
                List<Product> productList = productService.list(lambdaQueryWrapper);
                Map<Long, String> shopIdsMap = productList.stream().collect(Collectors.toMap(Product::getId, Product::getShopId));
                //获取购物车里的会员价类型
                Map<Long, Integer> memberPriceTypeMap = productList.stream().collect(Collectors.toMap(Product::getId, Product::getMemberPriceType));
                //获取购物车里的复购价类型
                Map<Long, Integer> memberAgainPriceTypeMap = productList.stream().collect(Collectors.toMap(Product::getId, Product::getMemberAgainPriceType));

                Boolean finalB = b;
                apiShoppingCartVos.forEach(apiShoppingCartVo -> {
                    String productId = String.valueOf(apiShoppingCartVo.getProductId());
                    //会员类型
                    Long memberTypeId = apiShoppingCartVo.getMemberTypeId();

                    Integer count = remoteOrderService.getCountByMemberType(curUserDto.getUserId(),memberTypeId);

                    //会员等级
                    String memberLevelId = remoteMiniAccountService.getMemberLevelId(accountInfoDto.getMiniAccountunt().getUserId(), memberTypeId);

                    ApiShoppingCartProductVo apiShoppingCartProductVo = JSON.parseObject(goodsProductRedisKey.get(productId), ApiShoppingCartProductVo.class);
                    BeanUtil.copyProperties(apiShoppingCartProductVo, apiShoppingCartVo);

                    //获取会员价类型
                    Integer memberPriceType = memberPriceTypeMap.get(Long.parseLong(productId));
                    //获取复购价类型
                    Integer memberAgainPriceType = memberAgainPriceTypeMap.get(Long.parseLong(productId));

                    String shopId = shopIdsMap.get(Long.parseLong(productId));
                    apiShoppingCartVo.setShopId(shopId);


                    //商品sku库存赋值
                    if (CollectionUtil.isNotEmpty(apiShoppingCartVo.getSkuStocks())) {
                        Integer finalMemberPriceType = memberPriceType;
                        apiShoppingCartVo.getSkuStocks().forEach(skuStockVo -> {

                            //查询商品会员等级价格表数据
                            LambdaQueryWrapper<MemberLevelGoodsPrice> queryWrapper = new LambdaQueryWrapper<>();
                            queryWrapper.eq(MemberLevelGoodsPrice::getMemberLevelId, memberLevelId)
                                    .eq(MemberLevelGoodsPrice::getProductId, apiShoppingCartVo.getProductId())
                                    .eq(MemberLevelGoodsPrice::getMemberTypeId,memberTypeId);

                            List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = this.apiMemberGoodsPriceMapper.selectList(queryWrapper);

                            BigDecimal price = skuStockVo.getPrice();

                            if (CollectionUtil.isNotEmpty(memberLevelGoodsPriceList)&& finalB) {
                                //筛选该商品是否属于会员价格表的商品
                                //List<MemberLevelGoodsPrice> memberLevelGoodsPrices = memberLevelGoodsPriceList.stream().filter(i -> i.getProductId().equals(apiShoppingCartVo.getProductId())).collect(Collectors.toList());
                                //获取商品规格会员价
                                List<MemberLevelGoodsPrice> memberLevelGoodsPrices = memberLevelGoodsPriceList.stream().filter(memberLevelGoodsPrice -> memberLevelGoodsPrice.getSkuId().equals(skuStockVo.getId())).collect(Collectors.toList());

                                if (CollUtil.isNotEmpty(memberLevelGoodsPrices)) {
                                    //把价格替换成会员价
                                    if (memberLevelGoodsPrices.get(0).getMemberLevelPrice() != null && finalB) {
                                        if (null != finalMemberPriceType && ProductStatusEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus() == finalMemberPriceType) {
                                            skuStockVo.setPrice(memberLevelGoodsPrices.get(0).getMemberLevelPrice());
                                        } else if (null != finalMemberPriceType && ProductStatusEnum.MEMBER_PRICE_PERCENTAGE.getStatus() == finalMemberPriceType) {
                                            skuStockVo.setPrice(price.multiply(memberLevelGoodsPrices.get(0).getMemberLevelPrice()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                        }
                                    }
                                }
                            }
                            if(count>0){
                                //查询商品复购等级价格表数据
                                LambdaQueryWrapper<MemberLevelGoodsAgainPrice> againWrapper = new LambdaQueryWrapper<>();
                                againWrapper.eq(MemberLevelGoodsAgainPrice::getMemberLevelId, accountInfoDto.getMiniAccountunt().getMemberLevelId())
                                        .eq(MemberLevelGoodsAgainPrice::getProductId, apiShoppingCartVo.getProductId());
                                List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPriceList = this.apiMemberGoodsAgainPriceMapper.selectList(againWrapper);
                                if (CollectionUtil.isNotEmpty(memberLevelGoodsAgainPriceList)&& finalB) {
                                    List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPrices = memberLevelGoodsAgainPriceList.stream().filter(memberLevelGoodsAgainPrice -> memberLevelGoodsAgainPrice.getSkuId().equals(skuStockVo.getId())).collect(Collectors.toList());
                                    if (CollUtil.isNotEmpty(memberLevelGoodsAgainPrices)) {
                                        //把价格替换成复购价
                                        if (memberLevelGoodsAgainPrices.get(0).getMemberLevelAgainPrice() != null && finalB) {
                                            if (null != finalMemberPriceType && ProductStatusEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus() == memberAgainPriceType) {
                                                skuStockVo.setPrice(memberLevelGoodsAgainPrices.get(0).getMemberLevelAgainPrice());
                                            } else if (null != finalMemberPriceType && ProductStatusEnum.MEMBER_PRICE_PERCENTAGE.getStatus() == memberAgainPriceType) {
                                                skuStockVo.setPrice(price.multiply(memberLevelGoodsAgainPrices.get(0).getMemberLevelAgainPrice()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                            }
                                        }
                                    }
                                }

                            }

                            String stock = goodsSkuStockRedisKey.get(String.valueOf(skuStockVo.getId()));
                            if (StringUtil.isNotEmpty(stock)) {
                                skuStockVo.setStock(new BigDecimal(stock));
                            } else {
                                skuStockVo.setStock(null);
                            }
                        });
                    }

                    //判断满减满赠活动
                    Integer buyType = 1;
                    if(count>0){
                        buyType = 2;
                    }else{
                        buyType = 1;
                    }
                    List<String>showTitleList = remoteShopsService.getShowTitleList(apiShoppingCartVo.getProductId(),apiShoppingCartVo.getShopId(),buyType);
                    if(showTitleList!=null&&showTitleList.size()>0){
                        apiShoppingCartVo.setShowTitleList(showTitleList);
                    }

                    apiShoppingCartVoList.add(apiShoppingCartVo);
                });
            }
            //按购物车车商品的更新时间倒序显示
            //SortUtil.sortByMethod(apiShoppingCartVoList, "getSortTime", true);
            if (CollectionUtil.isNotEmpty(apiShoppingCartVoList)) {
                Map<String, List<ApiShoppingCartVo>> listMap = apiShoppingCartVoList.stream().collect(Collectors.groupingBy(ApiShoppingCartVo::getShopId));
                List<ApiShoppingCartByShopsVo>list = new ArrayList<>(listMap.size());
                for (Map.Entry<String, List<ApiShoppingCartVo>> entry : listMap.entrySet()) {
                    ApiShoppingCartByShopsVo apiShoppingCartByShopsVo = new ApiShoppingCartByShopsVo();
                    String shopId = entry.getKey();
                    List<ApiShoppingCartVo> dataList  = entry.getValue();
                    ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
                    apiShoppingCartByShopsVo.setShopId(Long.valueOf(shopsPartner.getShopId()));
                    apiShoppingCartByShopsVo.setShopName(shopsPartner.getName());
                    if(shopsPartner.getProhibitStatus().equals(ProhibitStatusEnum.DISABLE.getStatus())){
                        dataList.stream().forEach(p->{
                            p.setStatus(0);
                        });

                    }
                    apiShoppingCartByShopsVo.setApiShoppingCartVos(dataList);
                    list.add(apiShoppingCartByShopsVo);
                }
                return list;
            }

        }
        return null;
    }

}
