package com.medusa.gruul.goods.service.manager;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.goods.api.entity.MemberLevelGoodsAgainPrice;
import com.medusa.gruul.goods.api.model.dto.manager.ProductDto;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:21 2025/3/21
 */
public interface IMemberLevelGoodsAgainPriceService extends IService<MemberLevelGoodsAgainPrice> {

    /**
     * 添加/编辑商品复购价格
     * @param productDto
     */
    void addOrUpdate(ProductDto productDto);

}
