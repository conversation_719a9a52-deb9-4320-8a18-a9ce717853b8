package com.medusa.gruul.goods.service.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.goods.api.entity.ProductUnit;
import com.medusa.gruul.goods.api.entity.SaleMode;
import com.medusa.gruul.goods.mapper.manager.ProductUnitMapper;
import com.medusa.gruul.goods.mapper.manager.SaleModeMapper;
import com.medusa.gruul.goods.service.manager.IProductUnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Description: 商品基本单位
 * @Author: jeecg-boot
 * @Date:   2022-02-24
 * @Version: V1.0
 */
@Service
public class ProductUnitServiceImpl extends ServiceImpl<ProductUnitMapper, ProductUnit> implements IProductUnitService {
//    @Autowired
//    private ProductUnitMapper productUnitMapper;

    @Autowired
    private ProductUnitServiceImpl productUnitService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sort(List<ProductUnit> productUnitList) {
        productUnitList.stream().forEach(saleModeDto -> {
            LambdaQueryWrapper<ProductUnit> lambdaQueryWrapper=new LambdaQueryWrapper<ProductUnit>();
            lambdaQueryWrapper.eq(ProductUnit::getId, saleModeDto.getId());
            boolean update =productUnitService.update(saleModeDto,lambdaQueryWrapper);
            if (!update) {
                throw new ServiceException("修改失败！", SystemCode.DATA_UPDATE_FAILED.getCode());
            }
        });

    }
}
