package com.medusa.gruul.goods.service.manager.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.common.core.util.HuToolExcelUtils;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.goods.api.entity.ProductPackage;
import com.medusa.gruul.goods.api.model.param.manager.ProductPackageDetailParam;
import com.medusa.gruul.goods.api.model.vo.manager.AddProductPackageVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductAllPackageVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductPackageDetailExcelVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductPackageDetailVo;
import com.medusa.gruul.goods.mapper.manager.ProductPackageMapper;
import com.medusa.gruul.goods.service.manager.IProductPackageService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:04 2024/9/3
 */
@Service
public class ProductPackageImpl extends ServiceImpl<ProductPackageMapper, ProductPackage>implements IProductPackageService {


    @Override
    public List<ProductAllPackageVo> findProductPackageByPackageId(Long packageId) {
        return this.baseMapper.findProductPackageByPackageId(packageId);
    }

    @Override
    public PageUtils<ProductPackageDetailVo> getProductPackageDetailVo(ProductPackageDetailParam param) {
        IPage<ProductPackageDetailVo> page = this.baseMapper.getProductPackageDetailVo(new Page(param.getCurrent(), param.getSize()), param);
        List<ProductPackageDetailVo> records = page.getRecords();
        if(records!=null&&records.size()>0){
            for (ProductPackageDetailVo record : records) {
                if(StringUtil.isNotEmpty(record.getPackageStartTime())){
                    record.setPackageStartTime(record.getPackageStartTime().substring(0,10));
                }
                if(StringUtil.isNotEmpty(record.getPackageEndTime())){
                    record.setPackageEndTime(record.getPackageEndTime().substring(0,10));
                }
                if(StringUtil.isNotEmpty(record.getPackageShowStartTime())){
                    record.setPackageShowStartTime(record.getPackageShowStartTime().substring(0,10));
                }
                if(StringUtil.isNotEmpty(record.getPackageShowEndTime())){
                    record.setPackageShowEndTime(record.getPackageShowEndTime().substring(0,10));
                }
            }
        }
        return new PageUtils<ProductPackageDetailVo>(page);
    }


    @Override
    public void exportProductPackageDetailVo(ProductPackageDetailParam param) {

        // 设置导出最大数量
        HuToolExcelUtils.exportParamToMax(param);
        PageUtils<ProductPackageDetailVo> productPackageDetailVo = this.getProductPackageDetailVo(param);
        HuToolExcelUtils.exportData(productPackageDetailVo.getList(), "权益包明细列表",(source)->{
            ProductPackageDetailExcelVo target = new ProductPackageDetailExcelVo();
            if (source.getMutexFlag()!=null){
                target.setMutexFlag(source.getMutexFlag()==1?"是":"否");
            }
            if (source.getNotTerm()!=null)
                target.setNotTerm(source.getNotTerm()==1?"是":"否");

            if (source.getNotTime()!=null)
                target.setNotTime(source.getNotTime()==1?"是":"否");
            return target;
        });
    }
}
