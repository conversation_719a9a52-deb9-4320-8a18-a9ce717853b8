package com.medusa.gruul.goods.service.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.medusa.gruul.goods.api.entity.MemberLevelGoodsPrice;
import com.medusa.gruul.goods.api.entity.Product;
import com.medusa.gruul.goods.api.model.dto.manager.AccountCollectMemberLevelGoodsPriceDto;
import com.medusa.gruul.goods.api.model.dto.manager.ProductDto;
import com.medusa.gruul.goods.api.model.vo.manager.MemberLevelGoodsPriceGroupByMemberTypeVo;
import com.medusa.gruul.goods.api.model.vo.manager.SkuStockMemberPriceVo;
import com.medusa.gruul.goods.mapper.manager.MemberGoodsPriceMapper;
import com.medusa.gruul.goods.service.manager.IMemberLevelGoodsPriceService;
import com.medusa.gruul.goods.service.manager.IProductService;
import com.medusa.gruul.goods.api.enums.ProductStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 会员等级价格
 * @Author: jeecg-boot
 * @Date:   2022-02-22
 * @Version: V1.0
 */
@Service
public class MemberLevelGoodsPriceServiceImpl extends ServiceImpl<MemberGoodsPriceMapper, MemberLevelGoodsPrice> implements IMemberLevelGoodsPriceService {
    @Autowired
    private IMemberLevelGoodsPriceService memberLevelGoodsPriceService;
    @Autowired
    private IProductService productService;
    @Override
    public void addOrUpdate(ProductDto productDto) {
        //改变会员价类型
        Product product=new Product();
        product.setId(productDto.getId());
        product.setMemberPriceType(productDto.getMemberPriceType());
        productService.updateById(product);
        //如果类型是不使用会员价，将之前的记录删掉
         if(ProductStatusEnum.MEMBER_PRICE_NOT_USED.getStatus()==productDto.getMemberPriceType()){
            LambdaQueryWrapper<MemberLevelGoodsPrice> lambdaQueryWrapper=new LambdaQueryWrapper<MemberLevelGoodsPrice>();
            lambdaQueryWrapper.eq(MemberLevelGoodsPrice::getProductId,productDto.getId());
            memberLevelGoodsPriceService.remove(lambdaQueryWrapper);
        }else {
            List<SkuStockMemberPriceVo> skuStockMemberPriceDtos=productDto.getSkuStockMemberPriceVos();
            List<MemberLevelGoodsPrice> memberLevelGoodsPriceList=new ArrayList<MemberLevelGoodsPrice>();
            for(SkuStockMemberPriceVo skuStockMemberPrice:skuStockMemberPriceDtos){
                List<MemberLevelGoodsPriceGroupByMemberTypeVo> list = skuStockMemberPrice.getMemberLevelGoodsPriceGroupByMemberTypeVos();
                for (MemberLevelGoodsPriceGroupByMemberTypeVo memberLevelGoodsPriceGroupByMemberTypeVo : list) {
                    List<MemberLevelGoodsPrice> memberLevelGoodsPrices = memberLevelGoodsPriceGroupByMemberTypeVo.getMemberLevelGoodsPriceList();
                    memberLevelGoodsPriceList.addAll(memberLevelGoodsPrices);
                }
            }
            memberLevelGoodsPriceService.saveOrUpdateBatch(memberLevelGoodsPriceList);
        }
    }

    @Override
    public List<AccountCollectMemberLevelGoodsPriceDto> selecetMemberLevelGoodsPrice(String memberId, List<Long> productIds) {
        List<AccountCollectMemberLevelGoodsPriceDto> list=new ArrayList<>();
        for(Long productId:productIds){
            AccountCollectMemberLevelGoodsPriceDto  AccountCollectMemberLevelGoodsPrice= this.baseMapper.selectMemberLevelGoodsPrice(memberId,productId);
            if( AccountCollectMemberLevelGoodsPrice!=null){
                list.add(AccountCollectMemberLevelGoodsPrice);
            }

        }
        return list;
    }

    /**
     * 查询会员等级关联的商品会员等级价格表的记录数
     * @param memberLevelId
     * @return
     */
    @Override
    public Integer getCountByMemberLevelId(String memberLevelId){
        LambdaQueryWrapper<MemberLevelGoodsPrice> lambdaQueryWrapper=new LambdaQueryWrapper<MemberLevelGoodsPrice>();
        lambdaQueryWrapper.eq(MemberLevelGoodsPrice::getMemberLevelId, memberLevelId).eq(MemberLevelGoodsPrice::getDeleted, false);
        return this.baseMapper.selectCount(lambdaQueryWrapper);
    }
}
