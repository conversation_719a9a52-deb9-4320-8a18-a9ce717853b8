<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountPackageGoodsMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.account.model.vo.MiniAccountPackageGoodsVo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="user_phone" property="userPhone"/>
        <result column="package_id" property="packageId"/>
        <result column="package_name" property="packageName"/>
        <result column="order_id" property="orderId"/>
        <result column="pay_store" property="payStore"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="sku_id" property="skuId"/>
        <result column="sku_name" property="skuName"/>
        <result column="all_times" property="allTimes"/>
        <result column="already_times" property="alreadyTimes"/>
        <result column="status" property="status"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="not_term" property="notTerm"/>
        <result column="not_time" property="notTime"/>
        <result column="pay_time" property="payTime"/>
        <result column="department_name" property="departmentName"/>
    </resultMap>
    <resultMap id="ApiPackageGoodsMap" type="com.medusa.gruul.account.model.vo.ApiPackageGoodsVo">
        <id column="id" property="id"/>
        <result column="main_id" property="mainId"/>
        <result column="product_id" property="productId"/>
        <result column="sku_id" property="skuId"/>
        <result column="all_times" property="allTimes"/>
        <result column="already_times" property="alreadyTimes"/>
        <result column="status" property="status"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="not_term" property="notTerm"/>
        <result column="not_time" property="notTime"/>
        <result column="source_user_name" property="sourceUserName"/>
    </resultMap>
    <resultMap id="PackageGoodsShowMap" type="com.medusa.gruul.account.api.model.vo.PackageGoodsShowVo">
        <id column="id" property="id"/>
        <result column="product_id" property="productId"/>
        <result column="all_times" property="allTimes"/>
        <result column="already_times" property="alreadyTimes"/>
        <result column="status" property="status"/>
        <result column="not_term" property="notTerm"/>
        <result column="not_time" property="notTime"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
    </resultMap>
    <select id="getPageList" resultMap="BaseResultMap">
        select
        t0.id as id,
        t1.user_id as user_id,t3.nike_name as user_name,
        t3.phone as user_phone,t1.package_id as package_id,
        t1.package_name as package_name,t1.order_id as order_id,
        t1.pay_time as pay_time,t1.pay_amount as pay_amount,
        t1.pay_store as pay_store,t0.product_id as product_id,
        t0.product_name as product_name,t0.sku_id as sku_id,
        t0.sku_name as sku_name,t0.all_times as all_times,
        t0.already_times as already_times,t0.status as status,
        t0.start_time as start_time,t0.end_time as end_time,
        t0.not_term as not_term,t0.not_time as not_time,
        t1.department_name as department_name
        from
        t_mini_account_package_goods t0
        left join
        t_mini_account_package_order t1 on t0.main_id = t1.id
        left join
        t_mini_account_extends t2 on t1.user_id = t2.shop_user_id
        left join
        t_mini_account t3 on t3.user_id = t2.user_id
        where 1=1
        <if test="paramMap.userName!=null and paramMap.userName!='' ">
            and t3.nike_name like CONCAT('%',#{paramMap.userName},'%')
        </if>
        <if test="paramMap.userPhone!=null and paramMap.userPhone!='' ">
            and t3.phone like CONCAT('%',#{paramMap.userPhone},'%')
        </if>
        <if test="paramMap.productName!=null and paramMap.productName!='' ">
            and t0.product_name like CONCAT('%',#{paramMap.productName},'%')
        </if>
        <if test="paramMap.packageName!=null and paramMap.packageName!='' ">
            and t1.package_name like CONCAT('%',#{paramMap.packageName},'%')
        </if>
        <if test="paramMap.orderId!=null and paramMap.orderId!='' ">
            and t1.order_id  = #{paramMap.orderId}
        </if>
        <if test="paramMap.userId!=null and paramMap.userId!='' ">
            and t1.user_id  = #{paramMap.userId}
        </if>
        <if test="paramMap.startTime!=null ">
            and t0.start_time >=#{paramMap.startTime}
        </if>
        <if test="paramMap.endTime!=null ">
            and t0.end_time &lt;= #{paramMap.endTime}
        </if>
        <if test="paramMap.status!=null ">
            and t0.status = #{paramMap.status}
        </if>
        order by t1.create_time desc,t1.id desc,t0.sku_name desc
    </select>
    <select id="getMiniAccountPackageGoods" resultMap="BaseResultMap">
        select
            t0.id as id,
            t1.user_id as user_id,t3.nike_name as user_name,
            t3.phone as user_phone,t1.package_id as package_id,
            t1.package_name as package_name,t1.order_id as order_id,
            t1.pay_time as pay_time,t1.pay_amount as pay_amount,
            t1.pay_store as pay_store,t0.product_id as product_id,
            t0.product_name as product_name,t0.sku_id as sku_id,
            t0.sku_name as sku_name,t0.all_times as all_times,
            t0.already_times as already_times,t0.status as status,
            t0.start_time as start_time,t0.end_time as end_time,
            t0.not_term as not_term,t0.not_time as not_time,
            t1.department_name as department_name
        from
            t_mini_account_package_goods t0
                left join
            t_mini_account_package_order t1 on t0.main_id = t1.id
                left join
            t_mini_account_extends t2 on t1.user_id = t2.shop_user_id
                left join
            t_mini_account t3 on t3.user_id = t2.user_id
        where 1=1
        <if test="paramMap.mainId!=null and paramMap.mainId!='' ">
            and t0.main_id = #{paramMap.mainId}
        </if>
    </select>
    <select id="getApiPackageGoods" resultMap="ApiPackageGoodsMap">
        select
            t1.id,t1.main_id,t1.product_id,t1.sku_id,t1.all_times,
            t1.already_times,t1.status,t1.start_time,t1.end_time,
            t1.not_term,t1.not_time
        from
            t_mini_account_package_goods t1
        where
            t1.is_deleted = 0 and t1.main_id = #{mainId}
        <if test="userId!=null and userId!='' ">
            and t1.user_id  = #{userId}
        </if>
        order by t1.all_times - t1.already_times = 0 asc

    </select>
    <select id="getPackageGoodsShowVo" resultMap="PackageGoodsShowMap">
        SELECT
            t1.id,
            t1.product_id,
            t1.all_times,
            t1.already_times,
            t1.status,
            t1.not_term,
            t1.not_time,
            t1.start_time,
            t1.end_time
        FROM
            t_mini_account_package_goods t1
                LEFT JOIN t_mini_account_package_order t2 ON t1.main_id = t2.id
        WHERE
            t1.is_deleted = 0 and t1.user_id = t2.user_id
          AND t2.order_id = #{orderId}
          AND t2.package_id = #{packageId}
    </select>

    <select id="getGiftPackageGoods" resultMap="ApiPackageGoodsMap">
        select
            t1.id,t1.main_id,t1.product_id,t1.sku_id,t1.all_times,
            t1.already_times,t1.status,t1.start_time,t1.end_time,
            t1.not_term,t1.not_time, t3.nike_name as source_user_name
        from
            t_mini_account_package_goods t1 left join t_mini_account_extends t2 on t1.source_user_id = t2.shop_user_id
                                            left join t_mini_account t3 on t2.user_id = t3.user_id
        where
            t1.is_deleted = 0 and ifNull(t1.source_mini_account_package_goods_id, 0) > 0

        <if test="userId!=null and userId!='' ">
            and t1.user_id  = #{userId}
        </if>
        order by t1.status asc, t1.id desc

    </select>
</mapper>
