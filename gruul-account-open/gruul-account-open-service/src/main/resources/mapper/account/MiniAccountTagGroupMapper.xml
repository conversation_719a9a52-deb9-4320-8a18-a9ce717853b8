<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountTagGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.account.api.entity.MiniAccountTagGroup">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="tag_id" property="tagId"/>
        <result column="user_id" property="userId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        is_deleted,
        update_time,
        id, tag_id, user_id
    </sql>

    <select id="getUserIdByTagIds" resultType="java.lang.String">
        SELECT
            t1.user_id
        FROM
            t_mini_account_tag_group t1
        WHERE
            t1.is_deleted = 0
          AND t1.tag_id IN
        <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
            #{tagId}
        </foreach>
        GROUP BY
            t1.user_id;
    </select>
    <select id="getTagAccountNum" resultType="java.lang.Integer">
        SELECT
            count( t1.id ) AS count
        FROM
            t_mini_account_tag_group t1
            LEFT JOIN t_mini_account_extends t2 ON t1.user_id = t2.shop_user_id
            AND t2.is_deleted = 0
            LEFT JOIN t_mini_account t3 ON t3.user_id = t2.user_id
            AND t3.is_deleted = 0
        WHERE
            t1.is_deleted = 0
          AND ifnull( t2.id, '' )!= ''
          AND t3.whether_authorization = 1
          AND t1.tag_id = #{tagId}
    </select>
</mapper>
