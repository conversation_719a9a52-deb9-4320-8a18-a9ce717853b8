<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MemberActiveProductMapper">
    <resultMap id="MemberActiveProductMap" type="com.medusa.gruul.account.model.vo.MemberActiveProductVo">
        <result column="product_id" property="productId"/>
        <result column="product_pic" property="productPic"/>
        <result column="product_name" property="productName"/>
        <result column="product_sku_id" property="productSkuId"/>
        <result column="product_sku_code" property="productSkuCode"/>
        <result column="product_quantity" property="productQuantity"/>
        <result column="sku_specs" property="skuSpecs"/>
    </resultMap>
    <select id="getList" resultMap="MemberActiveProductMap">
        SELECT
            t1.product_id,
            t1.product_pic,
            t1.product_name,
            t1.product_sku_id,
            t1.product_sku_code,
            t1.product_quantity,
            t2.specs as sku_specs
        FROM
            t_member_active_product t1
        left join t_sku_stock t2 on t1.product_sku_id = t2.id
        WHERE
            t1.is_deleted = 0
    </select>
</mapper>
