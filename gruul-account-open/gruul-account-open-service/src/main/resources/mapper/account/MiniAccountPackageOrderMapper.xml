<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountPackageOrderMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.account.model.vo.MiniAccountPackageOrderVo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="user_phone" property="userPhone"/>
        <result column="package_id" property="packageId"/>
        <result column="package_name" property="packageName"/>
        <result column="order_id" property="orderId"/>
        <result column="pay_time" property="payTime"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="pay_store" property="payStore"/>
        <result column="product_quantity" property="productQuantity"/>
        <result column="department_code" property="departmentCode"/>
        <result column="department_name" property="departmentName"/>
        <result column="store_front_name" property="storeFrontName"/>
    </resultMap>
    <resultMap id="ApiBaseResultMap" type="com.medusa.gruul.account.model.vo.ApiMiniAccountPackageOrderVo">
        <id column="id" property="id"/>
        <result column="package_name" property="packageName"/>
        <result column="package_start_time" property="packageStartTime"/>
        <result column="package_end_time" property="packageEndTime"/>
        <result column="pay_time" property="payTime"/>
        <result column="department_name" property="departmentName"/>
        <result column="department_name2" property="departmentName2"/>
        <result column="store_front_name" property="storeFrontName"/>
        <result column="package_start_time_str" property="packageStartTimeStr"/>
        <result column="package_end_time_str" property="packageEndTimeStr"/>
    </resultMap>


    <select id="getPageList" resultMap="BaseResultMap">
        select
            t1.id as id,
            t1.user_id as user_id,t3.nike_name as user_name,
            t3.phone as user_phone,t1.package_id as package_id,
            t1.package_name as package_name,t1.order_id as order_id,
            t1.pay_time as pay_time,t1.pay_amount as pay_amount,
            t1.pay_store as pay_store,t1.product_quantity as product_quantity,
            t1.department_code as department_code,t1.department_name as department_name,
            t4.store_front_name as  store_front_name
        from
            t_mini_account_package_order t1
        left join
            t_mini_account_extends t2 on t1.user_id = t2.shop_user_id
        left join
            t_mini_account t3 on t3.user_id = t2.user_id
        left join
            t_platform_account_info t4 on t4.id = t1.account_id
        where 1=1
        <if test="paramMap.userName!=null and paramMap.userName!='' ">
            and t3.nike_name like CONCAT('%',#{paramMap.userName},'%')
        </if>
        <if test="paramMap.userPhone!=null and paramMap.userPhone!='' ">
            and t3.phone like CONCAT('%',#{paramMap.userPhone},'%')
        </if>
        <if test="paramMap.packageName!=null and paramMap.packageName!='' ">
            and t1.package_name like CONCAT('%',#{paramMap.packageName},'%')
        </if>
        <if test="paramMap.storeFrontName!=null and paramMap.storeFrontName!='' ">
            and t4.store_front_name like CONCAT('%',#{paramMap.storeFrontName},'%')
        </if>
        <if test="paramMap.orderId!=null and paramMap.orderId!='' ">
            and t1.order_id  = #{paramMap.orderId}
        </if>
        <if test="paramMap.startTime!=null ">
            and t1.pay_time >=#{paramMap.startTime}
        </if>
        <if test="paramMap.endTime!=null ">
            and t1.pay_time &lt;= #{paramMap.endTime}
        </if>
        order by t1.create_time desc
    </select>

    <select id="getApiMiniAccountPackageOrder" resultMap="ApiBaseResultMap">
        SELECT
            t1.id,
            t1.package_name,
            t1.package_start_time,
            DATE_FORMAT(t1.package_start_time, '%Y.%m.%d') as package_start_time_str,
            t1.package_end_time,
            DATE_FORMAT(t1.package_end_time, '%Y.%m.%d') as package_end_time_str,
            t1.pay_time,
            t1.store_front_name as department_name,
            t1.department_name as department_name2,
            t1.store_front_name
        FROM
            t_mini_account_package_order t1
        left join
            t_mini_account_extends t2 on t1.user_id = t2.shop_user_id
        left join
            t_mini_account t3 on t3.user_id = t2.user_id
        where
            t1.status = #{paramMap.status} and t3.user_id = #{paramMap.userId}
        order by t1.pay_time desc
    </select>

</mapper>
