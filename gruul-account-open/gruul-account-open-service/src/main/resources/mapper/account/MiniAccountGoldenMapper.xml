<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountGoldenMapper">
    <resultMap id="UserGoldenVoMap" type="com.medusa.gruul.account.model.vo.UserGoldenVo">
        <result column="nike_name" property="nikeName"/>
        <result column="avatar_url" property="avatarUrl"/>
        <result column="recommend_name" property="recommendName"/>
        <result column="golden" property="golden"/>
        <result column="used_golden" property="usedGolden"/>
        <result column="current_golden" property="currentGolden"/>
    </resultMap>
    <resultMap id="MiniAccountGoldenMap" type="com.medusa.gruul.account.model.vo.MiniAccountGoldenVo">
        <id column="id" property="id"/>
        <result column="total_golden" property="totalGolden"/>
        <result column="source_shop_user_name" property="sourceShopUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="amount" property="amount"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <resultMap id="MiniAccountGoldenManageMap" type="com.medusa.gruul.account.model.vo.MiniAccountGoldenManageVo">
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="golden" property="golden"/>
        <result column="way" property="way"/>
        <result column="amount" property="amount"/>
        <result column="last_golden" property="lastGolden"/>
        <result column="total_golden" property="totalGolden"/>
        <result column="source_nike_name" property="sourceNikeName"/>
        <result column="time" property="time"/>
        <result column="order_id" property="orderId"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <select id="getUserGoldenVo" resultMap="UserGoldenVoMap">
        SELECT
            t1.nike_name,
            t1.avatar_url,
            ifnull(t1.golden,0) as golden,
            ifnull(t1.current_golden,0) as current_golden,
            ifnull(t1.used_golden,0) as used_golden,
            t2.nike_name as recommend_name
        FROM
            t_mini_account t1 left join t_mini_account t2 on t1.parent_id = t2.user_id
        where t1.user_id = #{userId}
    </select>
    <select id="pageMyGolden" resultMap="MiniAccountGoldenMap">
        SELECT
            t1.id,
            t1.total_golden,
            t3.nike_name AS source_shop_user_name,
            DATE_FORMAT( t1.create_time, '%Y-%m-%d %H:%i:%s' ) AS create_time,
            t1.amount,
            t1.remark
        FROM
            t_mini_account_golden t1
                LEFT JOIN t_mini_account_extends t2 ON t1.source_shop_user_id = t2.shop_user_id
                LEFT JOIN t_mini_account t3 ON t2.user_id = t3.user_id
        WHERE
            t1.is_deleted = 0
        <if test="paramMap.shopUserId!=null and paramMap.shopUserId!='' ">
            AND t1.user_id =  #{paramMap.shopUserId}
        </if>
        <if test="paramMap.startTime!=null and paramMap.startTime!='' ">
            AND t1.create_time  &gt;= #{paramMap.startTime}
        </if>
        <if test="paramMap.endTime!=null and paramMap.endTime!='' ">
            AND t1.create_time  &lt;= #{paramMap.endTime}
        </if>
        <if test="paramMap.keyword!=null and paramMap.keyword!='' ">
            AND (
            t3.nike_name like concat('%',#{paramMap.keyword},'%')
            or t1.remark like concat('%',#{paramMap.keyword},'%')
            )
        </if>
        <if test="paramMap.type!=null and paramMap.type == 1 ">
            AND t1.amount>0
        </if>
        <if test="paramMap.type!=null and paramMap.type == 2 ">
            AND t1.amount&lt;0
        </if>
        ORDER BY
        t1.create_time DESC
    </select>
    <select id="searchMiniAccountGoldenDet" resultMap="MiniAccountGoldenManageMap">
        SELECT
            t3.nike_name,
            t3.phone,
            t3.golden,
            t1.last_golden,
            t1.total_golden,
            t1.amount,
            CASE
                WHEN t1.amount > 0 THEN
                    '增加' ELSE '减少'
                END AS way,
            t5.nike_name AS source_nike_name,
            DATE_FORMAT( t1.create_time, '%Y-%m-%d %H:%i:%s' ) AS time,
            t1.order_id,
            t1.remark
        FROM
            t_mini_account_golden t1
            LEFT JOIN t_mini_account_extends t2 ON t1.user_id = t2.shop_user_id
            LEFT JOIN t_mini_account t3 ON t2.user_id = t3.user_id
            LEFT JOIN t_mini_account_extends t4 ON t1.source_shop_user_id = t4.shop_user_id
            LEFT JOIN t_mini_account t5 ON t4.user_id = t5.user_id
        WHERE
            t1.is_deleted = 0
            <if test="paramMap.nikeName!=null and paramMap.nikeName!='' ">
                AND t3.nike_name like concat('%',#{paramMap.nikeName},'%')
            </if>
            <if test="paramMap.phone!=null and paramMap.phone!='' ">
                AND t3.phone like concat('%',#{paramMap.phone},'%')
            </if>
            <if test="paramMap.startTime!=null and paramMap.startTime!='' and paramMap.endTime!=null and paramMap.endTime!=''">
                and t1.create_time >= #{paramMap.startTime} and t1.create_time &lt;= #{paramMap.endTime}
            </if>
            <if test="paramMap.commissionType!=null and paramMap.commissionType!=0">
                AND t1.commission_type = #{paramMap.commissionType}
            </if>
            <if test="paramMap.remark!=null and paramMap.remark!='' ">
                AND t1.remark like concat('%',#{paramMap.remark},'%')
            </if>
            <if test="paramMap.way!=null and paramMap.way==1">
                AND t1.amount > 0
            </if>
            <if test="paramMap.way!=null and paramMap.way==2">
                AND t1.amount &lt; 0
            </if>
        ORDER BY
            t1.create_time DESC
    </select>
</mapper>
