<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountBankMapper">
    <resultMap id="MiniAccountBankMap" type="com.medusa.gruul.account.model.vo.MiniAccountBankVo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_no" property="bankNo"/>
        <result column="bank_user_name" property="bankUserName"/>
        <result column="company_name" property="companyName"/>
    </resultMap>
    <select id="queryList" resultMap="MiniAccountBankMap">
        SELECT
            t1.id,
            t1.user_id,
            t1.bank_name,
            t1.bank_no,
            t1.bank_user_name,
            t1.company_name
        FROM
            t_mini_account_bank t1
        WHERE
            t1.is_deleted = 0 and t1.user_id = #{param.userId}

    </select>
    <select id="getMiniAccountBank" resultMap="MiniAccountBankMap">
        SELECT
            t1.id,
            t1.user_id,
            t1.bank_name,
            t1.bank_no,
            t1.bank_user_name
        FROM
            t_mini_account_bank t1
        WHERE
            t1.is_deleted = 0 and t1.user_id = #{userId}

    </select>
</mapper>
