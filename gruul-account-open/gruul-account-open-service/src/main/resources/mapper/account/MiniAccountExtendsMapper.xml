<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountExtendsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.account.api.entity.MiniAccountExtends">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="user_id" property="userId"/>
        <result column="last_deal_time" property="lastDealTime"/>
        <result column="is_blacklist" property="isBlacklist"/>
        <result column="consume_num" property="consumeNum"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="join_blacklist_time" property="joinBlacklistTime"/>
        <result column="community_type" property="communityType"/>
        <result column="shop_user_id" property="shopUserId"/>
        <result column="current_status" property="currentStatus"/>
        <result column="last_choose_lcation" property="lastChooseLcation"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,community_type,shop_user_id,current_status,
        is_deleted,
        update_time,
        tenant_id,last_choose_lcation,
        id, user_id, last_deal_time, is_blacklist, consume_num,  last_login_time,join_blacklist_time
    </sql>

    <select id="selectByCurrentStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_mini_account_extends
        where
        is_deleted = 0
        AND user_id = #{userId}
        AND current_status = #{currentStatus}
    </select>
    <select id="getTeamShopUserIds" resultType="java.lang.String">
        SELECT
            t1.shop_user_id
        FROM
            t_mini_account_extends t1
        LEFT JOIN
            t_mini_account t2 ON t1.user_id = t2.user_id
        WHERE
            t1.is_deleted = 0
          AND t2.is_deleted = 0
          AND t2.whether_authorization = 1
          AND (
            t2.parent_id = #{userId} or t2.above_parent_id = #{userId}
            )
    </select>
    <select id="getDirectMemberQty" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            t_mini_account_extends t1
                LEFT JOIN t_mini_account t2 ON t1.user_id = t2.user_id
        WHERE
            t1.is_deleted = 0
          AND t2.is_deleted = 0
          AND t2.whether_authorization = 1
          and t2.parent_id = #{userId}
        <if test="memberLevelIds!=null and  memberLevelIds.size() > 0">
            AND t1.user_id  IN (
                SELECT
                    t3.user_id
                FROM
                    t_member_level_relation t3
                WHERE
                t3.is_deleted = 0
                and t3.member_level_id in
                <foreach collection="memberLevelIds" item="memberLevelId" open="(" separator="," close=")">
                    #{memberLevelId}
                </foreach>
            )
        </if>
    </select>
</mapper>
