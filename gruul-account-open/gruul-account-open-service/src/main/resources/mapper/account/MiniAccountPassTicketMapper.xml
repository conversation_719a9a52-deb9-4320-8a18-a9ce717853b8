<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountPassTicketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.account.api.entity.MiniAccountPassTicket">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="user_id" property="userId"/>
        <result column="pass_ticket_id" property="passTicketId"/>
        <result column="status" property="status"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,create_time,is_deleted,update_time, user_id, pass_ticket_id, status, start_time, end_time
    </sql>


</mapper>
