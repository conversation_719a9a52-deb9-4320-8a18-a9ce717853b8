<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountAddressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.account.api.entity.MiniAccountAddress">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="user_name" property="userName"/>
        <result column="phone" property="phone"/>
        <result column="post_code" property="postCode"/>
        <result column="is_default" property="isDefault"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="county" property="county"/>
        <result column="detail_Info" property="detailInfo"/>
        <result column="user_id" property="userId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        is_deleted,
        update_time,
        id, user_name, phone, post_code, is_default, province, city, county, detail_Info, user_id
    </sql>

</mapper>
