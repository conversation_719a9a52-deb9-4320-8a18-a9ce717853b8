<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountRestrictMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.account.api.entity.MiniAccountRestrict">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="restrict_type" property="restrictType"/>
        <result column="user_id" property="userId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        is_deleted,
        update_time,
        id, restrict_type, user_id
    </sql>

</mapper>
