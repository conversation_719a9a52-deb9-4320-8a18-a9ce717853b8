<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MemberLevelRelationMapper">

    <select id="getAgentRegionCodeList" resultType="java.lang.String">
        SELECT
            t1.agent_region_code
        FROM
            t_member_level_relation t1
        WHERE
            t1.is_deleted = 0
          AND IFNULL( t1.agent_region_code, '' )!= ''
          AND t1.member_type_id = #{memberTypeId}
    </select>

</mapper>
