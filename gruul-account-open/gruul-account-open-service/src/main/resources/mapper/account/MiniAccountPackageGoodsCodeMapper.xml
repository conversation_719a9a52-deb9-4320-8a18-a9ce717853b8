<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountPackageGoodsCodeMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.account.api.model.vo.ShopPackageGoodsCodeVo">
        <id column="id" property="id"/>
        <result column="package_id" property="packageId"/>
        <result column="package_name" property="packageName"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="shop_id" property="shopId"/>
        <result column="shop_name" property="shopName"/>
        <result column="verify_time" property="verifyTime"/>
        <result column="user_mobile" property="userMobile"/>
        <result column="user_name" property="userName"/>
        <result column="verify_user_id" property="verifyUserId"/>
        <result column="verify_user_name" property="verifyUserName"/>
        <result column="verify_user_mobile" property="verifyUserMobile"/>
        <result column="verify_code" property="verifyCode"/>
        <result column="package_order_id" property="packageOrderId"/>
        <result column="department_name" property="departmentName"/>
        <result column="department_code" property="departmentCode"/>
        <result column="emp_name" property="empName"/>
        <result column="emp_id" property="empId"/>
        <result column="account_name" property="accountName"/>
        <result column="store_front_name" property="storeFrontName"/>
    </resultMap>
    <resultMap id="ApiPackageGoodsCodeMap" type="com.medusa.gruul.account.model.vo.ApiPackageGoodsCodeVo">
        <result column="package_name" property="packageName"/>
        <result column="verify_time" property="verifyTime"/>
        <result column="product_name" property="productName"/>
        <result column="department_name" property="departmentName"/>
        <result column="emp_name" property="empName"/>
        <result column="store_front_name" property="storeFrontName"/>
        <result column="user_name" property="userName"/>
        <result column="account_name" property="accountName"/>
    </resultMap>
    <resultMap id="PackageGoodsCodeDetailMap" type="com.medusa.gruul.account.api.model.vo.PackageGoodsCodeDetailVo">
        <id column="id" property="id"/>
        <result column="package_name" property="packageName"/>
        <result column="product_name" property="productName"/>
        <result column="sku_name" property="skuName"/>
        <result column="verify_time" property="verifyTime"/>
        <result column="verify_number" property="verifyNumber"/>
        <result column="department_name" property="departmentName"/>
        <result column="emp_name" property="empName"/>
        <result column="can_times" property="canTimes"/>
        <result column="package_show_start_time" property="packageShowStartTime"/>
        <result column="package_show_end_time" property="packageShowEndTime"/>
        <result column="package_start_time" property="packageStartTime"/>
        <result column="package_end_time" property="packageEndTime"/>
        <result column="not_time" property="notTime"/>
        <result column="not_term" property="notTerm"/>
        <result column="mutex_flag" property="mutexFlag"/>
        <result column="verify_no" property="verifyNo"/>
        <result column="account_name" property="accountName"/>
        <result column="store_front_name" property="storeFrontName"/>
        <result column="verify_goods_name" property="verifyGoodsName"/>
        <result column="user_name" property="userName"/>
        <result column="user_mobile" property="userMobile"/>
    </resultMap>

    <!-- 管理端会员汇总报表查询映射结果 -->
    <resultMap id="ManageVerifyPackageGoodsStaticMap" type="com.medusa.gruul.account.api.model.vo.ManageVerifyPackageGoodsStaticVo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="package_Verify_Qty" property="packageVerifyQty"/>
        <result column="is_catalog" property="isCatalog"/>
    </resultMap>

    <select id="selectShopVerifyList"   resultMap="BaseResultMap">
        select
        t1.id as id,t3.package_id as package_id,t3.package_name as package_name,
        t2.product_id as product_id,t2.product_name as product_name,t1.verify_time as verify_time,
        t5.phone as user_mobile,t5.nike_name as user_name,t1.verify_user_id as verify_user_id,
        t6.nike_name as verify_user_name,t1.verify_code as verify_code,t6.phone as verify_user_mobile,t3.id as package_order_id,
        t1.department_name as department_name,t1.department_code as department_code,t1.emp_name as emp_name,t1.emp_id as emp_id,
        t1.verify_no,t6.nike_name as account_name,t6.store_front_name as store_front_name
        from
        t_mini_account_package_goods_code t1
        left join
        t_mini_account_package_goods t2 on t1.mini_account_package_goods_id = t2.id
        left join
        t_mini_account_package_order t3 on t2.main_id = t3.id
        left join
        t_mini_account_extends t4 on t4.shop_user_id = t1.user_id
        left join
        t_mini_account t5 on t5.user_id = t4.user_id
        left join
        t_platform_account_info t6 on t6.id = t1.verify_user_id
        where
        t1.is_deleted = 0 and t1.status = 101
        <if test="param.startTime!=null ">
            and t1.verify_Time >=#{param.startTime}
        </if>
        <if test="param.endTime!=null ">
            and t1.verify_Time &lt;= #{param.endTime}
        </if>
        <if test="param.departmentName!=null and param.departmentName!='' ">
            and t1.department_name like CONCAT('%',#{param.departmentName},'%')
        </if>
        <if test="param.accountName!=null and param.accountName!='' ">
            and t6.nike_name like CONCAT('%',#{param.accountName},'%')
        </if>
        <if test="param.storeFrontName!=null and param.storeFrontName!='' ">
            and t6.store_front_name like CONCAT('%',#{param.storeFrontName},'%')
        </if>
        <if test="param.departmentName!=null and param.departmentName!='' ">
            and t1.department_name like CONCAT('%',#{param.departmentName},'%')
        </if>
        <if test="param.empName!=null and param.empName!='' ">
            and t1.emp_name like CONCAT('%',#{param.empName},'%')
        </if>
        <if test="param.verifyCode!=null and param.verifyCode!='' ">
            and t1.verify_code like CONCAT('%',#{param.verifyCode},'%')
        </if>
        <if test="param.packageName!=null and param.packageName!='' ">
            and t3.package_name like CONCAT('%',#{param.packageName},'%')
        </if>
        <if test="param.packageGoodsName!=null and param.packageGoodsName!='' ">
            and t2.product_name like CONCAT('%',#{param.packageGoodsName},'%')
        </if>
        <if test="param.nickName!=null and param.nickName!='' ">
            and t5.nike_name like CONCAT('%',#{param.nickName},'%')
        </if>
        <if test="param.userMobile!=null and param.userMobile!='' ">
            and t5.phone like CONCAT('%',#{param.userMobile},'%')
        </if>
        order by t1.verify_Time desc
    </select>
    <select id="pagePackageGoodsCodeDetail"   resultMap="PackageGoodsCodeDetailMap">
        SELECT
        t1.id AS id,
        t3.package_name AS package_name,
        t2.product_name AS product_name,
        t2.sku_name AS sku_name,
        t1.verify_time AS verify_time,
        '1' AS verify_number,
        t1.department_name AS department_name,
        t1.emp_name AS emp_name,
        t1.can_times AS can_times,
        t4.package_show_start_time as package_show_start_time,
        t4.package_show_end_time as package_show_end_time,
        t4.package_start_time as package_start_time,
        t4.package_end_time as package_end_time,
        t2.not_time as not_time,
        t2.not_term as not_term,
        t2.mutex_flag as mutex_flag,
        t7.nike_name as  account_name,
        t7.store_front_name as  store_front_name,
        t8.name as verify_goods_name,
        t6.phone as user_mobile,
        t6.nike_name as user_name
        FROM
        t_mini_account_package_goods_code t1
        LEFT JOIN t_mini_account_package_goods t2 ON t1.mini_account_package_goods_id = t2.id
        LEFT JOIN t_mini_account_package_order t3 ON t2.main_id = t3.id
        left join t_product t4 on t4.id = t3.package_id
        left join t_mini_account_extends t5 on t5.shop_user_id = t3.user_id
        left join t_mini_account t6 on t6.user_id = t5.user_id
        left join t_platform_account_info t7 on t7.id = t1.account_id
        left join t_product t8 on t8.id = t1.verify_goods_id
        where
        t1.status = 101
        <if test="param.packageName!=null and param.packageName!='' ">
            and t3.package_name like CONCAT('%',#{param.packageName},'%')
        </if>
        <if test="param.productName!=null and param.productName!='' ">
            and t2.product_name like CONCAT('%',#{param.productName},'%')
        </if>
        <if test="param.userName!=null and param.userName!='' ">
            and t6.nike_name like CONCAT('%',#{param.userName},'%')
        </if>
        <if test="param.departmentName!=null and param.departmentName!='' ">
            and t1.department_name like CONCAT('%',#{param.departmentName},'%')
        </if>
        <if test="param.empName!=null and param.empName!='' ">
            and t1.emp_name like CONCAT('%',#{param.empName},'%')
        </if>
        <if test="param.accountName!=null and param.accountName!='' ">
            and t7.nike_name like CONCAT('%',#{param.accountName},'%')
        </if>
        <if test="param.storeFrontName!=null and param.storeFrontName!='' ">
            and t7.store_front_name like CONCAT('%',#{param.storeFrontName},'%')
        </if>
        <if test="param.startTime!=null ">
            and t1.verify_Time >=#{param.startTime}
        </if>
        <if test="param.endTime!=null ">
            and t1.verify_Time &lt;= #{param.endTime}
        </if>
        order by t1.verify_Time desc
    </select>
    <select id="pageApiPackageGoodsCode"   resultMap="ApiPackageGoodsCodeMap">
        SELECT
            t3.package_name AS package_name,
            t1.verify_time as verify_time,
            t2.product_name AS product_name,
            t1.department_name as department_name,
            t1.emp_name as emp_name,
            t1.store_front_name,
            t1.account_name,
            t5.nike_name as user_name

        FROM
            t_mini_account_package_goods_code t1
                LEFT JOIN t_mini_account_package_goods t2 ON t1.mini_account_package_goods_id = t2.id
                LEFT JOIN t_mini_account_package_order t3 ON t3.id = t2.main_id
                LEFT JOIN t_mini_account_extends t4 ON t4.shop_user_id = t1.user_id
                LEFT JOIN t_mini_account t5 ON t5.user_id = t4.user_id
        where
            (t1.user_id = #{param.userId} or t1.source_user_id = #{param.userId}) and t1.status = 101
        order by
            t1.verify_time desc
    </select>

    <!-- 门店、员工核销权益包次数汇总 -->
    <select id="manageVerifyPackageGoodsStatic" resultMap="ManageVerifyPackageGoodsStaticMap">
        SELECT  case when #{params.type} = 0 then b.class_code else a.account_id end as id, min(case when #{params.type} = 0 then b.store_full_name else a.account_name end) as name,
        min(case when #{params.type} = 0 then b.is_catalog else 0 end) as is_catalog,  IFNULL(count(distinct(a.id)),0) AS package_Verify_Qty
        FROM t_mini_account_package_goods_code a left join t_platform_lyd_store_front b on a.store_front_code like concat('', b.class_code, '%')
        left join t_product c on c.id = a.package_id
        WHERE a.is_deleted = 0 and a.status = 101
        <if test="params.type == 0">
            <if test="params.classCodeList!=null and  params.classCodeList.size() > 0">
                AND b.class_code in
                <foreach collection="params.classCodeList" item="classCode" open="(" separator="," close=")">
                    #{classCode}
                </foreach>
            </if>
        </if>
        <if test="params.type == 1">
            <if test="params.employeeIdList!=null and  params.employeeIdList.size() > 0">
                AND a.account_id in
                <foreach collection="params.employeeIdList" item="employeeId" open="(" separator="," close=")">
                    #{employeeId}
                </foreach>
            </if>
        </if>

        <if test="params.startTime!=null and params.startTime!=''">
            and a.verify_time>=#{params.startTime}
        </if>
        <if test="params.endTime!=null and params.endTime!=''">
            and  a.verify_time &lt;=#{params.endTime}
        </if>
        <if test="params.packageName!=null and params.packageName!=''">
            and c.name like CONCAT('%',#{param.packageName},'%')
        </if>
        group by case when #{params.type} = 0 then b.class_code else a.account_id end

    </select>

    <!-- 门店、员工核销权益包次数汇总合计值 -->
    <select id="manageVerifyPackageGoodsStaticTotal" resultMap="ManageVerifyPackageGoodsStaticMap">
        SELECT  '-1' as id, '总计' as name, '0' as is_catalog,  IFNULL(count(distinct(a.id)),0) AS package_Verify_Qty
        FROM t_mini_account_package_goods_code a left join t_platform_lyd_store_front b on a.store_front_code like concat('', b.class_code, '%')
        left join t_product c on c.id = a.package_id
        WHERE a.is_deleted = 0 and a.status = 101
        <if test="params.type == 0">
            <if test="params.classCodeList!=null and  params.classCodeList.size() > 0">
                AND b.class_code in
                <foreach collection="params.classCodeList" item="classCode" open="(" separator="," close=")">
                    #{classCode}
                </foreach>
            </if>
        </if>
        <if test="params.type == 1">
            <if test="params.employeeIdList!=null and  params.employeeIdList.size() > 0">
                AND a.account_id in
                <foreach collection="params.employeeIdList" item="employeeId" open="(" separator="," close=")">
                    #{employeeId}
                </foreach>
            </if>
        </if>
        <if test="params.startTime!=null and params.startTime!=''">
            and a.verify_time>=#{params.startTime}
        </if>
        <if test="params.endTime!=null and params.endTime!=''">
            and  a.verify_time &lt;=#{params.endTime}
        </if>
        <if test="params.packageName!=null and params.packageName!=''">
            and c.name like CONCAT('%',#{param.packageName},'%')
        </if>
    </select>

</mapper>
