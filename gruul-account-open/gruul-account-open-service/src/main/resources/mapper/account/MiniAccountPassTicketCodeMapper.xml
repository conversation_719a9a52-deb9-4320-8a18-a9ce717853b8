<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountPassTicketCodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.account.api.entity.MiniAccountPassTicketCode">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="user_id" property="userId"/>
        <result column="pass_ticket_id" property="passTicketId"/>
        <result column="mini_account_pass_ticket_id" property="miniAccountPassTicketId"/>
        <result column="shop_id" property="shopId"/>
        <result column="verify_code" property="verifyCode"/>
        <result column="status" property="status"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="verify_Time" property="verifyTime"/>
        <result column="verify_user_id" property="verifyUserId"/>
    </resultMap>


    <resultMap id="ShopTicketExcelResultMap" type="com.medusa.gruul.account.api.model.vo.ShopsPassTicketCodeExcelVo">
        <result column="user_Mobile" property="userMobile"/>
        <result column="nick_name" property="nickName"/>
        <result column="pass_ticket_name" property="passTicketName"/>
        <result column="verify_code" property="verifyCode"/>
        <result column="verify_user_mobile" property="verifyUserMobile"/>
        <result column="verify_Time" property="verifyTime"/>
    </resultMap>

    <resultMap id="ShopTicketResultMap" type="com.medusa.gruul.account.api.model.vo.ShopPassTicketCodeVo">
        <id column="id" property="id"/>
        <result column="pass_ticket_id" property="passTicketId"/>
        <result column="pass_ticket_name" property="passTicketName"/>
        <result column="mini_account_pass_ticket_id" property="miniAccountPassTicketId"/>
        <result column="user_id" property="userId"/>
        <result column="nick_name" property="nickName"/>
        <result column="user_Mobile" property="userMobile"/>
        <result column="shop_id" property="shopId"/>
        <result column="verify_code" property="verifyCode"/>
        <result column="verify_Time" property="verifyTime"/>
        <result column="verify_user_id" property="verifyUserId"/>
        <result column="verify_user_mobile" property="verifyUserMobile"/>
        <result column="verify_Nick_Name" property="verifyNickName"/>
        <result column="shop_Name" property="shopName"/>
        <result column="promotion" property="promotion"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,create_time,is_deleted,update_time, user_id, pass_ticket_id, mini_account_pass_ticket_id, shop_id, verify_code,
        status, start_time, end_time, verify_Time, verify_user_id
    </sql>

    <!-- 商家核销查询结果列 -->
    <sql id="Shop_Ticket_Column_List">
        t.id, t.pass_ticket_id, t4. ticket_name as pass_ticket_name, t.mini_account_pass_ticket_id, t.user_id, t2.nike_name nick_name,
        t2.phone as user_Mobile, t.shop_id, t.verify_code, t.verify_Time, t.verify_user_id, t5.phone as verify_user_mobile, t5.nike_name as verify_Nick_Name
    </sql>

    <!-- 商家自己核销查询结果列 -->
    <sql id="Shop_User_Ticket_Column_List">
        t.id, t.pass_ticket_id, t4.ticket_name as pass_ticket_name, t.mini_account_pass_ticket_id, t.user_id, t.shop_id, t.verify_code, t.verify_Time, t.verify_user_id,
        t5.name as shop_name, t4.promotion
    </sql>

    <!-- 查询商家核销记录 -->
    <select id="selectShopVerifyList" parameterType="Object"  resultMap="ShopTicketResultMap">
        SELECT <include refid="Shop_Ticket_Column_List"/>
        FROM t_mini_account_pass_ticket_code t left join t_mini_account_extends t1 on t.user_id = t1.shop_user_id
        left join t_mini_account t2 on t2.user_id = t1.user_id left join t_shop_pass_ticket t4 on t.pass_ticket_id = t4.id
        left join t_platform_account_info t5 on t.verify_user_id = t5.id
         where t.is_deleted = 0 and t.status = 101
        <if test="miniAccountPassTicketCodeParam.shopId!=null and miniAccountPassTicketCodeParam.shopId!='' ">
            and t.shop_id  = #{miniAccountPassTicketCodeParam.shopId}
        </if>
        <if test="miniAccountPassTicketCodeParam.nickName!=null and miniAccountPassTicketCodeParam.nickName!='' ">
            and t2.nike_name like CONCAT('%',#{miniAccountPassTicketCodeParam.nickName},'%')
        </if>
        <if test="miniAccountPassTicketCodeParam.userMobile!=null and miniAccountPassTicketCodeParam.userMobile!='' ">
            and t2.phone like CONCAT('%',#{miniAccountPassTicketCodeParam.userMobile},'%')
        </if>
        <if test="miniAccountPassTicketCodeParam.startTime!=null ">
            and t.verify_Time >=#{miniAccountPassTicketCodeParam.startTime}
        </if>
        <if test="miniAccountPassTicketCodeParam.endTime!=null ">
            and t.verify_Time &lt;= #{miniAccountPassTicketCodeParam.endTime}
        </if>
        <if test="miniAccountPassTicketCodeParam.verifyCode!=null and miniAccountPassTicketCodeParam.verifyCode!='' ">
            and t.verify_code like CONCAT('%',#{miniAccountPassTicketCodeParam.verifyCode},'%')
        </if>
        <if test="miniAccountPassTicketCodeParam.passTicketName!=null and miniAccountPassTicketCodeParam.passTicketName!='' ">
            and t4.ticket_name like CONCAT('%',#{miniAccountPassTicketCodeParam.passTicketName},'%')
        </if>
        <if test="miniAccountPassTicketCodeParam.verifyUserMobile!=null and miniAccountPassTicketCodeParam.verifyUserMobile!='' ">
            and t5.phone like CONCAT('%',#{miniAccountPassTicketCodeParam.verifyUserMobile},'%')
        </if>
        <if test="miniAccountPassTicketCodeParam.verifyTimeSort!=null and miniAccountPassTicketCodeParam.verifyTimeSort == 1">
            order by t.verify_Time asc
        </if>
        <if test="miniAccountPassTicketCodeParam.verifyTimeSort!=null and miniAccountPassTicketCodeParam.verifyTimeSort == 2">
            order by t.verify_Time desc
        </if>
    </select>

    <select id="selectShopVerifyExcelList" parameterType="Object"  resultMap="ShopTicketExcelResultMap">
        SELECT
        t2.phone as user_Mobile,
        t2.nike_name nick_name,
        t4. ticket_name as pass_ticket_name,
        t.verify_code, t.verify_Time, t5.phone as verify_user_mobile
        FROM t_mini_account_pass_ticket_code t left join t_mini_account_extends t1 on t.user_id = t1.shop_user_id
        left join t_mini_account t2 on t2.user_id = t1.user_id left join t_shop_pass_ticket t4 on t.pass_ticket_id = t4.id
        left join t_platform_account_info t5 on t.verify_user_id = t5.id
        where t.is_deleted = 0 and t.status = 101
        <if test="miniAccountPassTicketCodeParam.shopId!=null and miniAccountPassTicketCodeParam.shopId!='' ">
            and t.shop_id  = #{miniAccountPassTicketCodeParam.shopId}
        </if>
        <if test="miniAccountPassTicketCodeParam.nickName!=null and miniAccountPassTicketCodeParam.nickName!='' ">
            and t2.nike_name like CONCAT('%',#{miniAccountPassTicketCodeParam.nickName},'%')
        </if>
        <if test="miniAccountPassTicketCodeParam.userMobile!=null and miniAccountPassTicketCodeParam.userMobile!='' ">
            and t2.phone like CONCAT('%',#{miniAccountPassTicketCodeParam.userMobile},'%')
        </if>
        <if test="miniAccountPassTicketCodeParam.startTime!=null ">
            and t.verify_Time >=#{miniAccountPassTicketCodeParam.startTime}
        </if>
        <if test="miniAccountPassTicketCodeParam.endTime!=null ">
            and t.verify_Time &lt;= #{miniAccountPassTicketCodeParam.endTime}
        </if>
        <if test="miniAccountPassTicketCodeParam.verifyCode!=null and miniAccountPassTicketCodeParam.verifyCode!='' ">
            and t.verify_code like CONCAT('%',#{miniAccountPassTicketCodeParam.verifyCode},'%')
        </if>
        <if test="miniAccountPassTicketCodeParam.passTicketName!=null and miniAccountPassTicketCodeParam.passTicketName!='' ">
            and t4.ticket_name like CONCAT('%',#{miniAccountPassTicketCodeParam.passTicketName},'%')
        </if>
        <if test="miniAccountPassTicketCodeParam.verifyUserMobile!=null and miniAccountPassTicketCodeParam.verifyUserMobile!='' ">
            and t5.phone like CONCAT('%',#{miniAccountPassTicketCodeParam.verifyUserMobile},'%')
        </if>
        <if test="miniAccountPassTicketCodeParam.verifyTimeSort!=null and miniAccountPassTicketCodeParam.verifyTimeSort == 1">
            order by t.verify_Time asc
        </if>
        <if test="miniAccountPassTicketCodeParam.verifyTimeSort!=null and miniAccountPassTicketCodeParam.verifyTimeSort == 2">
            order by t.verify_Time desc
        </if>
    </select>

    <!-- 查询商家自己的核销记录 -->
    <select id="selectShopUserVerifyList" parameterType="Object"  resultMap="ShopTicketResultMap">
        SELECT <include refid="Shop_User_Ticket_Column_List"/>
        FROM t_mini_account_pass_ticket_code t left join t_shop_pass_ticket t4 on t.pass_ticket_id = t4.id
        left join t_shops_partner t5 on t.shop_id = t5.shop_id
        where t.is_deleted = 0 and t.status = 101
        <if test="miniAccountPassTicketCodeParam.verifyUserId!=null and miniAccountPassTicketCodeParam.verifyUserId!='' ">
            and t.verify_User_Id = #{miniAccountPassTicketCodeParam.verifyUserId}
        </if>
        <if test="miniAccountPassTicketCodeParam.startTime!=null ">
            and t.verify_Time >=#{miniAccountPassTicketCodeParam.startTime}
        </if>
        <if test="miniAccountPassTicketCodeParam.endTime!=null ">
            and t.verify_Time &lt;= #{miniAccountPassTicketCodeParam.endTime}
        </if>
        order by t.verify_Time desc
    </select>


</mapper>
