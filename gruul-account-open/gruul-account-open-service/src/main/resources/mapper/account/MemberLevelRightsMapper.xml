<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MemberLevelRightsMapper">

    <resultMap id="MMiniAccountMemberLevelRightsMap" type="com.medusa.gruul.account.api.entity.MemberLevelRights">
        <id column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="name" property="name"/>
        <result column="powerExplain" property="power_explain"/>
        <result column="icon" property="icon"/>
    </resultMap>

    <resultMap id="RightsVoMap" type="com.medusa.gruul.account.model.vo.MemberLevelRightsVo">
        <id column="foot_mark_id" property="footMarkId"/>
        <result column="user_id" property="userId"/>
        <result column="product_id" property="productId"/>
        <result column="product_pic" property="productPic"/>
        <result column="product_name" property="productName"/>
        <result column="product_price" property="productPrice"/>
        <result column="original_price" property="originalPrice"/>
        <result column="isSelected" property="isSelected"/>

    </resultMap>

    <select id="selectRightsList" resultMap="MMiniAccountMemberLevelRightsMap">
        SELECT id,type,name,power_explain,enable,icon FROM t_member_level_rights WHERE
        id in
        <foreach collection="rightsIdList" item="id"
                 index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </select>
    <select id="selectRightsVoAllList" resultMap="RightsVoMap">
        SELECT id,type,name,power_explain,enable,0 as isSelected FROM t_member_level_rights

    </select>

</mapper>