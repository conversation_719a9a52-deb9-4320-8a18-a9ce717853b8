<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountCouponCodeMapper">
    <resultMap id="ShopCouponResultMap" type="com.medusa.gruul.account.api.model.vo.ShopCouponCodeVo">
        <id column="id" property="id"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_name" property="couponName"/>
        <result column="mini_account_coupon_id" property="miniAccountCouponId"/>
        <result column="user_id" property="userId"/>
        <result column="nick_name" property="nickName"/>
        <result column="user_Mobile" property="userMobile"/>
        <result column="shop_id" property="shopId"/>
        <result column="verify_code" property="verifyCode"/>
        <result column="verify_Time" property="verifyTime"/>
        <result column="verify_user_id" property="verifyUserId"/>
        <result column="verify_user_mobile" property="verifyUserMobile"/>
        <result column="verify_Nick_Name" property="verifyNickName"/>
        <result column="shop_Name" property="shopName"/>
        <result column="promotion" property="promotion"/>
    </resultMap>

    <resultMap id="ShopCouponExcelResultMap" type="com.medusa.gruul.account.api.model.vo.ShopsCouponCodeExcelVo">
        <result column="user_Mobile" property="userMobile"/>
        <result column="nick_name" property="nickName"/>
        <result column="coupon_name" property="couponName"/>
        <result column="verify_code" property="verifyCode"/>
        <result column="verify_user_mobile" property="verifyUserMobile"/>
        <result column="verify_nick_name" property="verifyNickName"/>
        <result column="shop_name" property="shopName"/>
        <result column="verify_Time" property="verifyTime"/>
    </resultMap>

    <!-- 商家自己核销查询结果列 -->
    <sql id="Shop_User_Coupon_Column_List">
        t.id, t.coupon_id, t4.coupon_name as coupon_name, t.mini_account_coupon_id, t.user_id, t.shop_id, t.verify_code, t.verify_Time, t.verify_user_id,
        t5.name as shop_name, t4.promotion
    </sql>
    <!-- 查询商家自己的核销记录 -->
    <select id="selectShopUserVerifyList" parameterType="Object"  resultMap="ShopCouponResultMap">
        SELECT <include refid="Shop_User_Coupon_Column_List"/>
        FROM t_mini_account_coupon_code t left join t_shop_coupon t4 on t.coupon_id = t4.id
        left join t_shops_partner t5 on t.shop_id = t5.shop_id
        where t.is_deleted = 0 and t.status = 101
        <if test="param.verifyUserId!=null and param.verifyUserId!='' ">
            and t.verify_User_Id = #{param.verifyUserId}
        </if>
        <if test="param.startTime!=null ">
            and t.verify_Time >=#{param.startTime}
        </if>
        <if test="param.endTime!=null ">
            and t.verify_Time &lt;= #{param.endTime}
        </if>
        order by t.verify_Time desc
    </select>

    <!-- 商家核销查询结果列 -->
    <sql id="Shop_Coupon_Column_List">
        t.id, t.coupon_id, t4.coupon_name as coupon_name, t.mini_account_coupon_id, t.user_id, t2.nike_name nick_name,
        t2.phone as user_Mobile, t.shop_id, t.verify_code, t.verify_Time, t.verify_user_id, t5.phone as verify_user_mobile, t5.nike_name as verify_Nick_Name,
        t6.name as shop_name
    </sql>

    <!-- 查询商家核销记录 -->
    <select id="selectShopVerifyList" parameterType="Object"  resultMap="ShopCouponResultMap">
        SELECT <include refid="Shop_Coupon_Column_List"/>
        FROM t_mini_account_coupon_code t left join t_mini_account_extends t1 on t.user_id = t1.shop_user_id
        left join t_mini_account t2 on t2.user_id = t1.user_id left join t_shop_coupon t4 on t.coupon_id = t4.id
        left join t_platform_account_info t5 on t.verify_user_id = t5.id
        left join t_shops_partner t6 on t.shop_id = t6.shop_id
        where t.is_deleted = 0 and t.status = 101
        <if test="param.shopId!=null and param.shopId!='' ">
            and t.shop_id  = #{param.shopId}
        </if>
        <if test="param.nickName!=null and param.nickName!='' ">
            and t2.nike_name like CONCAT('%',#{param.nickName},'%')
        </if>
        <if test="param.userMobile!=null and param.userMobile!='' ">
            and t2.phone like CONCAT('%',#{param.userMobile},'%')
        </if>
        <if test="param.startTime!=null ">
            and t.verify_Time >=#{param.startTime}
        </if>
        <if test="param.endTime!=null ">
            and t.verify_Time &lt;= #{param.endTime}
        </if>
        <if test="param.verifyCode!=null and param.verifyCode!='' ">
            and t.verify_code like CONCAT('%',#{param.verifyCode},'%')
        </if>
        <if test="param.couponName!=null and param.couponName!='' ">
            and t4.coupon_name like CONCAT('%',#{param.couponName},'%')
        </if>
        <if test="param.verifyUserMobile!=null and param.verifyUserMobile!='' ">
            and t5.phone like CONCAT('%',#{param.verifyUserMobile},'%')
        </if>
        <if test="param.verifyNickName!=null and param.verifyNickName!='' ">
            and t5.nick_name like CONCAT('%',#{param.verifyNickName},'%')
        </if>
        <if test="param.shopName!=null and param.shopName!='' ">
            and t6.name like CONCAT('%',#{param.shopName},'%')
        </if>
        <if test="param.verifyTimeSort!=null and param.verifyTimeSort == 1">
            order by t.verify_Time asc
        </if>
        <if test="param.verifyTimeSort!=null and param.verifyTimeSort == 2">
            order by t.verify_Time desc
        </if>
    </select>


    <select id="selectShopVerifyExcelList" parameterType="Object"  resultMap="ShopCouponExcelResultMap">
        SELECT
        t2.phone as user_Mobile,
        t2.nike_name nick_name,
        t4.coupon_name as coupon_name,
        t.verify_code, t.verify_Time, t5.phone as verify_user_mobile, t5.nike_name as verify_Nick_Name,
        t6.name as shop_name
        FROM t_mini_account_coupon_code t left join t_mini_account_extends t1 on t.user_id = t1.shop_user_id
        left join t_mini_account t2 on t2.user_id = t1.user_id left join t_shop_coupon t4 on t.coupon_id = t4.id
        left join t_platform_account_info t5 on t.verify_user_id = t5.id
        left join t_shops_partner t6 on t.shop_id = t6.shop_id
        where t.is_deleted = 0 and t.status = 101
        <if test="param.shopId!=null and param.shopId!='' ">
            and t.shop_id  = #{paramshopId}
        </if>
        <if test="param.nickName!=null and param.nickName!='' ">
            and t2.nike_name like CONCAT('%',#{param.nickName},'%')
        </if>
        <if test="param.userMobile!=null and param.userMobile!='' ">
            and t2.phone like CONCAT('%',#{param.userMobile},'%')
        </if>
        <if test="param.startTime!=null ">
            and t.verify_Time >=#{param.startTime}
        </if>
        <if test="param.endTime!=null ">
            and t.verify_Time &lt;= #{param.endTime}
        </if>
        <if test="param.verifyCode!=null and param.verifyCode!='' ">
            and t.verify_code like CONCAT('%',#{param.verifyCode},'%')
        </if>
        <if test="param.couponName!=null and param.couponName!='' ">
            and t4.coupon_name like CONCAT('%',#{param.couponName},'%')
        </if>
        <if test="param.verifyUserMobile!=null and param.verifyUserMobile!='' ">
            and t5.phone like CONCAT('%',#{param.verifyUserMobile},'%')
        </if>
        <if test="param.shopName!=null and param.shopName!='' ">
            and t6.name like CONCAT('%',#{param.shopName},'%')
        </if>
        <if test="param.verifyTimeSort!=null and param.verifyTimeSort == 1">
            order by t.verify_Time asc
        </if>
        <if test="param.verifyTimeSort!=null and param.verifyTimeSort == 2">
            order by t.verify_Time desc
        </if>
    </select>
</mapper>
