<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MemberLevelRightsRelationMapper">
    <resultMap id="MiniAccountMemberPowerMap" type="com.medusa.gruul.account.api.entity.MemberLevelRightsRelation">
        <id column="id" property="id"/>
        <result column="member_level_id" property="memberLevelId"/>
        <result column="rights_id" property="rightsId"/>
        <result column="value" property="value"/>
    </resultMap>
    <!--    会员表和权益表的子表-->
    <select id="selectMemberPowerList" resultMap="MiniAccountMemberPowerMap">
        SELECT id,member_level_id,rights_id,`value` FROM t_member_level_rights_relation WHERE
        member_level_id in
        <foreach collection="memberLevelIdList" item="id"
                 index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </select>

<!--    <insert id="addMemberPower" parameterType="Object" >-->
<!--	  INSERT  INTO  t_member_level_rights_relation   /* 会员等级表关联的权益子表 */-->
<!--					(-->
<!--                        member_level_id,-->
<!--                        rights_id,-->
<!--                        value-->
<!--					)-->
<!--			values (-->
<!--                    #{memberLevelId,jdbcType=VARCHAR}-->
<!--                    ,#{rightsId,jdbcType=VARCHAR}-->
<!--                    ,#{value,jdbcType=VARCHAR}-->
<!--					)-->
<!--	</insert>-->




    <delete id="deleteMemberRights" parameterType="Object">
        DELETE 	FROM t_member_level_rights_relation 	/* 会员权益表的子表 */
        WHERE
        member_level_id = #{memberLevelId}
    </delete>

    <!--    会员表和权益表的子表   end-->
</mapper>