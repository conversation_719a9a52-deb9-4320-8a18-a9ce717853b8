<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountTagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.account.api.entity.MiniAccountTag">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="tag_name" property="tagName"/>
    </resultMap>

    <resultMap id="ChooseTagMap" type="com.medusa.gruul.account.api.model.vo.ChooseTagVo">
        <result column="tag_name" property="tagName"/>
        <result column="tag_id" property="tagId"/>
        <result column="account_num" property="accountNum"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        is_deleted,
        update_time,
        id, tag_name
    </sql>
    <select id="getChooseTag" resultMap="ChooseTagMap">
        SELECT
            t1.id AS tag_id,
            t1.tag_name
        FROM
            t_mini_account_tag t1
        WHERE
            t1.is_deleted = 0
        <if test="param.tagName!=null and param.tagName!='' ">
            and t1.tag_name like CONCAT('%',#{param.tagName},'%')
        </if>
        <if test="param.tagIds!=null and  param.tagIds.size() > 0">
            AND t1.id NOT IN
            <foreach collection="param.tagIds" item="tagId" open="(" separator="," close=")">
                #{tagId}
            </foreach>
        </if>
    </select>
    <select id="getChooseTagById" resultMap="ChooseTagMap">
        SELECT
            t1.id AS tag_id,
            t1.tag_name
        FROM
            t_mini_account_tag t1
        WHERE
            t1.is_deleted = 0 and t1.id = #{tagId}
    </select>
</mapper>
