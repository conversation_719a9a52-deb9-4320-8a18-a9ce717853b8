<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountCouponMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.account.api.model.vo.MiniAccountCouponSearchVo">
        <id column="id" property="id"/>
        <result column="coupon_name" property="couponName"/>
        <result column="promotion" property="promotion"/>
        <result column="use_name" property="useName"/>
        <result column="use_shop_name" property="useShopName"/>
        <result column="order_id" property="orderId"/>
        <result column="use_time" property="useTime"/>
        <result column="verify_Time" property="verifyTime"/>
        <result column="verify_user_id" property="verifyUserId"/>
        <result column="verify_user_mobile" property="verifyUserMobile"/>
        <result column="verify_nike_name" property="verifyNickName"/>
    </resultMap>

    <select id="getMiniAccountCouponSearchVo" resultMap="BaseResultMap">
            select
                t1.id,t1.coupon_name,t1.promotion,t1.use_name,t1.use_shop_name,t1.order_id,t1.use_time,t2.verify_Time,
                t2.verify_user_id,t3.phone as verify_user_mobile,t3.nike_name as verify_nike_name
            from
                t_mini_account_coupon t1
            left join
                t_mini_account_coupon_code t2 on t1.id = t2.mini_account_coupon_id
            left join
                t_platform_account_info t3 on t2.verify_user_id = t3.id
            where t1.status = 101
        <if test="paramMap.couponName!=null and paramMap.couponName!='' ">
            and t1.coupon_name like CONCAT('%',#{paramMap.couponName},'%')
        </if>
        <if test="paramMap.useName!=null and paramMap.useName!='' ">
            and t1.use_name like CONCAT('%',#{paramMap.useName},'%')
        </if>
        <if test="paramMap.useShopName!=null and paramMap.useShopName!='' ">
            and t1.use_shop_name like CONCAT('%',#{paramMap.useShopName},'%')
        </if>
        <if test="paramMap.orderId!=null and paramMap.orderId!='' ">
            and t1.order_id  = #{paramMap.orderId}
        </if>
        <if test="paramMap.startTime!=null ">
            and t1.use_time >=#{paramMap.startTime}
        </if>
        <if test="paramMap.endTime!=null ">
            and t1.use_time &lt;= #{paramMap.endTime}
        </if>
        order by use_time desc
    </select>
</mapper>
