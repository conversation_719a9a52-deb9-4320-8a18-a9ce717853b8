<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MemberLevelRuleProductMapper">
    <resultMap id="MemberLevelRuleProductMap" type="com.medusa.gruul.account.model.vo.MemberLevelRuleProductVo">
        <id column="id" property="id"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="sku_id" property="skuId"/>
        <result column="specs" property="specs"/>
        <result column="member_type_id" property="memberTypeId"/>
    </resultMap>
    <select id="getMemberLevelRuleProduct" resultMap="MemberLevelRuleProductMap">
        SELECT
            t1.id,
            t1.product_id,
            t2.NAME AS product_name,
            t1.sku_id,
            t3.specs,
            t1.member_type_id
        FROM
            t_member_level_rule_product t1
        LEFT JOIN
            t_product t2 ON t1.product_id = t2.id
        LEFT JOIN
            t_sku_stock t3 ON t3.id = t1.sku_id
        where
            t1.is_deleted = 0  and t1.member_type_id = #{memberTypeId}
    </select>
</mapper>
