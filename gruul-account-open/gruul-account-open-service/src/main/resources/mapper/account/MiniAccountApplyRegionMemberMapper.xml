<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountApplyRegionMemberMapper">
    <resultMap id="MiniAccountApplyRegionMemberMap" type="com.medusa.gruul.account.model.vo.MiniAccountApplyRegionMemberVo">
        <id column="id" property="id"/>
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="member_level" property="memberLevel"/>
        <result column="apply_agent_region_name" property="applyAgentRegionName"/>
        <result column="apply_agent_region_code" property="applyAgentRegionCode"/>
        <result column="region_type" property="regionType"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_reason" property="auditReason"/>
        <result column="audit_time" property="auditTime"/>
        <result column="audit_platform_user_id" property="auditPlatformUserId"/>
        <result column="audit_platform_user_name" property="auditPlatformUserName"/>
    </resultMap>
    <select id="pageMiniAccountApplyRegionMemberVo" resultMap="MiniAccountApplyRegionMemberMap">
        SELECT
            t1.id,
            t2.nike_name,
            t2.phone,
            t3.member_level,
            t1.apply_agent_region_name,
            t1.apply_agent_region_code,
            t1.region_type,
            t1.audit_status,
            t1.audit_reason,
            DATE_FORMAT( t1.audit_time, '%Y-%m-%d %H:%i:%s' ) AS audit_time,
            t1.audit_platform_user_id,
            t1.audit_platform_user_name
        FROM
            t_mini_account_apply_region_member t1
                LEFT JOIN t_mini_account t2 ON t1.user_id = t2.user_id
                LEFT JOIN t_member_level t3 ON t3.id = t1.apply_member_level_id
        where t1.is_deleted = 0
        <if test="param.nikeName!=null and param.nikeName!=''">
           and t2.nike_name like concat('%',#{param.nikeName},'%')
        </if>
        <if test="param.applyAgentRegionName!=null and param.applyAgentRegionName!=''">
            and t2.apply_agent_region_name like concat('%',#{param.applyAgentRegionName},'%')
        </if>
        <if test="param.auditStatus!=null and param.auditStatus!=''">
            and  t1.audit_status = #{param.auditStatus}
        </if>
        <if test="param.memberLevel!=null and param.memberLevel!=''">
            and t3.member_level like concat('%',#{param.memberLevel},'%')
        </if>
        <if test="param.userId!=null and param.userId!=''">
            and  t1.user_id = #{param.userId}
        </if>
        <if test="param.applyBeginTime != null">
            and DATE(t1.create_time) >= #{param.applyBeginTime}
        </if>
        <if test="param.applyEndTime != null">
            and DATE(t1.create_time) &amp;le #{param.applyEndTime}
        </if>
        ORDER BY
            t1.create_time DESC
    </select>
</mapper>
