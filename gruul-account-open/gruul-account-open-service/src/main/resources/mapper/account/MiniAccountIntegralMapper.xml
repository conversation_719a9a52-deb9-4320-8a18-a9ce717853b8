<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountIntegralMapper">
    <resultMap id="UserIntegralMap" type="com.medusa.gruul.account.model.vo.AccountIntegralVo">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="user_id" property="userId"/>
        <result column="order_id" property="orderId"/>
        <result column="type" property="type"/>
        <result column="integral" property="integral"/>
        <result column="remark" property="remark"/>
    </resultMap>
    <resultMap id="IntegralRankingMap" type="com.medusa.gruul.account.model.vo.IntegralRankingVo">
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="sale_integral" property="saleIntegral"/>
        <result column="integral" property="integral"/>
        <result column="all_integral" property="allIntegral"/>
    </resultMap>
    <resultMap id="MiniAccountIntegralManageMap" type="com.medusa.gruul.account.model.vo.MiniAccountIntegralManageVo">
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="member_level" property="memberLevel"/>
        <result column="first_login_time" property="firstLoginTime"/>
        <result column="change_time" property="changeTime"/>
        <result column="type" property="type"/>
        <result column="last_integral" property="lastIntegral"/>
        <result column="total_integral" property="totalIntegral"/>
        <result column="integral" property="integral"/>
        <result column="change_user" property="changeUser"/>
    </resultMap>
    <select id="selectAccountIntegral" resultMap="UserIntegralMap">
        select
        t1.id,
        t1.create_time,
        t1.user_id,
        t1.order_id,
        t1.type,
        t1.integral,
        t1.remark
        from
        t_mini_account_integral t1
        where
        t1.is_deleted = 0
        <if test="paramMap.userId!=null">
            and t1.user_id = #{paramMap.userId}
        </if>
        <if test="paramMap.type!=null and paramMap.type == 1">
            and t1.integral>=0
        </if>
        <if test="paramMap.type!=null and paramMap.type == 2">
            and t1.integral&lt;0
        </if>
        order by t1.create_time desc
    </select>
    <select id="searchIntegralRanking" resultMap="IntegralRankingMap">
        SELECT
        t2.nike_name,
        t2.phone,
        sum(t1.integral) AS all_integral
        FROM
        t_mini_account t2
        LEFT JOIN
        t_mini_account_integral t1 ON t1.user_id = t2.user_id AND t1.is_deleted = 0
        <if test="paramMap.startTime!=null and paramMap.startTime!='' and paramMap.endTime!=null and paramMap.endTime!=''">
            and t1.create_time >= #{paramMap.startTime} and t1.create_time &lt;= #{paramMap.endTime}
        </if>
        WHERE
        t2.is_deleted = 0 AND t2.whether_authorization = 1
        <if test="paramMap.nikeName!=null and paramMap.nikeName!=''">
            and t2.nike_name like CONCAT('%',#{paramMap.nikeName},'%')
        </if>

        <if test="paramMap.phone!=null and paramMap.phone!=''">
            and t2.phone like CONCAT('%',#{paramMap.phone},'%')
        </if>
        GROUP BY
        t2.nike_name,
        t2.phone
        HAVING 1=1
        <if test="paramMap.allIntegralFlag!=null and paramMap.allIntegralFlag == true">
            and sum(t1.integral) !=0
        </if>

        <if test="paramMap.allIntegralSort != null and paramMap.allIntegralSort==1">
            order by sum(t1.integral) asc
        </if>
        <if test="paramMap.allIntegralSort != null and paramMap.allIntegralSort==2">
            order by sum(t1.integral) desc
        </if>
    </select>
    <select id="searchMiniAccountIntegralManage" resultMap="MiniAccountIntegralManageMap">
        SELECT
        t2.nike_name,
        t2.phone,
        t3.member_level,
        DATE_FORMAT( t2.first_login_time, '%Y-%m-%d %H:%i:%s' ) AS first_login_time,
        DATE_FORMAT( t1.create_time, '%Y-%m-%d %H:%i:%s' ) AS change_time,
        t1.type,
        t1.last_integral,
        t1.total_integral,
        t1.integral,
        case t1.source when 0 then '系统自动' when 1 then t1.platform_user_name  end as change_user
        FROM
        t_mini_account_integral t1
        LEFT JOIN t_mini_account t2 ON t1.user_id = t2.user_id
        LEFT JOIN t_member_level t3 ON t2.member_level_id = t3.id
        where
        t1.is_deleted = 0
        <if test="paramMap.nikeName!=null and paramMap.nikeName!='' ">
            AND t2.nike_name like concat('%',#{paramMap.nikeName},'%')
        </if>
        <if test="paramMap.phone!=null and paramMap.phone!='' ">
            AND t2.phone like concat('%',#{paramMap.phone},'%')
        </if>
        <if test="paramMap.memberLevelId!=null and paramMap.memberLevelId!='' ">
            AND t3.id = #{paramMap.memberLevelId}
        </if>
        <if test="paramMap.startTime!=null and paramMap.startTime!='' and paramMap.endTime!=null and paramMap.endTime!=''">
            and t1.create_time >= #{paramMap.startTime} and t1.create_time &lt;= #{paramMap.endTime}
        </if>
        <if test="paramMap.source!=null and paramMap.source!=-1">
            AND t1.source = #{paramMap.source}
        </if>
        ORDER BY
        t1.create_time DESC,t1.type DESC
    </select>
</mapper>
