<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MemberLevelRuleMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="MemberLevelRuleMap" type="com.medusa.gruul.account.model.vo.MemberLevelRuleVo">
        <id column="id" property="id"/>
        <result column="member_level_id" property="memberLevelId"/>
        <result column="member_level" property="memberLevel"/>
        <result column="amount_start" property="amountStart"/>
        <result column="amount_end" property="amountEnd"/>
        <result column="integral_start" property="integralStart"/>
        <result column="integral_end" property="integralEnd"/>
        <result column="disable" property="disable"/>
        <result column="amount_flag" property="amountFlag"/>
        <result column="integral_flag" property="integralFlag"/>
        <result column="sort" property="sort"/>
        <result column="member_amount_start" property="memberAmountStart"/>
        <result column="member_amount_end" property="memberAmountEnd"/>
        <result column="member_amount_flag" property="memberAmountFlag"/>
        <result column="again_flag" property="againFlag"/>
        <result column="direct_member_qty" property="directMemberQty"/>
        <result column="direct_low_member_level_id" property="directLowMemberLevelId"/>
        <result column="product_qty" property="productQty"/>
        <result column="product_amount" property="productAmount"/>
        <result column="team_amount_start" property="teamAmountStart"/>
        <result column="team_amount_end" property="teamAmountEnd"/>
        <result column="pre_low_member_level_id" property="preLowMemberLevelId"/>
        <result column="direct_condition_relation" property="directConditionRelation"/>
        <result column="direct_member_qty2" property="directMemberQty2"/>
        <result column="direct_low_member_level_id2" property="directLowMemberLevelId2"/>
        <result column="team_times" property="teamTimes"/>
        <result column="apply_shop_partner" property="applyShopPartner"/>
    </resultMap>

    <select id="getMemberLevelRule" resultMap="MemberLevelRuleMap">
        SELECT
            t2.id,
            t1.id AS member_level_id,
            t1.member_level,
            t2.amount_start,
            t2.amount_end,
            t2.integral_start,
            t2.integral_end,
            t1.DISABLE,
            t2.amount_flag,
            t2.integral_flag,
            t2.sort,
            t2.member_amount_start,
            t2.member_amount_end,
            t2.member_amount_flag,
            t2.again_flag,
            t2.direct_member_qty,
            t2.direct_low_member_level_id,
            t2.product_qty,
            t2.product_amount,
            t2.team_amount_start,
            t2.team_amount_end,
            t2.direct_condition_relation,
            t2.direct_member_qty2,
            t2.direct_low_member_level_id2,
            t2.team_times,
            t2.pre_low_member_level_id,
            t2.apply_shop_partner
        FROM
            t_member_level t1
                LEFT JOIN t_member_level_rule t2 ON t1.id = t2.member_level_id and t2.is_deleted = 0
        WHERE
            t1.is_deleted = 0 and t1.member_type_id = #{memberTypeId}
        ORDER BY
            t2.sort DESC,
            t1.LEVEL ASC
    </select>
    <select id="getMemberLevelIds" resultType="java.lang.String">
        SELECT
            t1.member_level_id
        FROM
            t_member_level_rule t1
        WHERE
            t1.is_deleted = 0
          and t1.sort >= #{sort}
          and t1.main_id = #{mainId}
    </select>
</mapper>
