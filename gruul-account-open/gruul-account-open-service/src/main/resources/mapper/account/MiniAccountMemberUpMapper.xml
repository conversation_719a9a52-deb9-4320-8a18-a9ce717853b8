<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountMemberUpMapper">

    <select id="getSumAmount" resultType="java.math.BigDecimal">
        SELECT
            sum(t1.amount)
        FROM
            t_mini_account_member_up t1
        where t1.is_deleted = 0
        <if test="shopUserIds!=null and  shopUserIds.size() > 0">
            AND t1.user_id IN
            <foreach collection="shopUserIds" item="shopUserId" open="(" separator="," close=")">
                #{shopUserId}
            </foreach>
        </if>
    </select>

</mapper>
