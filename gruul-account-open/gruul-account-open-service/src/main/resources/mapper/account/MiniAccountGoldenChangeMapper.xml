<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MiniAccountGoldenChangeMapper">
    <resultMap id="MiniAccountGoldenChangeMap" type="com.medusa.gruul.account.model.vo.MiniAccountGoldenChangeVo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="nike_name" property="nikeName"/>
        <result column="phone" property="phone"/>
        <result column="type" property="type"/>
        <result column="amount" property="amount"/>
        <result column="audit_time" property="auditTime"/>
        <result column="audit_user_id" property="auditUserId"/>
        <result column="audit_user_name" property="auditUserName"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_reason" property="auditReason"/>
    </resultMap>
    <select id="queryList" resultMap="MiniAccountGoldenChangeMap">
        SELECT
            t1.id,
            t1.user_id,
            t3.nike_name,
            t3.phone,
            t1.type,
            t1.amount,
            t1.create_time,
            t1.audit_time,
            t1.audit_user_id,
            t1.audit_user_name,
            t1.audit_status,
            t1.audit_reason
        FROM
            t_mini_account_golden_change t1
                LEFT JOIN t_mini_account_extends t2 ON t1.user_id = t2.shop_user_id
                LEFT JOIN t_mini_account t3 ON t2.user_id = t3.user_id
        WHERE
            t1.is_deleted = 0
        <if test="paramMap.nikeName!=null and paramMap.nikeName!='' ">
            AND t3.nike_name like concat('%',#{paramMap.nikeName},'%')
        </if>
        <if test="paramMap.phone!=null and paramMap.phone!='' ">
            AND t3.phone like concat('%',#{paramMap.phone},'%')
        </if>
        <if test="paramMap.auditUserName!=null and paramMap.auditUserName!='' ">
            AND t1.audit_user_name like concat('%',#{paramMap.auditUserName},'%')
        </if>
        <if test="paramMap.auditStatus!=null ">
            AND t1.audit_status = #{paramMap.auditStatus}
        </if>
        ORDER BY
            t1.create_time DESC
    </select>

    <select id="getMiniAccountGoldenChange" resultMap="MiniAccountGoldenChangeMap">
        SELECT
        t1.id,
        t1.user_id,
        t3.nike_name,
        t3.phone,
        t1.type,
        t1.amount,
        t1.create_time,
        t1.audit_time,
        t1.audit_user_id,
        t1.audit_user_name,
        t1.audit_status,
        t1.audit_reason
        FROM
        t_mini_account_golden_change t1
        LEFT JOIN t_mini_account_extends t2 ON t1.user_id = t2.shop_user_id
        LEFT JOIN t_mini_account t3 ON t2.user_id = t3.user_id
        WHERE
        t1.is_deleted = 0 and t1.id = #{paramMap.id}
    </select>
</mapper>
