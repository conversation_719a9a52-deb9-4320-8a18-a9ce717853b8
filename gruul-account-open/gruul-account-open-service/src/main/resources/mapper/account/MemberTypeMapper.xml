<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.account.mapper.MemberTypeMapper">
    <resultMap id="MiniAccountMemberMap" type="com.medusa.gruul.account.model.vo.MemberTypeVo">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="default_type" property="defaultType"/>
        <result column="region_flag" property="regionFlag"/>
        <result column="together_flag" property="togetherFlag"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
    </resultMap>
    <select id="getMemberTypeList" resultMap="MiniAccountMemberMap">
        SELECT
            t2.id,
            t2.name,
            t2.default_type
        FROM
            t_member_level_relation t1
                LEFT JOIN t_member_type t2 ON t1.member_type_id = t2.id
                LEFT JOIN t_member_level t3 ON t3.id = t1.member_level_id
        WHERE
            t1.is_deleted = 0
          AND t2.is_deleted = 0
          AND t3.is_deleted = 0
          AND t2.status = 1
          AND t3.disable = 0
          AND t3.member_flag = 1
          AND t1.user_id = #{userId}
    </select>
    <select id="getMemberType" resultMap="MiniAccountMemberMap">
        SELECT
            t1.id,
            t1.name,
            t1.default_type,
            t1.region_flag,
            t1.together_flag,
            t1.status,
            t1.remark
        FROM
            t_member_type t1
        WHERE
            t1.is_deleted = 0
        <if test="param.status != null">
            and t1.status = #{param.status}
        </if>
        order by t1.id asc
    </select>
</mapper>
