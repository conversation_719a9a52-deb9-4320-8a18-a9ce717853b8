server:
  port: 10450
  compression:
    enabled: true
spring:
  application:
    name: account-open
  cloud:
    nacos:
      discovery:
        #nacos的ip
        server-addr: 127.0.0.1:8848
        #自己电脑的ip
        ip: 127.0.0.1
        namespace: cdd46b31-59b3-400f-8575-d06d0c957231
      #        register-enabled: false
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: cdd46b31-59b3-400f-8575-d06d0c957231
        file-extension: yml
        shared-dataids: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  autoconfigure:
    exclude: org.springframework.cloud.gateway.config.GatewayAutoConfiguration,org.springframework.cloud.gateway.config.GatewayClassPathWarningAutoConfiguration
  profiles:
    active: open
# Logger Config
logging:
  file: /log/mall/account-open/application.log
  pattern:
    file: '%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID}){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx'
  level:
    com.alibaba.nacos: error
    root: debug

snowflake:
  workerId: 1
  datacenterId: 1
geo:
  key: d7773e4b4416c97def00932c317fb642