package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:04 2025/5/20
 */
@Data
@ApiModel(value = "MemberTypeParam 实体", description = "会员类型查询 param")
public class MemberTypeParam extends QueryParam {

    /**
     * 状态->0-停用，1-启用
     */
    @ApiModelProperty(value = "状态->0-停用，1-启用")
    private Integer status;

}
