package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: 提现记录param
 * @Date: Created in 14:29 2023/8/31
 */
@Data
@ApiModel(value = "MiniAccountCommissionCashParam 实体", description = "提现记录param")
public class MiniAccountCommissionCashParam extends QueryParam {

    /**
     * 用户id
     */
    @ApiModelProperty(value = "提现单号")
    private String id;


    /**
     * 提现人：姓名或手机号
     */
    @ApiModelProperty(value = "提现人：姓名或手机号")
    private String cashMessage;


    /**
     * 提现金额
     */
    @ApiModelProperty(value = "提现金额")
    private BigDecimal amount;

    /**
     * 提交开始时间
     */
    @ApiModelProperty(value = "提交开始时间")
    private LocalDateTime startTime;

    /**
     * 提交结束时间
     */
    @ApiModelProperty(value = "提交结束时间")
    private LocalDateTime endTime;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间:yyyy-MM-dd")
    private String startTimeStr;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间:yyyy-MM-dd")
    private String endTimeStr;

    /**
     * 状态:100->待审核;101->已审核;300->已放款
     */
    @ApiModelProperty(value = "状态:100->待审核;101->已审核;300->已放款")
    private String status;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型:1.近七天，2.近30天")
    private Integer type;

    /**
     * 店铺用户id
     */
    private String shopUserId;

    /**
     * 时间类型时间
     */
    private LocalDateTime typeTime;


    /**
     * 申请时间排序:1.正序，2.倒序
     */
    @ApiModelProperty(value = "申请时间排序:1.正序，2.倒序")
    private Integer createTimeSort;

    /**
     * 申请金额排序:1.正序，2.倒序
     */
    @ApiModelProperty(value = "申请金额排序:1.正序，2.倒序")
    private Integer amountSort;

    /**
     * 放款时间排序:1.正序，2.倒序
     */
    @ApiModelProperty(value = "放款时间排序:1.正序，2.倒序")
    private Integer payTimeSort;
}
