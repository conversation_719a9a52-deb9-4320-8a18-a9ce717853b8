package com.medusa.gruul.account.model.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会员-佣金明细查询实体
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MiniAccountCommissionParam 实体", description = "会员-佣金明细 param")
@Data
public class MiniAccountCommissionParam extends QueryParam {

    /**
     * 佣金类型:100->团队分佣;佣金类型:101->奖励佣金;佣金类型:102->奖励提成;200->佣金提现;300->删除订单
     */
    @ApiModelProperty(value = "佣金类型:100->团队分佣;佣金类型:101->奖励佣金;佣金类型:102->奖励提成;200->佣金提现;300->删除订单")
    private Integer commissionType;

    @ApiModelProperty(value = "开始时间：yyyy-MM-dd")
    private String startTime;

    @ApiModelProperty(value = "结束时间：yyyy-MM-dd")
    private String endTime;

    @ApiModelProperty(value = "小程序用户id")
    private String shopUserId;


}
