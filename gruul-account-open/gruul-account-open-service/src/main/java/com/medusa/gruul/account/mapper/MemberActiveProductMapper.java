package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.account.api.entity.MemberActiveProduct;
import com.medusa.gruul.account.model.dto.MemberActiveSettingDto;
import com.medusa.gruul.account.model.vo.MemberActiveProductVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:13 2025/6/3
 */
@Mapper
public interface MemberActiveProductMapper extends BaseMapper<MemberActiveProduct> {
    /**
     * 查询激活会员商品列表
     * @return
     */
    List<MemberActiveProductVo>getList();

}
