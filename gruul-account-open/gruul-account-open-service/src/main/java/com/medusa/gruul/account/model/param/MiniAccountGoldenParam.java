package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MiniAccountGoldenParam 实体", description = "会员-金豆明细 param")
@Data
public class MiniAccountGoldenParam extends QueryParam {

    @ApiModelProperty(value = "开始时间:yyyy-MM-dd")
    private String startTime;

    @ApiModelProperty(value = "结束时间:yyyy-MM-dd")
    private String endTime;

    @ApiModelProperty(value = "类型：1.来源；2.使用")
    private Integer type;

    @ApiModelProperty(value = "小程序用户id")
    private String shopUserId;
}
