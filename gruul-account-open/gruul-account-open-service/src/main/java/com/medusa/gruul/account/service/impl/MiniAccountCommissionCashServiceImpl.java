package com.medusa.gruul.account.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.api.enums.CommissionTypeEnum;
import com.medusa.gruul.account.api.enums.OauthTypeEnum;
import com.medusa.gruul.account.api.enums.OfflinePayTypeEnum;
import com.medusa.gruul.account.api.enums.PayTypeEnum;
import com.medusa.gruul.account.api.model.message.AccountCommissionCashMessage;
import com.medusa.gruul.account.api.model.message.UpdateCommissionMessage;
import com.medusa.gruul.account.mapper.MiniAccountCommissionCashMapper;
import com.medusa.gruul.account.model.dto.MiniAccountCommissionCashDto;
import com.medusa.gruul.account.model.dto.MiniAccountCommissionDto;
import com.medusa.gruul.account.model.dto.MiniAccountManualCommissionCashDto;
import com.medusa.gruul.account.model.param.MiniAccountBankParam;
import com.medusa.gruul.account.model.param.MiniAccountCommissionCashParam;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.mq.CashMessage;
import com.medusa.gruul.account.mq.Sender;
import com.medusa.gruul.account.service.*;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.*;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.ApproveDataParam;
import com.medusa.gruul.common.dto.CurPcUserInfoDto;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.order.api.model.OrderDeliveryProxyMessage;
import com.medusa.gruul.payment.api.entity.EntPay;
import com.medusa.gruul.payment.api.enums.CheckNameEnum;
import com.medusa.gruul.payment.api.enums.PayChannelEnum;
import com.medusa.gruul.payment.api.feign.RemotePaymentService;
import com.medusa.gruul.payment.api.model.dto.EntQueryPayDto;
import com.medusa.gruul.payment.api.model.message.WxTransferSceneV3Message;
import com.medusa.gruul.payment.api.model.message.WxTransferV3Message;
import com.medusa.gruul.payment.api.model.param.EntPayReQuestParam;
import com.medusa.gruul.payment.api.util.GlobalConstant;
import com.medusa.gruul.payment.api.util.ParamMd5SignUtils;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.dto.ShopConfigDto;
import com.medusa.gruul.platform.api.model.vo.MiniInfoVo;
import com.medusa.gruul.platform.api.model.vo.PayInfoVo;
import com.medusa.gruul.shops.api.entity.ShopCommissionRule;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Author: plh
 * @Description: 佣金提现服务实现类
 * @Date: Created in 10:22 2023/8/31
 */
@Service
@Slf4j
public class MiniAccountCommissionCashServiceImpl extends ServiceImpl<MiniAccountCommissionCashMapper, MiniAccountCommissionCash> implements IMiniAccountCommissionCashService {

    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;
    @Autowired
    private IMiniAccountService miniAccountService;
    @Autowired
    private RemoteShopsService remoteShopsService;
    @Autowired
    private IMiniAccountExtendsService miniAccountExtendsService;
    @Autowired
    private IMiniAccountOauthsService miniAccountOauthsService;
    @Autowired
    private IMemberLevelService memberLevelService;
    @Autowired
    private Sender sender;
    @Autowired
    private RemotePaymentService remotePaymentService;

    @Autowired
    private IMiniAccountBankService miniAccountBankService;

    @Autowired
    private IMemberLevelRelationService memberLevelRelationService;

    @Autowired
    private IMiniAccountCommissionService miniAccountCommissionService;
    @Override
    @Transactional
    public Map<String,String> add(MiniAccountCommissionCashDto miniAccountCommissionCashDto) {
        Map<String,String> resultData = new HashMap<>();
        Integer commissionCashFlag  = 0;
        // 非shop_user_id，是miniAccount的user_id
        String userId = CurUserUtil.getMiniReqeustAccountInfo().getUserId();
        LambdaQueryWrapper<MiniAccount> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getUserId, userId);
        MiniAccount account = this.miniAccountService.getOne(wrapper);
        if(account==null){
            throw new ServiceException("用户不存在！");
        }

        //获取主店铺id
        ShopsPartner shopsPartnerMain = remoteShopsService.getShopsPartnerMain();


        //用户可提现金额
        BigDecimal currentCommission = account.getCurrentCommission();
        //为空取0
        if(currentCommission==null){
            currentCommission = BigDecimal.ZERO;
        }
        //提现金额
        BigDecimal amount = miniAccountCommissionCashDto.getAmount();
        if(currentCommission.compareTo(amount)<0){
            throw new ServiceException("提现金额不能超过用户可提现金额！");
        }
        // 获取佣金规则
        ShopCommissionRule rule = remoteShopsService.getCommissionRule();
        // 提现最低金额
        BigDecimal minCashAmount = rule.getMinCashAmount();
        if(amount.compareTo(minCashAmount)<0){
            throw new ServiceException("您的提现金额小于最小提现金额"+minCashAmount.stripTrailingZeros().toPlainString()+"元，无法提现！");
        }
        //最高提现金额
        BigDecimal maxCashAmount = rule.getMaxCashAmount();
        if(maxCashAmount.compareTo(amount)<0){
            throw new ServiceException("您的提现金额超出最大提现金额"+maxCashAmount.stripTrailingZeros().toPlainString()+"元，无法提现！");
        }
        //每日提现次数
        Integer cashTimes = rule.getCashTimes();
        //当前时间
        LocalDateTime now = LocalDateTime.now();
        //当天开始时间
        LocalDateTime dayStart = LocalDateTimeUtils.getDayStart(now);
        //当天结束时间
        LocalDateTime dayEnd = LocalDateTimeUtils.getDayEnd(now);

        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(userId);
        String startTime = LocalDateTimeUtils.formatTime(dayStart, "yyyy-MM-dd HH:mm:ss");
        String endTime = LocalDateTimeUtils.formatTime(dayEnd, "yyyy-MM-dd HH:mm:ss");
        LambdaQueryWrapper<MiniAccountCommissionCash>commissionCashLambdaQueryWrapper = new LambdaQueryWrapper<>();
        commissionCashLambdaQueryWrapper.ge(MiniAccountCommissionCash::getCreateTime,startTime)
                .le(MiniAccountCommissionCash::getCreateTime,endTime)
                .eq(MiniAccountCommissionCash::getUserId, miniAccountExtends.getShopUserId());
        Integer count = this.baseMapper.selectCount(commissionCashLambdaQueryWrapper);
        if(cashTimes<=count){
            throw new ServiceException("您今天提现次数已超出"+cashTimes+"次，请您明日再提现！");
        }

        MiniAccountCommissionCash miniAccountCommissionCash = new MiniAccountCommissionCash();
        miniAccountCommissionCash.setUserId(miniAccountExtends.getShopUserId());
        miniAccountCommissionCash.setAmount(amount);
        //提现率
        BigDecimal cashRate = rule.getCashRate();
        miniAccountCommissionCash.setCashRate(cashRate);
        //实际体现金额
        BigDecimal result = amount.multiply(cashRate.divide(new BigDecimal(100)));
        miniAccountCommissionCash.setCashAmount(amount.subtract(result));
        miniAccountCommissionCash.setStatus(CommissionCashEnum.IN_REVIEW.getStatus());


        //判断支付方式不能为空
        if(miniAccountCommissionCashDto.getPayType() == null
                || miniAccountCommissionCashDto.getPayType().equals("")){
            throw new ServiceException("支付方式不能为空！");
        }

        if(miniAccountCommissionCashDto.getPayType()!=PayTypeEnum.OFFLINE_PAY.getType()
                &&miniAccountCommissionCashDto.getPayType()!=PayTypeEnum.WX_PAY.getType()){
            throw new ServiceException("支付方式不正确！");
        }

        miniAccountCommissionCash.setPayType(miniAccountCommissionCashDto.getPayType());
        //判断线下支付方式不能为空
        if(miniAccountCommissionCashDto.getPayType()==PayTypeEnum.OFFLINE_PAY.getType()){
            if(miniAccountCommissionCashDto.getOfflinePayType() == null
                    || miniAccountCommissionCashDto.getOfflinePayType().equals("") ){
                throw new ServiceException("线下支付方式不能为空！");
            }

            if(miniAccountCommissionCashDto.getOfflinePayType() != OfflinePayTypeEnum.BANK_PAY.getType()
                    && miniAccountCommissionCashDto.getOfflinePayType() !=OfflinePayTypeEnum.WX_PAY.getType()
                    && miniAccountCommissionCashDto.getOfflinePayType() !=OfflinePayTypeEnum.ALIPAY_PAY.getType()){
                throw new ServiceException("线下支付方式不正确！");
            }

            miniAccountCommissionCash.setOfflinePayType(miniAccountCommissionCashDto.getOfflinePayType());
            //银行卡
            if(miniAccountCommissionCashDto.getOfflinePayType() == OfflinePayTypeEnum.BANK_PAY.getType()){
                Long accountBankId = miniAccountCommissionCashDto.getAccountBankId();
                if(accountBankId == null || accountBankId.equals("")){
                    throw new ServiceException("用户银行卡id不能为空！");
                }
                //根据用户银行卡id查询银行卡信息
                MiniAccountBank miniAccountBank = miniAccountBankService.getById(accountBankId);
                if(miniAccountBank == null || accountBankId.equals("")){
                    throw new ServiceException("用户银行卡不存在！");
                }
                miniAccountCommissionCash.setAccountBankId(accountBankId);
                miniAccountCommissionCash.setBankName(miniAccountBank.getBankName());
                miniAccountCommissionCash.setBankNo(miniAccountBank.getBankNo());
                miniAccountCommissionCash.setBankUserName(miniAccountBank.getBankUserName());


            }
            //微信收款码
            if(miniAccountCommissionCashDto.getOfflinePayType() == OfflinePayTypeEnum.WX_PAY.getType()){
                miniAccountCommissionCash.setWxAccountUrl(miniAccountExtends.getWxAccountUrl());
            }
            //支付宝收款码
            if(miniAccountCommissionCashDto.getOfflinePayType() == OfflinePayTypeEnum.ALIPAY_PAY.getType()){
                miniAccountCommissionCash.setAlipayAccountUrl(miniAccountExtends.getAlipayAccountUrl());
            }
        }

        int insert = this.baseMapper.insert(miniAccountCommissionCash);
        if(insert>0){
            account.setCurrentCommission(currentCommission.subtract(miniAccountCommissionCash.getAmount()));
            miniAccountService.updateById(account);
        }

        //获取主店铺
        ShopsPartner shopsPartner = remoteShopsService.getShopsPartnerMain();

        //获取主店铺特殊配置
        List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSettingByShopId(shopsPartner.getShopId());
        if(specialSettingList!=null&&specialSettingList.size()>0){
            SpecialSetting specialSetting = specialSettingList.get(0);
            commissionCashFlag = specialSetting.getCommissionCashFlag();
            //佣金提现是否自动审核
            if(specialSetting.getCommissionCashFlag() == CommonConstants.NUMBER_ONE){

                miniAccountCommissionCash.setStatus(CommissionCashEnum.APPROVED.getStatus());
                miniAccountCommissionCash.setApprovedTime(new Date());
                miniAccountCommissionCash.setQueryTimes(0);
                miniAccountCommissionCash.setPayStatus(PayStatusEnum.PROCESSING.getCode());
                int result2 = this.baseMapper.updateById(miniAccountCommissionCash);
                if(result2>0&&miniAccountCommissionCash.getPayType() == PayTypeEnum.WX_PAY.getType()){
                    //发送微信打款消息
                    String shopUserId = miniAccountCommissionCash.getUserId();
                    String tenantId = TenantContextHolder.getTenantId();
                    MiniInfoVo miniInfoVo = remoteMiniInfoService.getMiniInfoVoByTenantId(tenantId);
                    MiniAccount miniAccount = miniAccountService.getByShopUserId(shopUserId);
                    MiniAccountOauths miniAccountOauths = miniAccountOauthsService.getByUserId(OauthTypeEnum.WX_MINI.getType(),miniAccount.getUserId());

                    //获取店铺配置的小程序信息
                    ShopConfigDto shopConfig = remoteMiniInfoService.getShopConfig();

                    if (shopConfig == null) {
                        throw new ServiceException("商户配置不存在");
                    }

                    PayInfoVo payInfo = shopConfig.getPayInfo();

                    if(payInfo.getMchId()==null||payInfo.getMchId().equals("")){
                        throw new ServiceException("请先设置微信商户号！");
                    }
                    if(payInfo.getPrivateKeyFromPath()==null||payInfo.getPrivateKeyFromPath().equals("")){
                        throw new ServiceException("请先上传商户API证书私钥！");
                    }
                    if(payInfo.getPublicKeyFromPath()==null||payInfo.getPublicKeyFromPath().equals("")){
                        throw new ServiceException("请先上传微信支付公钥！");
                    }
                    if(payInfo.getPublicKeyId()==null||payInfo.getPublicKeyId().equals("")){
                        throw new ServiceException("请先设置微信支付公钥ID！");
                    }
                    if(payInfo.getMerchantSerialNumber()==null||payInfo.getMerchantSerialNumber().equals("")){
                        throw new ServiceException("请先设置商户API证书序列号！");
                    }
                    if(payInfo.getApiV3Key()==null||payInfo.getApiV3Key().equals("")){
                        throw new ServiceException("请先设置APIv3密钥！");
                    }
                    WxTransferV3Message wxTransferV3Message = new WxTransferV3Message();
                    wxTransferV3Message.setTenantId(tenantId);
                    wxTransferV3Message.setOutBillNo(miniAccountCommissionCash.getId()+"");
                    wxTransferV3Message.setTransferSceneId("1005");
                    wxTransferV3Message.setUserRecvPerception("劳务报酬");
                    wxTransferV3Message.setTransferAmount(miniAccountCommissionCash.getAmount().multiply(new BigDecimal(100)).intValue());
                    wxTransferV3Message.setOpenid(miniAccountOauths.getOpenId());
                    wxTransferV3Message.setTransferRemark("代销奖励");

                    wxTransferV3Message.setUserName("");
                    wxTransferV3Message.setAppid(miniInfoVo.getAppId());

                    List<WxTransferSceneV3Message>list = new ArrayList<>();

                    WxTransferSceneV3Message wxTransferSceneV3Message = new WxTransferSceneV3Message();
                    wxTransferSceneV3Message.setInfoType("岗位类型");
                    wxTransferSceneV3Message.setInfoContent("销售员");
                    list.add(wxTransferSceneV3Message);

                    WxTransferSceneV3Message wxTransferSceneV3Message2 = new WxTransferSceneV3Message();
                    wxTransferSceneV3Message2.setInfoType("报酬说明");
                    wxTransferSceneV3Message2.setInfoContent("代销奖励");
                    list.add(wxTransferSceneV3Message2);
                    wxTransferV3Message.setList(list);
                    sender.sendTransferV3Message(wxTransferV3Message);
                }
            }else{
                //发送佣金审核消息
                AccountCommissionCashMessage message = new AccountCommissionCashMessage();
                message.setId(miniAccountCommissionCash.getId());
                message.setShopId(shopsPartnerMain.getShopId());
                message.setTenantId(TenantContextHolder.getTenantId());
                sender.sendCommissionCashMessage(message);
            }
        }else{
            //发送佣金审核消息
            AccountCommissionCashMessage message = new AccountCommissionCashMessage();
            message.setId(miniAccountCommissionCash.getId());
            message.setShopId(shopsPartnerMain.getShopId());
            message.setTenantId(TenantContextHolder.getTenantId());
            sender.sendCommissionCashMessage(message);
        }

        resultData.put("commissionCashFlag",commissionCashFlag+"");
        resultData.put("id",miniAccountCommissionCash.getId()+"");
        return resultData;
    }

    @Override
    @Transactional
    public void approve(ApproveDataParam approveDataParam, HttpServletRequest request) {
        String id = approveDataParam.getId();
        MiniAccountCommissionCash miniAccountCommissionCash = this.baseMapper.selectById(id);
        if(miniAccountCommissionCash==null){
            throw new ServiceException("提现记录不存在！");
        }
        if(miniAccountCommissionCash.getStatus()!=CommissionCashEnum.IN_REVIEW.getStatus()){
            throw new ServiceException("提现记录已经审核，请勿重复审核！");
        }
        miniAccountCommissionCash.setStatus(Integer.valueOf(approveDataParam.getApprovalStatus()));
        miniAccountCommissionCash.setApprovedComments(approveDataParam.getApprovalReason());
        CurPcUserInfoDto pcUserInfoDto = CurUserUtil.getPcRqeustAccountInfo();
        miniAccountCommissionCash.setApprovedUserId(Long.valueOf(pcUserInfoDto.getUserId()));
        miniAccountCommissionCash.setApprovedUserName(pcUserInfoDto.getNikeName());
        miniAccountCommissionCash.setApprovedTime(new Date());
        //审核通过
        if(miniAccountCommissionCash.getStatus()==CommissionCashEnum.APPROVED.getStatus()){
            miniAccountCommissionCash.setQueryTimes(0);

            //线下支付，直接表示已放款
            if(miniAccountCommissionCash.getPayType() == PayTypeEnum.OFFLINE_PAY.getType()){
                miniAccountCommissionCash.setPayStatus(PayStatusEnum.SUCCESS.getCode());
                miniAccountCommissionCash.setStatus(CommissionCashEnum.SUCCESS.getStatus());
                miniAccountCommissionCash.setPayTime(LocalDateTime.now());
                this.baseMapper.updateById(miniAccountCommissionCash);
            }else{
                miniAccountCommissionCash.setPayStatus(PayStatusEnum.PROCESSING.getCode());
                int result = this.baseMapper.updateById(miniAccountCommissionCash);
                if(result>0){
                    //发送微信打款消息
                    String shopUserId = miniAccountCommissionCash.getUserId();
                    String tenantId = TenantContextHolder.getTenantId();
                    MiniInfoVo miniInfoVo = remoteMiniInfoService.getMiniInfoVoByTenantId(tenantId);
                    MiniAccount miniAccount = miniAccountService.getByShopUserId(shopUserId);
                    MiniAccountOauths miniAccountOauths = miniAccountOauthsService.getByUserId(OauthTypeEnum.WX_MINI.getType(),miniAccount.getUserId());

                    //获取店铺配置的小程序信息
                    ShopConfigDto shopConfig = remoteMiniInfoService.getShopConfig();

                    if (shopConfig == null) {
                        throw new ServiceException("商户配置不存在");
                    }

                    PayInfoVo payInfo = shopConfig.getPayInfo();

                    if(payInfo.getMchId()==null||payInfo.getMchId().equals("")){
                        throw new ServiceException("请先设置微信商户号！");
                    }
                    if(payInfo.getPrivateKeyFromPath()==null||payInfo.getPrivateKeyFromPath().equals("")){
                        throw new ServiceException("请先上传商户API证书私钥！");
                    }
                    if(payInfo.getPublicKeyFromPath()==null||payInfo.getPublicKeyFromPath().equals("")){
                        throw new ServiceException("请先上传微信支付公钥！");
                    }
                    if(payInfo.getPublicKeyId()==null||payInfo.getPublicKeyId().equals("")){
                        throw new ServiceException("请先设置微信支付公钥ID！");
                    }
                    if(payInfo.getMerchantSerialNumber()==null||payInfo.getMerchantSerialNumber().equals("")){
                        throw new ServiceException("请先设置商户API证书序列号！");
                    }
                    if(payInfo.getApiV3Key()==null||payInfo.getApiV3Key().equals("")){
                        throw new ServiceException("请先设置APIv3密钥！");
                    }
                    WxTransferV3Message wxTransferV3Message = new WxTransferV3Message();
                    wxTransferV3Message.setTenantId(tenantId);
                    wxTransferV3Message.setOutBillNo(miniAccountCommissionCash.getId()+"");
                    wxTransferV3Message.setTransferSceneId("1005");
                    wxTransferV3Message.setUserRecvPerception("劳务报酬");
                    wxTransferV3Message.setTransferAmount(miniAccountCommissionCash.getAmount().multiply(new BigDecimal(100)).intValue());
                    wxTransferV3Message.setOpenid(miniAccountOauths.getOpenId());
                    wxTransferV3Message.setTransferRemark("代销奖励");

                    wxTransferV3Message.setUserName("");
                    wxTransferV3Message.setAppid(miniInfoVo.getAppId());

                    List<WxTransferSceneV3Message>list = new ArrayList<>();

                    WxTransferSceneV3Message wxTransferSceneV3Message = new WxTransferSceneV3Message();
                    wxTransferSceneV3Message.setInfoType("岗位类型");
                    wxTransferSceneV3Message.setInfoContent("销售员");
                    list.add(wxTransferSceneV3Message);

                    WxTransferSceneV3Message wxTransferSceneV3Message2 = new WxTransferSceneV3Message();
                    wxTransferSceneV3Message2.setInfoType("报酬说明");
                    wxTransferSceneV3Message2.setInfoContent("代销奖励");
                    list.add(wxTransferSceneV3Message2);
                    wxTransferV3Message.setList(list);
                    sender.sendTransferV3Message(wxTransferV3Message);
                }
            }


        }else{//审核失败
            int result = this.baseMapper.updateById(miniAccountCommissionCash);
            if(result>0){
                String shopUserId = miniAccountCommissionCash.getUserId();
                MiniAccount miniAccount = miniAccountService.getByShopUserId(shopUserId);
                BigDecimal amount = miniAccountCommissionCash.getAmount();
                if(null == miniAccount.getCurrentCommission()){
                    miniAccount.setCurrentCommission(BigDecimal.ZERO);
                }
                miniAccount.setCurrentCommission(miniAccount.getCurrentCommission().add(amount));
                miniAccountService.updateById(miniAccount);
            }
        }

    }

    @Override
    @Transactional
    public void batchApprove(List<ApproveDataParam> list,HttpServletRequest request) {
        if(list!=null&&list.size()>0){
            for (ApproveDataParam approveDataParam : list) {
                approve(approveDataParam,request);
            }
        }else{
            throw new ServiceException("上传的list不能为空！");
        }
    }

    @Override
    public void payCommission(String id, HttpServletRequest request) {
        MiniAccountCommissionCash miniAccountCommissionCash = this.baseMapper.selectById(id);
        if(miniAccountCommissionCash==null){
            throw new ServiceException("提现记录不存在！");
        }
        if(!miniAccountCommissionCash.getPayStatus().equals(PayStatusEnum.FAILED.getCode())){
            throw new ServiceException("只有提现失败记录才能重新打款！");
        }
        miniAccountCommissionCash.setQueryTimes(0);
        miniAccountCommissionCash.setPayStatus(PayStatusEnum.PROCESSING.getCode());
        int result = this.baseMapper.updateById(miniAccountCommissionCash);
        if(result>0){
            //发送微信打款消息
            String shopUserId = miniAccountCommissionCash.getUserId();
            CashMessage cashMessage = new CashMessage();
            cashMessage.setShopUserId(shopUserId);
            cashMessage.setAmount(miniAccountCommissionCash.getCashAmount());
            cashMessage.setCashId(miniAccountCommissionCash.getId()+"");
            String tenantId = TenantContextHolder.getTenantId();
            cashMessage.setTenantId(tenantId);
            String ipUrl= IPUtils.getIpAddr(request);
            cashMessage.setIpUrl(ipUrl);
            sender.sendCashMessage(cashMessage);
        }
    }

    @Override
    public PageUtils<MiniAccountCommissionCashVo> getMiniAccountCommissionCashVo(MiniAccountCommissionCashParam miniAccountCommissionCashParam) {
        if(miniAccountCommissionCashParam.getType()!=null&&miniAccountCommissionCashParam.getType()==1){
            LocalDateTime localDateTime = LocalDateTime.now().minusDays(7);
            miniAccountCommissionCashParam.setTypeTime(localDateTime);
        }
        if(miniAccountCommissionCashParam.getType()!=null&&miniAccountCommissionCashParam.getType()==2){
            LocalDateTime localDateTime = LocalDateTime.now().minusDays(30);
            miniAccountCommissionCashParam.setTypeTime(localDateTime);
        }

        IPage<MiniAccountCommissionCashVo> miniAccountCommissionCashVo = this.baseMapper.getMiniAccountCommissionCashVo(new Page<>(miniAccountCommissionCashParam.getCurrent(), miniAccountCommissionCashParam.getSize()), miniAccountCommissionCashParam);
        return new PageUtils<>(miniAccountCommissionCashVo);
    }


    @Override
    public PageUtils<MiniAccountCommissionCashVo> getMiniAccountCommissionCashVoByShopUserId(MiniAccountCommissionCashParam miniAccountCommissionCashParam) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null) {
            throw new ServiceException("token错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        miniAccountCommissionCashParam.setShopUserId(curUser.getUserId());
        miniAccountCommissionCashParam.setCreateTimeSort(2);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if(StringUtils.isNotEmpty(miniAccountCommissionCashParam.getStartTimeStr())){
            LocalDateTime startTime = LocalDateTime.parse(miniAccountCommissionCashParam.getStartTimeStr()+" 00:00:00", formatter);
            miniAccountCommissionCashParam.setStartTime(startTime);
        }
        if(StringUtils.isNotEmpty(miniAccountCommissionCashParam.getEndTimeStr())){
            LocalDateTime endTime = LocalDateTime.parse(miniAccountCommissionCashParam.getEndTimeStr()+" 23:59:59", formatter);
            miniAccountCommissionCashParam.setEndTime(endTime);
        }
        IPage<MiniAccountCommissionCashVo> miniAccountCommissionCashVo = this.baseMapper.getMiniAccountCommissionCashVo(new Page<>(miniAccountCommissionCashParam.getCurrent(), miniAccountCommissionCashParam.getSize()), miniAccountCommissionCashParam);
        return new PageUtils<>(miniAccountCommissionCashVo);
    }

    @Override
    public void CashAmountToUser(CashMessage cashMessage) {
        String shopUserId = cashMessage.getShopUserId();
        TenantContextHolder.setTenantId(cashMessage.getTenantId());
        MiniAccount miniAccount = miniAccountService.getByShopUserId(shopUserId);
        MiniAccountOauths miniAccountOauths = miniAccountOauthsService.getByUserId(OauthTypeEnum.WX_MINI.getType(),miniAccount.getUserId());
        EntPayReQuestParam entPayReQuestParam = new EntPayReQuestParam();
        entPayReQuestParam.setAmount(cashMessage.getAmount().multiply(new BigDecimal(100)).intValue());
        entPayReQuestParam.setOpenid(miniAccountOauths.getOpenId());
        entPayReQuestParam.setSpbillCreateIp(cashMessage.getIpUrl());
        entPayReQuestParam.setCheckName(CheckNameEnum.NO_CHECK);
        entPayReQuestParam.setPayChannel(PayChannelEnum.WX);
        entPayReQuestParam.setDescription("佣金提现");
        entPayReQuestParam.setReUserName(miniAccount.getNikeName());
        entPayReQuestParam.setOrderId(cashMessage.getCashId());
        String md5 = ParamMd5SignUtils.md5(entPayReQuestParam);
        entPayReQuestParam.setMd5(md5);
        EntPay entPay = remotePaymentService.payRequest(entPayReQuestParam);
        if(entPay!=null&&entPay.getId()!=null){
            MiniAccountCommissionCash miniAccountCommissionCash = this.baseMapper.selectById(cashMessage.getCashId());
            miniAccountCommissionCash.setEntPayId(entPay.getId()+"");
            this.updateById(miniAccountCommissionCash);
        }

    }

    @Override
    @Transactional
    public void updatePayStatus() {
        LambdaQueryWrapper<MiniAccountCommissionCash>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountCommissionCash::getStatus,ApproveStatusEnum.APPROVED.getStatus());
        wrapper.lt(MiniAccountCommissionCash::getQueryTimes,3);
        wrapper.orderByAsc(MiniAccountCommissionCash::getCreateTime);
        wrapper.last("limit 10");
        List<MiniAccountCommissionCash> list = this.baseMapper.selectList(wrapper);
        if(list!=null&&list.size()>0){
            for (MiniAccountCommissionCash miniAccountCommissionCash : list) {
                String entPayId = miniAccountCommissionCash.getEntPayId();
                Long id = miniAccountCommissionCash.getId();
                Integer queryTimes = miniAccountCommissionCash.getQueryTimes();
                EntQueryPayDto entQueryPayDto = remotePaymentService.getPayStatus(entPayId, id, queryTimes);
                if(entQueryPayDto!=null){
                    miniAccountCommissionCash.setPayStatus(entQueryPayDto.getPayStatus());
                    miniAccountCommissionCash.setPayTime(entQueryPayDto.getPayTime());
                    miniAccountCommissionCash.setPayFailReason(entQueryPayDto.getPayFailReason());
                    miniAccountCommissionCash.setStatus(entQueryPayDto.getStatus());
                    miniAccountCommissionCash.setQueryTimes(entQueryPayDto.getQueryTimes());
                    this.updateById(miniAccountCommissionCash);
                    if(entQueryPayDto.getPayStatus()!=null&&entQueryPayDto.getPayStatus().equals(PayStatusEnum.FAILED.getCode()) ){
                        BigDecimal amount = miniAccountCommissionCash.getAmount();
                        String shopUserId = miniAccountCommissionCash.getUserId();
                        MiniAccount miniAccount = miniAccountService.getByShopUserId(shopUserId);
                        BigDecimal currentCommission = miniAccount.getCurrentCommission();
                        if(currentCommission==null){
                            currentCommission = BigDecimal.ZERO;
                        }
                        miniAccount.setCurrentCommission(currentCommission.add(amount));
                        miniAccountService.updateById(miniAccount);
                    }
                    if(entQueryPayDto.getPayStatus()!=null&&entQueryPayDto.getPayStatus().equals(PayStatusEnum.SUCCESS.getCode()) ){
                        BigDecimal amount = miniAccountCommissionCash.getAmount();
                        String shopUserId = miniAccountCommissionCash.getUserId();
                        MiniAccount miniAccount = miniAccountService.getByShopUserId(shopUserId);
                        BigDecimal usedCommission = miniAccount.getUsedCommission();
                        if(usedCommission==null){
                            usedCommission = BigDecimal.ZERO;
                        }
                        miniAccount.setUsedCommission(usedCommission.add(amount));
                        miniAccountService.updateById(miniAccount);
                    }
                }
            }
        }
    }
    /**
     * 导出提现记录
     * @param miniAccountCommissionCashParam
     * @return
     */
    @Override
    public void exportMiniAccountCommissionCashVo(MiniAccountCommissionCashParam miniAccountCommissionCashParam) {
        miniAccountCommissionCashParam.setCurrent(1);
        // 设置导出最大数量
        miniAccountCommissionCashParam.setSize(CommonConstants.MAX_EXPORT_SIZE);

        if(miniAccountCommissionCashParam.getType()!=null&&miniAccountCommissionCashParam.getType()==1){
            LocalDateTime localDateTime = LocalDateTime.now().minusDays(7);
            miniAccountCommissionCashParam.setTypeTime(localDateTime);
        }
        if(miniAccountCommissionCashParam.getType()!=null&&miniAccountCommissionCashParam.getType()==2){
            LocalDateTime localDateTime = LocalDateTime.now().minusDays(30);
            miniAccountCommissionCashParam.setTypeTime(localDateTime);
        }
        IPage<MiniAccountCommissionCashExcelVo> miniAccountCommissionCashVo = this.baseMapper.getMiniAccountCommissionCashExcelVo(new Page<>(miniAccountCommissionCashParam.getCurrent(), miniAccountCommissionCashParam.getSize()), miniAccountCommissionCashParam);

        PageUtils<MiniAccountCommissionCashExcelVo> pageUtils =   new PageUtils<>(miniAccountCommissionCashVo);

        List<MiniAccountCommissionCashExcelVo> excelVoList = pageUtils.getList();

        String fileName = DateUtil.format(new Date(), "yyyyMMdd HHmmss") + RandomUtil.randomString(4) + "提现列表";
        boolean success = HuToolExcelUtils.list2xlsx(excelVoList, fileName, MiniAccountCommissionCashExcelVo.class);
        if(!success){
            throw new ServiceException("导出数据异常", SystemCode.FAILURE.getCode());
        }
    }

    @Override
    @Transactional
    public void updateCommissionCash(UpdateCommissionMessage message) {
        String outBillNo = message.getOutBillNo();
        MiniAccountCommissionCash miniAccountCommissionCash = this.getById(outBillNo);
        if(miniAccountCommissionCash!=null&&!miniAccountCommissionCash.equals("")){
            miniAccountCommissionCash.setState(message.getState());

            if(StringUtil.isNotEmpty(message.getPackageInfo())){
                miniAccountCommissionCash.setPackageInfo(message.getPackageInfo());
            }
            if(StringUtil.isNotEmpty(message.getTransferBillNo())){
                miniAccountCommissionCash.setTransferBillNo(message.getTransferBillNo());
            }
            if(StringUtil.isNotEmpty(message.getFailResult())){
                miniAccountCommissionCash.setPayFailReason(message.getFailResult());
            }
            //转账成功
            if(miniAccountCommissionCash.getState().equals(TransferV3StateEnum.SUCCESS.getState())){
                BigDecimal amount = miniAccountCommissionCash.getAmount();
                String shopUserId = miniAccountCommissionCash.getUserId();
                MiniAccount miniAccount = miniAccountService.getByShopUserId(shopUserId);


                MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                dto.setUserId(shopUserId);
                dto.setAmount(amount);
                dto.setCommissionType(CommissionTypeEnum.CASH.getType());
                dto.setSource(CommonConstants.NUMBER_ZERO);
                dto.setRemark(CommissionTypeEnum.CASH.getDesc());
                miniAccountCommissionService.add(dto);


                BigDecimal usedCommission = miniAccount.getUsedCommission();
                if(usedCommission==null){
                    usedCommission = BigDecimal.ZERO;
                }
                miniAccount.setUsedCommission(usedCommission.add(amount));
                //转账成功
                miniAccountCommissionCash.setStatus(CommissionCashEnum.SUCCESS.getStatus());
                miniAccountService.updateById(miniAccount);
            }
            //转账失败
            if(miniAccountCommissionCash.getState().equals(TransferV3StateEnum.FAIL.getState()) ){
                BigDecimal amount = miniAccountCommissionCash.getAmount();
                String shopUserId = miniAccountCommissionCash.getUserId();
                MiniAccount miniAccount = miniAccountService.getByShopUserId(shopUserId);
                BigDecimal currentCommission = miniAccount.getCurrentCommission();
                if(currentCommission==null){
                    currentCommission = BigDecimal.ZERO;
                }
                //转账失败
                miniAccountCommissionCash.setStatus(CommissionCashEnum.FAIL.getStatus());
                miniAccount.setCurrentCommission(currentCommission.add(amount));
                miniAccountService.updateById(miniAccount);
            }
            this.updateById(miniAccountCommissionCash);
        }else{
            throw new ServiceException("提现记录不存在！");
        }


    }

    @Override
    public ReceiveMoneyVo receiveMoney(String id) {

        MiniAccountCommissionCash miniAccountCommissionCash = this.getById(id);


        if(miniAccountCommissionCash!=null&&!miniAccountCommissionCash.equals("")){
            MiniInfoVo miniInfoVo = remoteMiniInfoService.getMiniInfoVoByTenantId(miniAccountCommissionCash.getTenantId());


            ReceiveMoneyVo receiveMoneyVo = new ReceiveMoneyVo();
            receiveMoneyVo.setAppId(miniInfoVo.getAppId());

            //获取店铺配置的小程序信息
            ShopConfigDto shopConfig = remoteMiniInfoService.getShopConfig();

            if (shopConfig == null) {
                throw new ServiceException("商户配置不存在");
            }
            PayInfoVo payInfo = shopConfig.getPayInfo();
            receiveMoneyVo.setMchId(payInfo.getMchId());

            //判断确认收款状态是否存在
            Boolean result = remotePaymentService.vailReceiveMoneyState(id);

            if(StringUtil.isNotEmpty(miniAccountCommissionCash.getState())&&
                    miniAccountCommissionCash.getState().equals(TransferV3StateEnum.WAIT_USER_CONFIRM.getState())
                    &&result){
                receiveMoneyVo.setPackageInfo(miniAccountCommissionCash.getPackageInfo());
            }else{
                throw new ServiceException("提现单状态不是待收款用户确认");
            }


            return receiveMoneyVo;
        }else{
            throw new ServiceException("提现记录不存在！");
        }

    }

    @Override
    public void updateTransferState(String id) {

        MiniAccountCommissionCash miniAccountCommissionCash = this.getById(id);
        if(miniAccountCommissionCash!=null&&!miniAccountCommissionCash.equals("")){
            String state = remotePaymentService.updateTransferState(id);
            if(StringUtil.isNotEmpty(state)){
                miniAccountCommissionCash.setState(state);
                //转账成功
                if(miniAccountCommissionCash.getState().equals(TransferV3StateEnum.SUCCESS.getState())){
                    BigDecimal amount = miniAccountCommissionCash.getAmount();
                    String shopUserId = miniAccountCommissionCash.getUserId();
                    MiniAccount miniAccount = miniAccountService.getByShopUserId(shopUserId);
                    BigDecimal usedCommission = miniAccount.getUsedCommission();
                    if(usedCommission==null){
                        usedCommission = BigDecimal.ZERO;
                    }
                    miniAccount.setUsedCommission(usedCommission.add(amount));
                    //转账成功
                    miniAccountCommissionCash.setStatus(CommissionCashEnum.SUCCESS.getStatus());
                    miniAccountCommissionCash.setPayTime(LocalDateTime.now());
                    miniAccountService.updateById(miniAccount);
                }
                this.updateById(miniAccountCommissionCash);
            }else{
                throw new ServiceException("获取提现状态失败！");
            }

        }else{
            throw new ServiceException("提现记录不存在！");
        }

    }

    @Override
    public ApiCommissionCashVo getApiCommissionCash() {

        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (curUserDto == null) {
            throw new ServiceException("数据异常！");
        }
        String userId = curUserDto.getUserId();
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(userId);

        LambdaQueryWrapper<MemberLevelRelation>queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberLevelRelation::getUserId,miniAccountExtends.getUserId());
        List<MemberLevelRelation> memberLevelRelationList = memberLevelRelationService.list(queryWrapper);
        List<String>ids = new ArrayList<>();
        if(memberLevelRelationList!=null&&memberLevelRelationList.size()>0){
            for (MemberLevelRelation memberLevelRelation : memberLevelRelationList) {
                ids.add(memberLevelRelation.getMemberLevelId());
            }
        }else{
            throw new ServiceException("当前用户没有会员等级！");
        }

        LambdaQueryWrapper<MemberLevel>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberLevel::getMemberFlag,MemberFlagEnum.YES.getStatus());
        wrapper.in(MemberLevel::getId,ids);
        int count = memberLevelService.count(wrapper);
        if(count == 0){
            throw new ServiceException("您还不是会员，无法提现！");
        }

        ApiCommissionCashVo apiCommissionCashVo = new ApiCommissionCashVo();
        ShopsPartner shopsPartner = remoteShopsService.getShopsPartnerMain();
        List<SpecialSetting> list = remoteMiniInfoService.getSpecialSettingByShopId(shopsPartner.getShopId());
        if(list!=null&&list.size()>0){
            SpecialSetting specialSetting = list.get(0);
            apiCommissionCashVo.setWithdrawalMethod(specialSetting.getWithdrawalMethod());
            if(specialSetting.getWithdrawalMethod() == CommonConstants.NUMBER_ONE || specialSetting.getWithdrawalMethod() == CommonConstants.NUMBER_THREE){
                apiCommissionCashVo.setWxAccountUrl(miniAccountExtends.getWxAccountUrl());
                apiCommissionCashVo.setAlipayAccountUrl(miniAccountExtends.getAlipayAccountUrl());
                List<MiniAccountBankVo> bankList = miniAccountBankService.getMiniAccountBank(miniAccountExtends.getUserId());
                apiCommissionCashVo.setBankList(bankList);
            }
        }else{
            throw new ServiceException("特殊配置不存在！");
        }
        return apiCommissionCashVo;
    }


    /**
     * 后台手动增加提现记录
     * @param miniAccountManualCommissionCashDto
     */
    @Override
    @Transactional
    public void manualAddCash(MiniAccountManualCommissionCashDto miniAccountManualCommissionCashDto) {

        // 查询用户是否存在
        MiniAccount miniAccount = miniAccountService.getByShopUserId(miniAccountManualCommissionCashDto.getUserId());
        if (null == miniAccount) {
            throw new ServiceException("用户记录不存在，请刷新后重试！");
        }
        //用户可提现金额
        BigDecimal currentCommission = miniAccount.getCurrentCommission();
        //为空取0
        if(currentCommission==null){
            currentCommission = BigDecimal.ZERO;
        }
        //提现金额
        BigDecimal amount = miniAccountManualCommissionCashDto.getAmount();
        if(currentCommission.compareTo(amount) < 0){
            throw new ServiceException("提现金额不能超过用户可提现金额！");
        }
        MiniAccountCommissionCash miniAccountCommissionCash = new MiniAccountCommissionCash();
        miniAccountCommissionCash.setUserId(miniAccountManualCommissionCashDto.getUserId());
        miniAccountCommissionCash.setAmount(amount);


        // 获取佣金规则
        ShopCommissionRule rule = remoteShopsService.getCommissionRule();
        //提现率
        BigDecimal cashRate = rule.getCashRate();
        miniAccountCommissionCash.setCashRate(cashRate);
        //实际提现金额
        BigDecimal result = amount.multiply(cashRate.divide(new BigDecimal("100")));
        miniAccountCommissionCash.setCashAmount(amount.subtract(result));
        miniAccountCommissionCash.setStatus(CommissionCashEnum.APPROVED.getStatus());
        miniAccountCommissionCash.setApprovedTime(new Date());
        miniAccountCommissionCash.setApprovedUserId(Long.parseLong(CurUserUtil.getHttpCurUser().getUserId()));
        miniAccountCommissionCash.setApprovedUserName(CurUserUtil.getHttpCurUser().getNikeName());
        miniAccountCommissionCash.setSource(CommonConstants.NUMBER_ONE);
        miniAccountCommissionCash.setPlatformRemark(miniAccountManualCommissionCashDto.getPlatformRemark());
        miniAccountCommissionCash.setQueryTimes(0);
        miniAccountCommissionCash.setPayStatus(PayStatusEnum.SUCCESS.getCode());
        int num = this.baseMapper.insert(miniAccountCommissionCash);
        if(num == 1){
            miniAccount.setCurrentCommission(currentCommission.subtract(miniAccountCommissionCash.getAmount()));
            miniAccount.setUsedCommission((null == miniAccount.getUsedCommission() ? BigDecimal.ZERO : miniAccount.getUsedCommission()).add(amount));
            miniAccountService.updateById(miniAccount);
        }
    }
}
