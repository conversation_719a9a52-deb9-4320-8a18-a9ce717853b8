package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountCommissionCash;
import com.medusa.gruul.account.api.model.message.UpdateCommissionMessage;
import com.medusa.gruul.account.model.dto.MiniAccountCommissionCashDto;
import com.medusa.gruul.account.model.dto.MiniAccountManualCommissionCashDto;
import com.medusa.gruul.account.model.param.MiniAccountCommissionCashParam;
import com.medusa.gruul.account.model.vo.ApiCommissionCashVo;
import com.medusa.gruul.account.model.vo.MiniAccountCommissionCashExcelVo;
import com.medusa.gruul.account.model.vo.MiniAccountCommissionCashVo;
import com.medusa.gruul.account.model.vo.ReceiveMoneyVo;
import com.medusa.gruul.account.mq.CashMessage;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.dto.ApproveDataParam;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @Author: plh
 * @Description: 佣金提现 服务类
 * @Date: Created in 10:21 2023/8/31
 */
public interface IMiniAccountCommissionCashService extends IService<MiniAccountCommissionCash> {

    /**
     * 佣金申请提现
     * @param miniAccountCommissionCashDto
     * @return
     */
    Map<String,String> add(MiniAccountCommissionCashDto miniAccountCommissionCashDto);

    /**
     * 审批提现记录
     * @param approveDataParam
     */
    void approve(ApproveDataParam approveDataParam, HttpServletRequest request);

    /**
     * 批量审批提现记录
     * @param list
     */
    void batchApprove(List<ApproveDataParam>list,HttpServletRequest request);

    /**
     * 打款
     * @param id
     * @param request
     */
    void payCommission(String id, HttpServletRequest request);
    /**
     * 分页获取提现记录
     * @return
     */
    PageUtils<MiniAccountCommissionCashVo>getMiniAccountCommissionCashVo(MiniAccountCommissionCashParam miniAccountCommissionCashParam);


    /**
     * 获取当前用户提现记录
     * @param miniAccountCommissionCashParam
     * @return
     */
    PageUtils<MiniAccountCommissionCashVo>getMiniAccountCommissionCashVoByShopUserId(MiniAccountCommissionCashParam miniAccountCommissionCashParam);

    void CashAmountToUser(CashMessage cashMessage);

    /**
     * 更新支付状态
     */
    void updatePayStatus();

    /**
     * 导出提现记录
     * @return
     */
    void exportMiniAccountCommissionCashVo(MiniAccountCommissionCashParam miniAccountCommissionCashParam);


    /**
     * 更新用户佣金信息
     * @param message
     */
    void updateCommissionCash(UpdateCommissionMessage message);

    /**
     * 确认收款
     * @param id
     * @return
     */
    ReceiveMoneyVo receiveMoney(String id);

    /**
     * 更新转账单状态
     * @param id
     */
    void updateTransferState(String id);

    /**
     * 获取用户提现信息
     * @return
     */
    ApiCommissionCashVo getApiCommissionCash();


    /**
     * 后台手动增加提现记录
     * @param miniAccountManualCommissionCashDto
     */
    void manualAddCash(MiniAccountManualCommissionCashDto miniAccountManualCommissionCashDto);
}
