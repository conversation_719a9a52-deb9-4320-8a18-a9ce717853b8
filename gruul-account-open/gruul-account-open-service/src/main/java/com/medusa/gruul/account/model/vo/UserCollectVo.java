package com.medusa.gruul.account.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 * @data 2020/2/24 10:57
 */

@Data
@ApiModel(value = "UserCollectVo对象", description = "用户收藏展示信息")
public class UserCollectVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户收藏表id")
    private Long collectId;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "商品id")
    private Long productId;

    @ApiModelProperty(value = "商品主图")
    private String productPic;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品状态  0-上架 1-下架 2-售罄")
    private Integer status;

    @ApiModelProperty(value = "商品实际售价")
    private BigDecimal productPrice;

    @ApiModelProperty(value = "指导价划线价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "商品兑换积分")
    private BigDecimal integral;

    @ApiModelProperty(value = "商品类型，GOOD:普通商品，INTEGRAL:积分商品")
    private String type;


    @ApiModelProperty(value = "价格类型：1->会员价；2->复购价；3->实售价")
    private Integer priceType;


    @ApiModelProperty(value = "会员类型")
    private Long memberTypeId;
}
