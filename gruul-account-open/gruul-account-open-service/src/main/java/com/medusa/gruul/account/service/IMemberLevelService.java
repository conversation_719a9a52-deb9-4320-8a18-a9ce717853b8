package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.api.model.vo.ApiMemberLevelVo;
import com.medusa.gruul.account.model.dto.MemberLevelDto;
import com.medusa.gruul.account.model.param.MemberLevelParam;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.common.core.util.PageUtils;

import java.util.List;
import java.util.Map;

/**
 * 会员等级
 *
 * <AUTHOR>
 * @since 2022-2-20
 */
public interface IMemberLevelService extends IService<MemberLevel> {

    /**
     * 获取指定会员等级数据
     *
     * @param tagIdList 会员等级id数组
     * @return
     */
    List<MemberLevel> getByIdList(List<String> tagIdList);

    /**
     * 停用或者启用会员等级
     *
     * @param memberLevel
     * @return java.lang.String
     */
    Map<String, Object> disable(MemberLevel memberLevel);

    /**
     * 添加或者修改会员卡信息
     *
     * @param memberLevel
     *
     */
    void addOrUpdate(List<MemberLevelVo> memberLevel);

    /**
     * 删除会员卡
     * @param memberLevel
     */
    void delete(MemberLevel memberLevel);

    /**
     * 修改等级
     *
     * @param miniAccountMember
     *
     */
    void updateLevel(MiniAccountMemberInformation miniAccountMember);
    /**
     * 续费
     *
     * @param memberLevel
     *
     */
    void renew(MemberLevel memberLevel);
    /**
     * 停用会员卡
     *
     * @param memberId
     *
     */
    void deactivate(String memberId);
    /**
     * 启用会员卡
     *
     * @param memberId
     *
     */
    void enable(String memberId);
    /**
     * 获取指定会员的等级
     *
     * @param memberLeveIdList 用户id数组
     *@return com.medusa.gruul.account.api.entity.MemberLevel
     *
     */
    List<MemberLevel> getByMemberLeveIdList(List<String> memberLeveIdList);

    /**
     * 查询会员等级信息
     *@return com.medusa.gruul.account.model.vo.MemberLevelVo
     *
     */
    List<MemberLevelVo> selectList(MemberLevelParam param);

    /**
     * 查询全部会员等级信息
     *@return com.medusa.gruul.account.model.vo.MemberLevelVo
     *
     */
    List<MemberLevelVo> selectAllList();

    /**
     *  查询全部会员等级信息-根据会员类型分组
     * @return
     */
    List<MemberLevelGroupByMemberTypeVo> selectAllListGroupByMemberTypeId();

    /**
     * 根据会员类型id获取启用会员等级
     * @param memberTypeId
     * @return
     */
    List<MemberLevelVo> getMemberLevelByMemberTypeId(Long memberTypeId);

    /**
     * 查询会员权益信息
     * @param userIdList
     * @return com.medusa.gruul.account.api.entity.MiniAccountMemberLevelRightsRelation
     *
     */
    List<MemberLevelRightsRelation> selectMemberPower(List<String> userIdList);

    /**
     * 查询权益信息
     *@return com.medusa.gruul.account.api.entity.MemberLevelRights
     *
     */
    List<MemberLevelRights> selectRights();

    /**
     * 查询会员等级价格信息
     *@param productId
     *@return com.medusa.gruul.account.model.vo.MemberLevePriceVo
     */
    List<MemberLevePriceVo> selectPrice(Long productId);

    /**
     * 设置默认会员等级
     * @param id
     */
    void defaultLevel(Long id);


    /**
     * 外部系统添加或者修改会员卡信息
     *
     * @param memberLevelDto
     *
     */
    MemberLevel outSave(MemberLevelDto memberLevelDto);

    /**
     * 外部系统获取会员卡列表
     * @param page
     * @param size
     * @return
     */
    PageUtils<List<MemberLevelVo>> externalMemberList(Integer page, Integer size);

    /**
     * 批量修改会员卡发送状态
     *
     * @param accountIds the order ids
     * @param    sendStatus
     * @return void
     * <AUTHOR>
     * @date 2022 /05/24 09:12
     */
    void updateSendStatus(List<String> accountIds,String sendStatus);

    /**
     * 获取小程序会员等级
     * @return
     */
    MemberLevel getMemberLevel(Long memberTypeId);

    /**
     * 获取小程序我的会员等级
     * @return
     */
    ApiMemberLevelVo getApiMemberLevel(Long memberTypeId);

    /**
     * 根据userId获取小程序我的会员等级
     * @param userId
     * @return
     */
    ApiMemberLevelVo getApiMemberLevelByUserId(String userId);
}
