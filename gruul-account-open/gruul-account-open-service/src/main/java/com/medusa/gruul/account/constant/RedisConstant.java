package com.medusa.gruul.account.constant;

/**
 * <AUTHOR>
 * @description
 * @data: 2019/11/26
 */
public class RedisConstant {

    /**
     * 登录key值
     */
    public static final String LOGIN_KEY = "mini:login:";

    /**
     * 小程序用户模块总key account
     */
    public static final String ACCOUNT_KEY = "account:";

    /**
     * 用户模块总key userId
     */
    public static final String ACCOUNT_DB_KEY = "userId:";

    /**
     * 用户登录时间
     */
    public static final String ACCOUNT_LOGIN_TIME = "userId:Login:time:";

    /**
     * 用户地址key
     */
    public static final String ADDRESS_KEY = "address:";


    /**
     * 用戶收藏key
     */
    public static final String COLLECT_KEY = "collect:";

    /**
     * 用戶通行票key
     */
    public static final String PASS_TICKET_KEY = "passTicket:";

    /**
     * 用戶优惠券key
     */
    public static final String COUPON_KEY = "coupon:";

    /**
     * 用戶权益包商品key
     */
    public static final String PACKAGE_GOODS_KEY = "packageGoods:";

    /**
     * APP用户模块总key app
     */
    public static final String APP_KEY = "app:";

    /**
     * APP用户模块token key
     */
    public static final String APP_TOKEN_KEY = "tk:";

    /**
     * 小程序（APP）用户模块手机号校验
     */
    public static final String PHONE_KEY = "phone:";

    /**
     * 小程序（APP）用户模块手机号校验成功有效期凭证key
     */
    public static final String PHONE_CERTIFICATE_KEY = "phone:certificate:";

}
