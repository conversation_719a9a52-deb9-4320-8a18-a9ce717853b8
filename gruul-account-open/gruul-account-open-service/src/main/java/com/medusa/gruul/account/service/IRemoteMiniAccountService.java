package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MemberLevelRelation;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.model.message.AccountReturnBalanceMessage;
import com.medusa.gruul.account.api.model.message.UpgradeMemberLevelMessage;
import com.medusa.gruul.account.model.vo.MemberLevelRuleVo;
import com.medusa.gruul.account.model.vo.MiniAccountRewardVo;
import com.medusa.gruul.goods.api.model.vo.manager.RewardSchemeDetVo;
import com.medusa.gruul.order.api.model.OrderItemVo;
import com.medusa.gruul.order.api.model.OrderVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: plh
 * @Description: 服务间调用service
 * @Date: Created in 21:14 2025/6/18
 */
public interface IRemoteMiniAccountService extends IService<MiniAccount> {

    /**
     * 处理完成订单
     * @param message
     */
    void handCompletedOrder(UpgradeMemberLevelMessage message);

    /**
     * 处理订单奖励方案内容
     * @param message
     */
    void handleReward(UpgradeMemberLevelMessage message);

    /**
     * 处理订单积分内容
     * @param message
     */
    void handleIntegral(UpgradeMemberLevelMessage message);
    /**
     * 处理会员升级消息
     * @param message
     */
    void upgradeMemberLevel(UpgradeMemberLevelMessage message);

    /**
     * 归还用户余额
     * @param message
     */
    void revertAccountBalance(AccountReturnBalanceMessage message);

    /**
     * 验证会员等级升级前置升级条件
     * @param upgradeMemberLevelRule
     * @param miniAccount
     * @return
     */
    Boolean checkUpgradePreLow(MemberLevelRuleVo upgradeMemberLevelRule, MiniAccount miniAccount);

    /**
     * 获取符合条件奖励方案规则
     * @param orderVo
     * @param orderItemVo
     * @param commissionAmountSource
     * @return
     */
    List<RewardSchemeDetVo>getRewardSchemeDetVo(OrderVo orderVo, OrderItemVo orderItemVo, Integer commissionAmountSource,
                                                Long orderMemberTypeId,Long regionMemberTypeId,Integer regionFlag);

    /**
     * 处理佣金奖励方式
     */
    void handleCommission(List<RewardSchemeDetVo> rewardSchemeDetList,String shopUserId,
                          Long orderId,BigDecimal money,Long orderMemberTypeId,
                          List<MiniAccountRewardVo> miniAccountRewardList);

    /**
     * 处理平级
     */
    void handleSameLevel(List<RewardSchemeDetVo> rewardSchemeDetList,String shopUserId,Long orderId,
                         BigDecimal money,Long orderMemberTypeId,
                         List<MiniAccountRewardVo> miniAccountRewardList);

    /**
     * 处理循环分佣
     * @param rewardSchemeDetList
     * @param orderVo
     * @param orderItemVo
     * @param miniAccountRewardList
     */
    void handleCycleCommission(List<RewardSchemeDetVo> rewardSchemeDetList,OrderVo orderVo,OrderItemVo orderItemVo,
                               List<MiniAccountRewardVo> miniAccountRewardList);

    /**
     * 处理级差
     */
    void handleDifferCommission(List<RewardSchemeDetVo> rewardSchemeDetList, String shopUserId,Long orderId,
                                BigDecimal money,Long orderMemberTypeId,Long skuId,Long productId,
                                Integer buyType,Integer productQuantity,
                                List<MiniAccountRewardVo> miniAccountRewardList);

    /**
     * 处理团队分佣
     */
    void handleTeamCommission(List<RewardSchemeDetVo> rewardSchemeDetList, String shopUserId,Long orderId,
                              BigDecimal money,Long orderMemberTypeId,
                              List<MiniAccountRewardVo> miniAccountRewardList);

    /**
     * 处理区域会员佣金
     */
    void handleRegionCommission(List<RewardSchemeDetVo> rewardSchemeDetList,BigDecimal money,String userId,
                                Long orderId,List<MiniAccountRewardVo> miniAccountRewardList);

    /**
     * 处理区域会员平级
     */
    void handleRegionSameLevel(List<RewardSchemeDetVo> rewardSchemeDetList,String userId,
                               String orderUserId,Long memberTypeId,BigDecimal money,Long orderId,
                               List<MiniAccountRewardVo> miniAccountRewardList);

    /**
     * 处理区域会员循环分佣
     */
    void handleRegionCycleCommission(List<RewardSchemeDetVo> rewardSchemeDetList, MemberLevelRelation memberLevelRelation,
                                     BigDecimal money, Long orderId, List<MiniAccountRewardVo> miniAccountRewardList);

    /**
     * 处理区域会员级差
     */
    void handleRegionDifferCommission(List<RewardSchemeDetVo> rewardSchemeDetList,
                                      List<RewardSchemeDetVo> allRewardSchemeDetList,
                                      MemberLevelRelation memberLevelRelation,
                                      OrderVo orderVo,
                                      BigDecimal money,
                                      Long orderId,
                                      List<MemberLevelRelation> memberLevelRelationList,
                                      List<MiniAccountRewardVo> miniAccountRewardList);

}
