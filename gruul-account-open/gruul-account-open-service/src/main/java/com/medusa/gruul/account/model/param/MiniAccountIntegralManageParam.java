package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 19:26 2025/7/8
 */
@Data
@ApiModel(value = "MiniAccountIntegralManageParam 实体", description = "后台查询积分明细param")
public class MiniAccountIntegralManageParam extends QueryParam {

    @ApiModelProperty(value = "用户名称")
    private String nikeName;

    @ApiModelProperty(value = "用户电话")
    private String phone;

    @ApiModelProperty(value = "会员等级id")
    private String memberLevelId;

    @ApiModelProperty(value = "变动类型:0.自动；1.手动")
    private Integer source;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

}
