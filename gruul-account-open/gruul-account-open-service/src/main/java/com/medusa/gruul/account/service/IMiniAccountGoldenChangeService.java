package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountGoldenChange;
import com.medusa.gruul.account.model.dto.MiniAccountGoldenChangeAuditDto;
import com.medusa.gruul.account.model.dto.MiniAccountGoldenChangeDeleteDto;
import com.medusa.gruul.account.model.dto.MiniAccountGoldenChangeDto;
import com.medusa.gruul.account.model.param.MiniAccountGoldenChangeParam;
import com.medusa.gruul.account.model.vo.MiniAccountGoldenChangeVo;
import com.medusa.gruul.account.model.vo.MiniAccountGoldenVo;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:37 2025/7/21
 */
public interface IMiniAccountGoldenChangeService extends IService<MiniAccountGoldenChange> {

    /**
     * 增加金豆变更记录
     * @param dto
     */
    void addMiniAccountGoldenChange(MiniAccountGoldenChangeDto dto);

    /**
     * 修改金豆变更记录
     * @param dto
     */
    void editMiniAccountGoldenChange(MiniAccountGoldenChangeDto dto);

    /**
     * 删除金豆变更记录
     * @param dto
     */
    void deleteMiniAccountGoldenChange(MiniAccountGoldenChangeDeleteDto dto);

    /**
     * 审核金豆变更记录
     * @param dto
     */
    void auditMiniAccountGoldenChange(MiniAccountGoldenChangeAuditDto dto);

    /**
     * 分页查询金豆变更记录
     * @param param
     * @return
     */
    IPage<MiniAccountGoldenChangeVo>queryList(MiniAccountGoldenChangeParam param);

    /**
     * 根据id查询金豆变更记录
     * @param param
     * @return
     */
    MiniAccountGoldenChangeVo getMiniAccountGoldenChangeVo(MiniAccountGoldenChangeParam param);

}
