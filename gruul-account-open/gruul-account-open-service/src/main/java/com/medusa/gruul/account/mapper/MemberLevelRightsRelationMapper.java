package com.medusa.gruul.account.mapper;

import java.util.List;

import com.medusa.gruul.account.api.entity.MemberLevelRightsRelation;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 会员等级、权益关联表
 * @Author: jeecg-boot
 * @Date:   2022-02-23
 * @Version: V1.0
 */
public interface MemberLevelRightsRelationMapper extends BaseMapper<MemberLevelRightsRelation> {
    /**
     * 查询会员权益信息
     *@param memberLevelIdList 会员等级Id
     *@return 会员权益信息
     *
     */
    List<MemberLevelRightsRelation> selectMemberPowerList(@Param("memberLevelIdList") List<String> memberLevelIdList);
    /**
     * 删除会员权益信息
     *@param memberLevelId 会员等级Id
     *
     */
    void deleteMemberRights(String memberLevelId);
}
