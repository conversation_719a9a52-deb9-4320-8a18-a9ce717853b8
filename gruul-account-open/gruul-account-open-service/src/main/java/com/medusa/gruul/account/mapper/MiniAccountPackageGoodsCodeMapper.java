package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccountPackageGoodsCode;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageGoodsCodeParam;
import com.medusa.gruul.account.api.model.param.PackageGoodsCodeDetailParam;
import com.medusa.gruul.account.api.model.vo.PackageGoodsCodeDetailVo;
import com.medusa.gruul.account.api.model.vo.ShopPackageGoodsCodeVo;
import com.medusa.gruul.account.api.model.ManageVerifyPackageGoodsStaticDto;
import com.medusa.gruul.account.model.param.ApiPackageGoodsCodeParam;
import com.medusa.gruul.account.model.vo.ApiPackageGoodsCodeVo;
import com.medusa.gruul.account.api.model.vo.ManageVerifyPackageGoodsStaticVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:11 2024/9/10
 */
@Repository
public interface MiniAccountPackageGoodsCodeMapper extends BaseMapper<MiniAccountPackageGoodsCode> {
    /**
     * 商家用户查询自己核销的权益包商品记录
     * @param page
     * @param miniAccountPackageGoodsCodeParam
     * @return
     */
    IPage<ShopPackageGoodsCodeVo> selectShopVerifyList(Page<ShopPackageGoodsCodeVo> page,
                                                       @Param("param")MiniAccountPackageGoodsCodeParam miniAccountPackageGoodsCodeParam);

    /**
     * 分页查询核销明细
     * @param page
     * @param param
     * @return
     */
    IPage<PackageGoodsCodeDetailVo> pagePackageGoodsCodeDetail(Page<PackageGoodsCodeDetailVo> page,
                                                               @Param("param")PackageGoodsCodeDetailParam param);

    /**
     * 分页查询小程序用户核销记录
     * @param page
     * @param param
     * @return
     */
    IPage<ApiPackageGoodsCodeVo>pageApiPackageGoodsCode(Page<ApiPackageGoodsCodeVo> page,
                                                        @Param("param") ApiPackageGoodsCodeParam param);

    /**
     * 门店、员工核销权益包数量汇总
     * @param manageVerifyPackageGoodsStaticDto
     * @return
     */
    List<ManageVerifyPackageGoodsStaticVo> manageVerifyPackageGoodsStatic(@Param("params") ManageVerifyPackageGoodsStaticDto manageVerifyPackageGoodsStaticDto);

    /**
     * 门店、员工核销权益包数量汇总合计
     * @param manageVerifyPackageGoodsStaticDto
     * @return
     */
    ManageVerifyPackageGoodsStaticVo manageVerifyPackageGoodsStaticTotal(@Param("params") ManageVerifyPackageGoodsStaticDto manageVerifyPackageGoodsStaticDto);
}
