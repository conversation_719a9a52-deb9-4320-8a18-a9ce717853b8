package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:32 2025/6/19
 */
@Data
@ApiModel(value = "会员升级消费额记录查询param")
public class MiniAccountMemberUpParam  extends QueryParam {

    @ApiModelProperty(value = "会员类型id")
    private Long memberTypeId;

    @ApiModelProperty(value = "小程序用户id")
    private String userId;

}
