package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccountCommissionCash;
import com.medusa.gruul.account.model.param.MiniAccountCommissionCashParam;
import com.medusa.gruul.account.model.vo.MiniAccountCommissionCashExcelVo;
import com.medusa.gruul.account.model.vo.MiniAccountCommissionCashVo;
import com.medusa.gruul.common.core.util.PageUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @Author: plh
 * @Description: 佣金提现 mapper类
 * @Date: Created in 10:15 2023/8/31
 */
@Repository
public interface MiniAccountCommissionCashMapper extends BaseMapper<MiniAccountCommissionCash> {

    /**
     * 获取提现记录数据
     * @param page
     * @param miniAccountCommissionCashParam
     * @return
     */
    IPage<MiniAccountCommissionCashVo>getMiniAccountCommissionCashVo(Page<MiniAccountCommissionCashVo> page,@Param("paramMap")MiniAccountCommissionCashParam miniAccountCommissionCashParam);

    /**
     * 获取导出excel提现记录
     * @param page
     * @param miniAccountCommissionCashParam
     * @return
     */
    IPage<MiniAccountCommissionCashExcelVo> getMiniAccountCommissionCashExcelVo(Page<MiniAccountCommissionCashVo> page,@Param("paramMap")MiniAccountCommissionCashParam miniAccountCommissionCashParam);
}
