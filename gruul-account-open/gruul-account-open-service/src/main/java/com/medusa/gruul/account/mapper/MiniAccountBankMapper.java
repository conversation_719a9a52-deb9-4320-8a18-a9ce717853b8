package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccountBank;
import com.medusa.gruul.account.model.param.MiniAccountBankParam;
import com.medusa.gruul.account.model.vo.MiniAccountBankVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:18 2025/5/27
 */
@Mapper
public interface MiniAccountBankMapper extends BaseMapper<MiniAccountBank> {

    /**
     * 分页查询用户银行卡
     * @param page
     * @param param
     * @return
     */
    IPage<MiniAccountBankVo> queryList(Page<MiniAccountBankVo> page, @Param("param")MiniAccountBankParam param);

    /**
     * 根据用户id查询用户银行
     * @param userId
     * @return
     */
    List<MiniAccountBankVo> getMiniAccountBank(@Param("userId")String userId);
}
