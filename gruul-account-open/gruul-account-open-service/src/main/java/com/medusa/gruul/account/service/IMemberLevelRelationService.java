package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MemberLevelRelation;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.model.dto.ApiApplyRegionMemberDto;
import com.medusa.gruul.account.model.param.MemberLevelRelationParam;
import com.medusa.gruul.account.model.vo.ApiApplyRegionMemberVo;
import com.medusa.gruul.account.model.vo.RegionMemberLevelVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:17 2025/5/22
 */
public interface IMemberLevelRelationService extends IService<MemberLevelRelation> {

    /**
     * 根据用户id获取会员等级关系
     *
     * @param userId
     * @return
     */
    List<MemberLevelRelation> getMemberLevelsByUserId(String userId);

    /**
     * 查询会员等级，类型记录
     * @return
     */
    List<MemberLevelRelation> getMemberLevelRelation(MemberLevelRelationParam param);

    /**
     * 获取区域会员申请信息
     * @return
     */
    ApiApplyRegionMemberVo getApplyRegionMember();


    /**
     * 查询会员类型下已经被代理的区域
     * @param memberTypeId
     * @return
     */
    List<String> getAgentRegionCodeList(Long memberTypeId);

    /**
     * 区域会员申请
     * @param dto
     */
    void applyRegionMember(ApiApplyRegionMemberDto dto);

    /**
     * 判断是否为区域会员
     * @return
     */
    Boolean getRegionMemberFlag();

    /**
     * 获取当前用户区域会员等级信息
     * @return
     */
    RegionMemberLevelVo getRegionMemberLevel();

    /**
     * 查询当前用户会员等级-过滤memberLevelId为空数据
     * @param memberLevelRelationParam
     * @return
     */
    List<MemberLevelRelation> getMemberLevelRelationNotMemberLevelId(MemberLevelRelationParam memberLevelRelationParam);

    /**
     * 查询当前用户会员等级id-过滤memberLevelId为空数据
     * @param memberLevelRelationParam
     * @return
     */
    String getMemberLevelIdNotMemberLevelId(MemberLevelRelationParam memberLevelRelationParam);

    /**
     * 通过区域代码获取区域类型会员
     * @param countyCode
     * @param id
     * @return
     */
    MemberLevelRelation getMemberLevelRelationByCode(String countyCode, String id);
}
