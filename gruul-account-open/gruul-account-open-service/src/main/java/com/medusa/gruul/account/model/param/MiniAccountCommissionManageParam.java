package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:08 2025/7/8
 */
@Data
@ApiModel(value = "MiniAccountCommissionManageParam 实体", description = "后台查询佣金明细param")
public class MiniAccountCommissionManageParam extends QueryParam {

    @ApiModelProperty(value = "用户名称")
    private String nikeName;

    @ApiModelProperty(value = "用户电话")
    private String phone;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "佣金类型:101->团队分佣;200->佣金提现")
    private Integer commissionType;

    @ApiModelProperty(value = "分佣标题")
    private String remark;


    @ApiModelProperty(value = "变更方式:1->增加;2->减少")
    private Integer way;

}
