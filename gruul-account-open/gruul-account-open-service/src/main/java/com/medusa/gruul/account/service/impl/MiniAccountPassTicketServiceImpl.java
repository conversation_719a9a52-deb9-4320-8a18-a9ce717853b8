package com.medusa.gruul.account.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccountPassTicket;
import com.medusa.gruul.account.api.model.vo.MiniAccountPassTicketVo;
import com.medusa.gruul.account.conf.AccountRedis;
import com.medusa.gruul.account.constant.RedisConstant;
import com.medusa.gruul.account.constant.RedisKey;
import com.medusa.gruul.account.model.param.MiniAccountPassTicketParam;
import com.medusa.gruul.account.model.param.MiniShopsPartnerParam;
import com.medusa.gruul.account.model.vo.MiniShopsPartnerVo;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.DateUtils;
import com.medusa.gruul.common.core.util.DistanceUtil;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.order.api.model.OrderItemVo;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.enums.ExpiredTypeEnum;
import com.medusa.gruul.shops.api.enums.PromotionStatusEnum;
import com.medusa.gruul.account.mapper.MiniAccountPassTicketMapper;
import com.medusa.gruul.account.model.dto.MiniAccountPassTicketDto;
import com.medusa.gruul.account.service.IMiniAccountPassTicketService;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import com.medusa.gruul.shops.api.enums.TradeStatusEnum;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员-通行票服务类
 */
@Service(value = "miniAccountPassTicketServiceImpl")
@Slf4j
public class MiniAccountPassTicketServiceImpl extends ServiceImpl<MiniAccountPassTicketMapper, MiniAccountPassTicket> implements IMiniAccountPassTicketService {

    @Autowired
    private RemoteShopsService remoteShopsService;

    /**
     * 查询用户多少张未用通行票
     * @return
     */
    @Override
    public Integer myUnusedCount() {
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        LambdaQueryWrapper<MiniAccountPassTicket> wrapper = new LambdaQueryWrapper<>();
        List<Integer>statusList = new ArrayList<>();
        statusList.add(PromotionStatusEnum.UN_USE.getStatus());
        statusList.add(PromotionStatusEnum.USED.getStatus());
        wrapper.select(MiniAccountPassTicket::getId).eq(MiniAccountPassTicket::getUserId, userId)
                .in(MiniAccountPassTicket::getStatus, statusList)
                .ge(MiniAccountPassTicket::getEndTime, new Date());
        Integer num = this.baseMapper.selectCount(wrapper);
        return num;
    }

    /**
     * 购买通行票增加会员通行票记录
     * @param miniAccountPassTicketDto
     * @return
     */
    @Override
    public MiniAccountPassTicket add(MiniAccountPassTicketDto miniAccountPassTicketDto) {
        MiniAccountPassTicket ticket = new MiniAccountPassTicket();
        BeanUtil.copyProperties(miniAccountPassTicketDto, ticket);
        ticket.setStatus(PromotionStatusEnum.UN_USE.getStatus());
        // 查询通行票的开始时间和结束时间
        ShopPassTicket shopPassTicket = remoteShopsService.getPassTicketById(ticket.getPassTicketId());
        if(null == shopPassTicket){
            throw new ServiceException("通行票记录不存在", SystemCode.DATA_NOT_EXIST_CODE);
        }
        ticket.setStartTime(shopPassTicket.getStartTime());
        ticket.setEndTime(shopPassTicket.getEndTime());
        if(shopPassTicket.getExpiredType().intValue() == ExpiredTypeEnum.BUY_DATE.getType()){
            Date nowDate = new Date();
            //开始时间按照购买多少天之后生效
            Integer afterDaysValid = shopPassTicket.getAfterDaysValid();
            if(null == afterDaysValid || afterDaysValid < 0){
                afterDaysValid = 0;
            }
            // 2022-09-20 23:59:59 会变成 变成了 2022-09-21 00:00:00，将生成的时间往前偏移999毫秒即可。
            Date startTime = DateUtil.offsetDay(nowDate, afterDaysValid).offset(DateField.MILLISECOND, -999);
            ticket.setStartTime(startTime);
            //结束时间按照购买之日算起
            Integer expiredDays = shopPassTicket.getExpiredDays();
            if(null == expiredDays){
                expiredDays = 0;
            }

            Date endDate = DateUtil.endOfDay(nowDate);
            // 2022-09-20 23:59:59 会变成 变成了 2022-09-21 00:00:00，将生成的时间往前偏移999毫秒即可。
            Date endTime = DateUtil.offsetDay(endDate, expiredDays).offset(DateField.MILLISECOND, -999);
            ticket.setEndTime(endTime);
        }
        boolean success = this.save(ticket);
        return success ? ticket : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMiniAccountPassTicketByMq(OrderVo orderVo) {
        TenantContextHolder.setTenantId(orderVo.getTenantId());
        log.info("开始添加队列发送通惠证信息");
        String userId = orderVo.getUserId();
        List<OrderItemVo> orderItemList = orderVo.getOrderItemList();
        if(orderItemList!=null&&orderItemList.size()>0){
            for (OrderItemVo orderItemVo : orderItemList) {
                MiniAccountPassTicketDto dto = new MiniAccountPassTicketDto();
                dto.setUserId(userId);
                dto.setOrderId(orderVo.getId());
                dto.setPassTicketId(orderItemVo.getShopTicketId()== null ? orderItemVo.getProductId() : Long.valueOf(orderItemVo.getShopTicketId()));
                add(dto);
            }
        }
    }

    /**
     * 查询用户个人的通行票记录
     * @param param
     * @return
     */
    @Override
    public IPage<MiniAccountPassTicketVo> pageMyTicket(MiniAccountPassTicketParam param) {
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        LambdaQueryWrapper<MiniAccountPassTicket> wrapper = new LambdaQueryWrapper<>();
        if(param.getStatus() != null && (param.getStatus().intValue() == PromotionStatusEnum.UN_USE.getStatus()
                || param.getStatus().intValue() == PromotionStatusEnum.USED.getStatus() )){
            wrapper.eq(MiniAccountPassTicket::getUserId, userId).eq(MiniAccountPassTicket::getStatus, param.getStatus())
                    .ge(MiniAccountPassTicket::getEndTime, new Date())
                    .orderByAsc(MiniAccountPassTicket::getEndTime);
        }else if(param.getStatus() != null && param.getStatus().intValue() == PromotionStatusEnum.EXPIRED.getStatus()){
            // 失效的通惠证
            wrapper.eq(MiniAccountPassTicket::getUserId, userId).eq(MiniAccountPassTicket::getStatus, PromotionStatusEnum.UN_USE.getStatus())
                    .lt(MiniAccountPassTicket::getEndTime, new Date())
                    .orderByAsc(MiniAccountPassTicket::getEndTime);
        }
        IPage<MiniAccountPassTicket> pageAccountPassTicket = this.page(new Page(param.getCurrent(),param.getSize()), wrapper);
        IPage<MiniAccountPassTicketVo> pageShopPassTicketVo = new Page<>();
        pageShopPassTicketVo.setCurrent(pageAccountPassTicket.getCurrent());
        pageShopPassTicketVo.setSize(pageAccountPassTicket.getSize());
        pageShopPassTicketVo.setPages(pageAccountPassTicket.getPages());
        pageShopPassTicketVo.setTotal(pageAccountPassTicket.getTotal());
        List<Long> ticketIds = pageAccountPassTicket.getRecords().stream().map(MiniAccountPassTicket::getPassTicketId).collect(Collectors.toList());
        if(CollectionUtil.isEmpty(ticketIds)){
            return pageShopPassTicketVo;
        }
        //Map<Long, MiniAccountPassTicket> accountPassTicketMap = pageAccountPassTicket.getRecords().stream().collect(Collectors.toMap(MiniAccountPassTicket::getPassTicketId, e -> e, (key1, key2) -> key2));
        List<ShopPassTicket> shopPassTicketList = this.remoteShopsService.getPassTicketByIds(ticketIds);
        Map<Long, ShopPassTicket> passTicketMap = shopPassTicketList.stream().collect(Collectors.toMap(ShopPassTicket::getId, e -> e, (key1, key2) -> key2));
        List<MiniAccountPassTicketVo> voList = new ArrayList<>(pageAccountPassTicket.getRecords().size());
        pageAccountPassTicket.getRecords().forEach(e -> {
            MiniAccountPassTicketVo vo = new MiniAccountPassTicketVo();
            if(passTicketMap.get(e.getPassTicketId()) != null){
                BeanUtil.copyProperties(passTicketMap.get(e.getPassTicketId()), vo);
            }
            vo.setMiniAccountPassTicketId(e.getId());
            vo.setEndTime(e.getEndTime());
            vo.setPassTicketId(e.getPassTicketId());
            voList.add(vo);
        });
        pageShopPassTicketVo.setRecords(voList);
        return pageShopPassTicketVo;
    }

    @Override
    public IPage<MiniShopsPartnerVo> pageShopsPartnerOrderByTicketId(MiniShopsPartnerParam miniShopsPartnerParam) {
        IPage<MiniShopsPartnerVo> pageVo = new Page<>(miniShopsPartnerParam.getCurrent(), miniShopsPartnerParam.getSize());
        Long ticketId = miniShopsPartnerParam.getTicketId();
        // 用户通行票记录id
        Long miniAccountPassTicketId = miniShopsPartnerParam.getMiniAccountPassTicketId();
        Double lat = miniShopsPartnerParam.getLat();
        Double lng = miniShopsPartnerParam.getLng();
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        //查询所有可用商家
        List<ShopsPartner> shopsPartnerList = this.remoteShopsService.getShopPartnerByTicketId(ticketId);
        if(null == shopsPartnerList){
            return pageVo;
        }
        List<MiniShopsPartnerVo> voList = new ArrayList<>(shopsPartnerList.size());
        // 查询当前用户是否购买了此通行票
        MiniAccountPassTicket accountPassTicket = null;
        if(null != miniAccountPassTicketId){
            accountPassTicket = this.getById(miniAccountPassTicketId);
        }else{
            // 查询用户最新购买此通行票的记录
            LambdaQueryWrapper<MiniAccountPassTicket> accountTicketWrapper = new LambdaQueryWrapper<>();
            accountTicketWrapper.eq(MiniAccountPassTicket::getUserId, userId).eq(MiniAccountPassTicket::getPassTicketId, ticketId)
                    .orderByDesc(MiniAccountPassTicket::getCreateTime).last("limit 1");
            accountPassTicket = this.getOne(accountTicketWrapper);
        }
        if(null == accountPassTicket){
            //未购买过此通行票，直接根据商家距离远近进行排序
            shopsPartnerList.stream().forEach(e -> {
                MiniShopsPartnerVo vo = new MiniShopsPartnerVo();
                BeanUtil.copyProperties(e, vo);
                Double distance = 0d;
                if(null != e.getMapX() && null != e.getMapY() && null != lng && null != lat){
                    distance = DistanceUtil.distanceByLngLat(lng, lat, e.getMapX(), e.getMapY());
                }
                vo.setDistance(distance);
                vo.setFormatDistance(DistanceUtil.formatDistance(distance));
                vo.setBuyFlag(0);
                vo.setUsableNum(0);
                vo.setBusinessFlag(0);
                if(null != e.getTradeStatus() && e.getTradeStatus() == TradeStatusEnum.START.getType()){
                    // 营业
                    if(StrUtil.isNotBlank(e.getTradeDate()) && null != e.getBusinessBeginHours() && null != e.getBusinessEndHours()){
                        if(e.getTradeDate().contains(DateUtils.weekDay(new Date()))){
                            // 营业日期包含当前日期
                            Date beginTime = DateUtil.parse(DateUtil.format(e.getBusinessBeginHours(), "HH:mm"), "HH:mm");
                            Date endTime = DateUtil.parse(DateUtil.format(e.getBusinessEndHours(), "HH:mm"), "HH:mm");
                            Date nowTime = DateUtil.parse(DateUtil.format(new Date(), "HH:mm"), "HH:mm");
                            if(DateUtil.isIn(nowTime, beginTime, endTime)){
                                vo.setBusinessFlag(1);
                            }
                        }
                    }
                }
                voList.add(vo);
            });
        }else{
            // 已买此票
            // 查询当前通行票
            ShopPassTicket ticket = this.remoteShopsService.getPassTicketById(ticketId);
            // 每个商家可使用次数
            Integer useableTimes = ticket.getUseableTimes();
            shopsPartnerList.stream().forEach(e -> {
                String idStr = miniAccountPassTicketId + "";
                String userKey = RedisKey.getUserPassTicketNumKey(userId, idStr, e.getShopId());
                AccountRedis accountRedis = new AccountRedis();
                // 可用次数
                String usableNum = accountRedis.get(userKey);
                if(StrUtil.isBlank(usableNum)){
                    usableNum = useableTimes + "";
                }
                // 已用次数 = 总次数 - 可用次数
                Integer usedNum = useableTimes - Integer.parseInt(usableNum);

                MiniShopsPartnerVo vo = new MiniShopsPartnerVo();
                BeanUtil.copyProperties(e, vo);
                Double distance = 0d;
                if(null != e.getMapX() && null != e.getMapY() && null != lng && null != lat){
                    distance = DistanceUtil.distanceByLngLat(lng, lat, e.getMapX(), e.getMapY());
                }
                vo.setDistance(distance);
                vo.setFormatDistance(DistanceUtil.formatDistance(distance));
                vo.setBuyFlag(1);
                vo.setBusinessFlag(0);
                if(null != e.getTradeStatus() && e.getTradeStatus() == TradeStatusEnum.START.getType()){
                    // 营业
                    if(StrUtil.isNotBlank(e.getTradeDate()) && null != e.getBusinessBeginHours() && null != e.getBusinessEndHours()){
                        if(e.getTradeDate().contains(DateUtils.weekDay(new Date()))){
                            // 营业日期包含当前日期
                            Date beginTime = DateUtil.parse(DateUtil.format(e.getBusinessBeginHours(), "HH:mm"), "HH:mm");
                            Date endTime = DateUtil.parse(DateUtil.format(e.getBusinessEndHours(), "HH:mm"), "HH:mm");
                            Date nowTime = DateUtil.parse(DateUtil.format(new Date(), "HH:mm"), "HH:mm");
                            if(DateUtil.isIn(nowTime, beginTime, endTime)){
                                vo.setBusinessFlag(1);
                            }
                        }
                    }
                }
                vo.setUsableNum(Integer.parseInt(usableNum));
                vo.setUsedNum(usedNum);
                voList.add(vo);
            });
        }
        // 排序，先按照可用次数排序，再按照距离进行排序
        Collections.sort(voList, new Comparator<MiniShopsPartnerVo>() {
            @Override
            public int compare(MiniShopsPartnerVo o1, MiniShopsPartnerVo o2) {
                // 只要有可用次数就置为1，因为只要还能用，排序的优先级就要按照距离优先排序
                int usableNumV1 = o1.getUsableNum() > 0 ? 1 : 0;
                int usableNumV2 = o2.getUsableNum()> 0 ? 1 : 0;
                // 先比较可用次数
                if(usableNumV1 != usableNumV2){
                    return usableNumV1 - usableNumV2;
                }
                // 可用次数相等，再比较距离
                return o1.getDistance().compareTo(o2.getDistance());
            }
        });
        int fromIndex = miniShopsPartnerParam.getCurrent() * miniShopsPartnerParam.getSize() - miniShopsPartnerParam.getSize();
        int toIndex = miniShopsPartnerParam.getCurrent() * miniShopsPartnerParam.getSize();
        int total = voList.size();
        if(toIndex > total){
            toIndex = total;
        }
        int pages = 0;
        if(total%miniShopsPartnerParam.getSize() == 0){
            pages = total/miniShopsPartnerParam.getSize();
        }else{
            pages = total/miniShopsPartnerParam.getSize() + 1;
        }
        List<MiniShopsPartnerVo> records = null;
        if(fromIndex > total){
            records = new ArrayList<>();
        }else {
            records = voList.subList(fromIndex, toIndex);
        }
        pageVo.setRecords(records);
        pageVo.setTotal(total);
        pageVo.setPages(pages);
        return pageVo;
    }

    @Override
    public Boolean deleteByPassTicketId(Long orderId) {
        LambdaQueryWrapper<MiniAccountPassTicket>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountPassTicket::getOrderId,orderId);
        int result = this.baseMapper.delete(wrapper);
        return result>0;
    }

    @Override
    public Boolean freezeAccountPassTicket(Long orderId) {
        LambdaUpdateWrapper<MiniAccountPassTicket> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MiniAccountPassTicket::getOrderId,orderId);
        wrapper.set(MiniAccountPassTicket::getStatus,PromotionStatusEnum.FREEZE.getStatus());
        int result = this.baseMapper.update(null, wrapper);
        return result>0;
    }

    @Override
    public Boolean thawAccountPassTicket(Long orderId) {
        LambdaUpdateWrapper<MiniAccountPassTicket> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MiniAccountPassTicket::getOrderId,orderId);
        wrapper.set(MiniAccountPassTicket::getStatus,PromotionStatusEnum.UN_USE.getStatus());
        int result = this.baseMapper.update(null, wrapper);
        return result>0;
    }
}
