package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.account.api.entity.MemberLevelRule;
import com.medusa.gruul.account.model.vo.MemberLevelRuleVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:46 2024/11/5
 */
@Repository
public interface MemberLevelRuleMapper extends BaseMapper<MemberLevelRule> {

    /**
     * 获取会员等级规则
     * @return
     */
    List<MemberLevelRuleVo>getMemberLevelRule(@Param("memberTypeId") Long memberTypeId);

    /**
     * 获取某个会员类型下，某个等级以上的会员等级id
     * @param sort
     * @param mainId
     * @return
     */
    List<String> getMemberLevelIds(@Param("sort")Integer sort, @Param("mainId")String mainId);
}
