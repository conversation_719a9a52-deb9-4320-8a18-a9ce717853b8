package com.medusa.gruul.account.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MemberLevel;
import com.medusa.gruul.account.api.entity.MemberLevelRelation;
import com.medusa.gruul.account.api.entity.MiniAccountApplyRegionMember;
import com.medusa.gruul.account.api.enums.UpgradeTypeEnum;
import com.medusa.gruul.account.api.model.message.UpgradeMemberLevelMessage;
import com.medusa.gruul.account.mapper.MiniAccountApplyRegionMemberMapper;
import com.medusa.gruul.account.model.dto.AuditDataDto;
import com.medusa.gruul.account.model.param.MiniAccountApplyRegionMemberParam;
import com.medusa.gruul.account.model.vo.MiniAccountApplyRegionMemberVo;
import com.medusa.gruul.account.mq.Sender;
import com.medusa.gruul.account.service.IMemberLevelRelationService;
import com.medusa.gruul.account.service.IMemberLevelService;
import com.medusa.gruul.account.service.IMiniAccountApplyRegionMemberService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.constant.enums.MemberFlagEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.StringUtil;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:13 2025/6/27
 */
@Service
public class MiniAccountApplyRegionMemberServiceImpl extends ServiceImpl<MiniAccountApplyRegionMemberMapper, MiniAccountApplyRegionMember> implements IMiniAccountApplyRegionMemberService {

    @Autowired
    private IMemberLevelRelationService memberLevelRelationService;
    @Autowired
    private IMemberLevelService memberLevelService;
    @Autowired
    private Sender sender;

    @Override
    public PageUtils<MiniAccountApplyRegionMemberVo> page(MiniAccountApplyRegionMemberParam param) {
        IPage<MiniAccountApplyRegionMemberVo> page =  this.baseMapper.pageMiniAccountApplyRegionMemberVo(new Page<MiniAccountApplyRegionMemberVo>(param.getCurrent(),param.getSize()),param);
        return new PageUtils<>(page);
    }

    @Override
    public void miniAccountApplyRegionMemberAudit(AuditDataDto dto) {

        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (curUserDto == null) {
            throw new ServiceException("数据异常");
        }



        if(dto.getId() == null){
            throw new ServiceException("区域等级申请审核id不能为空！");
        }

        MiniAccountApplyRegionMember miniAccountApplyRegionMember = this.getById(dto.getId());

        if(miniAccountApplyRegionMember == null){
            throw new ServiceException("区域等级申请审核记录不存在！");
        }

        if(dto.getAuditStatus() == null){
            throw new ServiceException("审核状态不能为空！");
        }

        if(StringUtils.isEmpty(dto.getAuditReason())){
            throw new ServiceException("审核意见不能为空！");
        }
        if(dto.getAuditStatus() == ApproveStatusEnum.APPROVED.getStatus()){
            LambdaQueryWrapper<MemberLevelRelation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MemberLevelRelation::getAgentRegionCode,miniAccountApplyRegionMember.getApplyAgentRegionCode());
            wrapper.eq(MemberLevelRelation::getDeleted, CommonConstants.NUMBER_ZERO);
            int count = memberLevelRelationService.count(wrapper);
            if(count>0){
                throw new ServiceException("区域已经被其他会员申请，审核通过失败！", SystemCode.DATA_NOT_EXIST_CODE);
            }
        }

        miniAccountApplyRegionMember.setAuditStatus(dto.getAuditStatus());
        miniAccountApplyRegionMember.setAuditReason(dto.getAuditReason());
        miniAccountApplyRegionMember.setAuditTime(LocalDateTime.now());
        miniAccountApplyRegionMember.setAuditPlatformUserId(Long.valueOf(curUserDto.getUserId()));
        miniAccountApplyRegionMember.setAuditPlatformUserName(curUserDto.getNikeName());

        this.updateById(miniAccountApplyRegionMember);

        if(dto.getAuditStatus() == ApproveStatusEnum.APPROVED.getStatus()){
            LambdaQueryWrapper<MemberLevelRelation>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MemberLevelRelation::getMemberTypeId,miniAccountApplyRegionMember.getMemberTypeId());
            wrapper.eq(MemberLevelRelation::getUserId,miniAccountApplyRegionMember.getUserId());
            MemberLevelRelation memberLevelRelation = memberLevelRelationService.getOne(wrapper);
            if(memberLevelRelation!=null){

                if(StringUtils.isNotEmpty(memberLevelRelation.getMemberLevelId())){
                    MemberLevel oldMemberLevel = memberLevelService.getById(memberLevelRelation.getMemberLevelId());
                    MemberLevel upgradeMemberLevel = memberLevelService.getById(miniAccountApplyRegionMember.getApplyMemberLevelId());
                    if(oldMemberLevel.getMemberFlag() == MemberFlagEnum.NO.getStatus()&&
                            upgradeMemberLevel.getMemberFlag() == MemberFlagEnum.YES.getStatus()){
                        memberLevelRelation.setUpLevelTime(LocalDateTime.now());
                    }
                }

                memberLevelRelation.setMemberLevelId(miniAccountApplyRegionMember.getApplyMemberLevelId()+"");
                memberLevelRelation.setApplyMemberLevelId("");
                memberLevelRelation.setAgentRegionCode(miniAccountApplyRegionMember.getApplyAgentRegionCode());
                memberLevelRelation.setRegionType(miniAccountApplyRegionMember.getRegionType());
                memberLevelRelation.setAgentRegionName(miniAccountApplyRegionMember.getApplyAgentRegionName());


                if(memberLevelRelation.getUpLevelTime()==null){
                    memberLevelRelation.setUpLevelTime(LocalDateTime.now());
                }

                memberLevelRelationService.updateById(memberLevelRelation);

                UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                upgradeMemberLevelMessage.setUserId(Long.valueOf(miniAccountApplyRegionMember.getUserId()));
                //发送升级会员等级消息
                sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
            }else{

                MemberLevelRelation newMemberLevelRelation = new MemberLevelRelation();
                newMemberLevelRelation.setMemberLevelId(miniAccountApplyRegionMember.getApplyMemberLevelId()+"");
                newMemberLevelRelation.setMemberTypeId(miniAccountApplyRegionMember.getMemberTypeId());
                newMemberLevelRelation.setUserId(miniAccountApplyRegionMember.getUserId());
                newMemberLevelRelation.setUpLevelTime(LocalDateTime.now());

                newMemberLevelRelation.setAgentRegionCode(miniAccountApplyRegionMember.getApplyAgentRegionCode());
                newMemberLevelRelation.setRegionType(miniAccountApplyRegionMember.getRegionType());
                newMemberLevelRelation.setAgentRegionName(miniAccountApplyRegionMember.getApplyAgentRegionName());
                memberLevelRelationService.save(newMemberLevelRelation);

                UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                upgradeMemberLevelMessage.setUserId(Long.valueOf(miniAccountApplyRegionMember.getUserId()));
                //发送升级会员等级消息
                sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
            }

        }

    }
}
