package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MemberLevel;
import com.medusa.gruul.account.api.entity.MemberLevelRule;
import com.medusa.gruul.account.model.dto.MemberLevelRuleDto;
import com.medusa.gruul.account.model.param.MemberLevelRuleMessageParam;
import com.medusa.gruul.account.model.vo.MemberLevelRuleVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:49 2024/11/5
 */
public interface IMemberLevelRuleService extends IService<MemberLevelRule> {
    /**
     * 获取会员等级规则
     * @return
     */
    List<MemberLevelRuleVo> getMemberLevelRule(MemberLevelRuleMessageParam param);

    /**
     * 通过 会员等级 查询是否能申请入驻商家
     * @param levelIds
     * @return
     */
    Boolean checkApplyShopByLevelId(List<String> levelIds);

    /**
     * 通过 userId 查询用户会员等级是否能申请入驻商家
     * @param showUserId
     * @return Boolean
     */
    Boolean checkApplyShopByUserId(String showUserId);

    /**
     * 通过会员等级id获取会员等级升级规则
     * @param memberLevelId
     * @return
     */
    MemberLevelRuleVo getByMemberLevelId(Long memberLevelId);

    /**
     * 获取某个会员类型下，某个等级以上的会员等级id
     * @param sort
     * @param memberTypeId
     * @return
     */
    List<String> getMemberLevelIds(Integer sort, String memberTypeId);
}
