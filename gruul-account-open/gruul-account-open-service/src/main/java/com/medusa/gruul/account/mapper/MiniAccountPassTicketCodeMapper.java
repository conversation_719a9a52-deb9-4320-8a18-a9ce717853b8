package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.medusa.gruul.account.api.entity.MiniAccountPassTicketCode;
import com.medusa.gruul.account.api.model.vo.ShopPassTicketCodeVo;
import com.medusa.gruul.account.api.model.param.MiniAccountPassTicketCodeParam;
import com.medusa.gruul.account.api.model.vo.ShopsPassTicketCodeExcelVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR> by rbw
 * @date created in 2023/08/22
 */
@Repository
public interface MiniAccountPassTicketCodeMapper extends BaseMapper<MiniAccountPassTicketCode> {

    /**
     * 查询商家核销记录
     * @param page
     * @param miniAccountPassTicketCodeParam
     * @return
     */
    IPage<ShopPassTicketCodeVo> selectShopVerifyList(IPage page , @Param("miniAccountPassTicketCodeParam") MiniAccountPassTicketCodeParam miniAccountPassTicketCodeParam);

    /**
     * 查询导出商家核销记录
     * @param page
     * @param miniAccountPassTicketCodeParam
     * @return
     */
    IPage<ShopsPassTicketCodeExcelVo> selectShopVerifyExcelList(IPage page , @Param("miniAccountPassTicketCodeParam") MiniAccountPassTicketCodeParam miniAccountPassTicketCodeParam);



    /**
     * 查询商家自己的核销记录
     * @param page
     * @param miniAccountPassTicketCodeParam
     * @return
     */
    IPage<ShopPassTicketCodeVo> selectShopUserVerifyList(IPage page , @Param("miniAccountPassTicketCodeParam") MiniAccountPassTicketCodeParam miniAccountPassTicketCodeParam);

}
