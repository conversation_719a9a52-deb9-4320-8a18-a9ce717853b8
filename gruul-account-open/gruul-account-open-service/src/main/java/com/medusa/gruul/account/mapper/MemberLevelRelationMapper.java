package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.account.api.entity.MemberLevelRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:16 2025/5/22
 */
@Mapper
public interface MemberLevelRelationMapper extends BaseMapper<MemberLevelRelation> {
    /**
     * 查询会员类型下已经被代理的区域
     * @param memberTypeId
     * @return
     */
    List<String> getAgentRegionCodeList(@Param("memberTypeId") Long memberTypeId);
}
