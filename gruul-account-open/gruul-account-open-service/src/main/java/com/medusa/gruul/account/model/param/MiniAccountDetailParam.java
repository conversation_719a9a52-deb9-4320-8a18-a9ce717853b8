package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:52 2024/12/10
 */
@ApiModel(value = "MiniAccountDetailParam 实体", description = "客户明细查询 param")
@Data
public class MiniAccountDetailParam extends QueryParam {

    @ApiModelProperty(value = "用户昵称")
    private String nikeName;
    @ApiModelProperty(value = "手机号码")
    private String phone;
    @ApiModelProperty(value = "会员类型")
    private List<String> memberTypeIds;
    @ApiModelProperty(value = "会员等级")
    private List<String> memberLevelIds;
}
