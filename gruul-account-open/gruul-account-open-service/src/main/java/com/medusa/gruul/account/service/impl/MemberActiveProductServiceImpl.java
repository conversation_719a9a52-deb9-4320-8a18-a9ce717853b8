package com.medusa.gruul.account.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MemberActiveProduct;
import com.medusa.gruul.account.mapper.MemberActiveProductMapper;
import com.medusa.gruul.account.model.vo.MemberActiveProductVo;
import com.medusa.gruul.account.service.IMemberActiveProductService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:18 2025/6/3
 */
@Service
public class MemberActiveProductServiceImpl extends ServiceImpl<MemberActiveProductMapper, MemberActiveProduct> implements IMemberActiveProductService {
    /**
     * 获取激活会员商品列表
     * @return
     */
    @Override
    public List<MemberActiveProductVo> getList() {
        return this.baseMapper.getList();
    }
}
