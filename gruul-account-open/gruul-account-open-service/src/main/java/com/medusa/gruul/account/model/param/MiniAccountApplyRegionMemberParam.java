package com.medusa.gruul.account.model.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:07 2025/6/28
 */
@Data
@ApiModel(value = "会员区域等级申请审核查询 实体", description = "会员区域等级申请审核查询 param")
public class MiniAccountApplyRegionMemberParam extends QueryParam {

    @ApiModelProperty(value = "小程序用户名称")
    private String nikeName;

    @ApiModelProperty(value = "申请代理区域名称")
    private String applyAgentRegionName;

    @ApiModelProperty(value = "审核状态:100->待审核，101->审核通过，200->驳回")
    private Integer auditStatus;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "申请升级的会员等级")
    private String memberLevel;

    @ApiModelProperty(value = "小程序用户id")
    private String userId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "申请开始时间")
    private LocalDate applyBeginTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "申请结束时间")
    private LocalDate applyEndTime;

}
