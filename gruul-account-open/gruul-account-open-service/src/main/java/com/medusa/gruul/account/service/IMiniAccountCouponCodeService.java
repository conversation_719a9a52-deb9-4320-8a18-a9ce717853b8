package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountCouponCode;
import com.medusa.gruul.account.api.model.param.MiniAccountCouponCodeParam;
import com.medusa.gruul.account.api.model.vo.ShopCouponCodeVo;
import com.medusa.gruul.account.model.dto.MiniAccountCouponCodeDto;
import com.medusa.gruul.account.model.vo.MiniAccountCouponCodeVo;
import com.medusa.gruul.shops.api.model.AccountCouponVo;

public interface IMiniAccountCouponCodeService extends IService<MiniAccountCouponCode> {

    /**
     * 添加记录，生成验证码
     * @param miniAccountCouponCodeDto
     * @return
     */
    MiniAccountCouponCode add(MiniAccountCouponCodeDto miniAccountCouponCodeDto);
    /**
     * 获取优惠券验证码
     * @param miniAccountCouponCodeDto
     * @return
     */
    MiniAccountCouponCodeVo getCode(MiniAccountCouponCodeDto miniAccountCouponCodeDto);

    /**
     * 核销优惠券验证码
     * @param verifyCode 核销码
     * @return
     */
    MiniAccountCouponCode verifyCode(String verifyCode);

    /**
     * 商家用户查询自己核销的记录
     * @return
     */
    IPage<ShopCouponCodeVo> pageShopUserVerifyCode(MiniAccountCouponCodeParam miniAccountCouponCodeParam);

    /**
     * 查询商家核销的记录
     * @return
     */
    IPage<ShopCouponCodeVo> pageShopVerifyCode(MiniAccountCouponCodeParam miniAccountCouponCodeParam);

    /**
     * 导出商家核销的记录
     * @param miniAccountCouponCodeParam
     */
    void exportShopCouponCode(MiniAccountCouponCodeParam miniAccountCouponCodeParam);

    /**
     * 通过验证码获取优惠券证记录
     * @param verifyCode 核销码
     * @return
     */
    AccountCouponVo getCouponByCode(String verifyCode);
}
