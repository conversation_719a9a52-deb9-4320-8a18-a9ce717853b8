package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccountAddress;
import com.medusa.gruul.account.api.entity.MiniAccountApplyRegionMember;
import com.medusa.gruul.account.model.param.MiniAccountApplyRegionMemberParam;
import com.medusa.gruul.account.model.vo.MiniAccountApplyRegionMemberVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:03 2025/6/27
 */
@Mapper
public interface MiniAccountApplyRegionMemberMapper extends BaseMapper<MiniAccountApplyRegionMember> {

    /**
     * 分页查询区域会员审核列表
     * @param page
     * @param param
     * @return
     */
    IPage<MiniAccountApplyRegionMemberVo> pageMiniAccountApplyRegionMemberVo(Page<MiniAccountApplyRegionMemberVo> page, @Param("param") MiniAccountApplyRegionMemberParam param);
}
