package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccountIntegral;
import com.medusa.gruul.account.model.param.IntegralRankingParam;
import com.medusa.gruul.account.model.param.MiniAccountIntegralManageParam;
import com.medusa.gruul.account.model.param.MiniAccountIntegralParam;
import com.medusa.gruul.account.model.vo.AccountIntegralVo;
import com.medusa.gruul.account.model.vo.IntegralRankingVo;
import com.medusa.gruul.account.model.vo.MiniAccountIntegralManageVo;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: plh
 * @Description: 用户积分Mapper类
 *
 * @Date: Created in 15:35 2023/8/22
 */

public interface MiniAccountIntegralMapper extends BaseMapper<MiniAccountIntegral> {
    /**
     * 分页查询菜单列表
     * @param accountIntegralVoPage
     * @param accountIntegralParam
     * @return
     */
    IPage<AccountIntegralVo> selectAccountIntegral(Page<AccountIntegralVo> accountIntegralVoPage, @Param("paramMap") MiniAccountIntegralParam accountIntegralParam);

    /**
     * 分页查询积分排名
     * @param page
     * @param param
     * @return
     */
    IPage<IntegralRankingVo> searchIntegralRanking(Page<IntegralRankingVo> page, @Param("paramMap")IntegralRankingParam param);

    /**
     * 分页查询积分明细列表
     * @param page
     * @param param
     * @return
     */
    IPage<MiniAccountIntegralManageVo> searchMiniAccountIntegralManage(Page<MiniAccountIntegralManageVo> page, @Param("paramMap") MiniAccountIntegralManageParam param);

}
