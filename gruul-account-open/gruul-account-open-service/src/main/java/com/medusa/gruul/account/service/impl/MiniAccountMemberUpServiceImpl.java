package com.medusa.gruul.account.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccountMemberUp;
import com.medusa.gruul.account.mapper.MiniAccountMemberUpMapper;
import com.medusa.gruul.account.model.param.MiniAccountMemberUpParam;
import com.medusa.gruul.account.service.IMiniAccountMemberUpService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 8:47 2025/6/19
 */
@Service
public class MiniAccountMemberUpServiceImpl extends ServiceImpl<MiniAccountMemberUpMapper, MiniAccountMemberUp>implements IMiniAccountMemberUpService {
    @Override
    public MiniAccountMemberUp getMiniAccountMemberUp(MiniAccountMemberUpParam param) {

        LambdaQueryWrapper<MiniAccountMemberUp>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountMemberUp::getDeleted, CommonConstants.NUMBER_ZERO);
        wrapper.eq(MiniAccountMemberUp::getUserId,param.getUserId());
        wrapper.eq(MiniAccountMemberUp::getMemberTypeId,param.getMemberTypeId());
        MiniAccountMemberUp miniAccountMemberUp = this.getOne(wrapper);
        return miniAccountMemberUp;
    }

    @Override
    public BigDecimal getSumAmount(List<String> shopUserIds) {
        BigDecimal sumAmount = this.baseMapper.getSumAmount(shopUserIds);
        return sumAmount;
    }

}
