package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.account.api.entity.MiniAccountMemberUp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 8:46 2025/6/19
 */
@Mapper
public interface MiniAccountMemberUpMapper extends BaseMapper<MiniAccountMemberUp> {
    /**
     * 获取团队升级消费额
     * @param shopUserIds
     * @return
     */
    BigDecimal getSumAmount(@Param("shopUserIds")List<String> shopUserIds);
}
