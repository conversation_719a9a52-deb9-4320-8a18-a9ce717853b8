package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountBank;
import com.medusa.gruul.account.model.dto.MiniAccountBankDto;
import com.medusa.gruul.account.model.param.MiniAccountBankParam;
import com.medusa.gruul.account.model.vo.MiniAccountBankVo;
import com.medusa.gruul.common.core.util.PageUtils;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:20 2025/5/27
 */
public interface IMiniAccountBankService extends IService<MiniAccountBank> {

    /**
     * 新增用户银行卡
     * @param dto
     */
    void addMiniAccountBank(MiniAccountBankDto dto);

    /**
     * 编辑用户银行卡
     * @param dto
     */
    void editMiniAccountBank(MiniAccountBankDto dto);

    /**
     * 删除用户银行卡
     * @param dto
     */
    void removeMiniAccountBank(MiniAccountBankDto dto);

    /**
     * 分页查询银行卡
     * @param param
     * @return
     */
    PageUtils<MiniAccountBankVo>queryList(MiniAccountBankParam param);

    /**
     * 获取银行卡列表
     * @param userId
     * @return
     */
    List<MiniAccountBankVo> getMiniAccountBank(String userId);
}
