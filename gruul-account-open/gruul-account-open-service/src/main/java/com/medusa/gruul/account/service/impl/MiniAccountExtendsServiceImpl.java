package com.medusa.gruul.account.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.api.enums.MiniAccountExtendsStatusEnum;
import com.medusa.gruul.account.api.enums.UpgradeTypeEnum;
import com.medusa.gruul.account.api.model.MiniAccountExtendsUpdateDto;
import com.medusa.gruul.account.api.model.message.UpgradeMemberLevelMessage;
import com.medusa.gruul.account.mapper.MiniAccountExtendsMapper;
import com.medusa.gruul.account.model.dto.UpdateUserExtendsInfoDto;
import com.medusa.gruul.account.mq.Sender;
import com.medusa.gruul.account.service.*;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.MemberLevelRuleTypeEnum;
import com.medusa.gruul.common.core.constant.enums.MessageTypeEnum;
import com.medusa.gruul.common.core.constant.enums.PriceTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.enums.ProductTypeEnum;
import com.medusa.gruul.order.api.enums.OrderTypeEnum;
import com.medusa.gruul.order.api.model.OrderItemVo;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.platform.api.entity.SpecialSetting;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户信息扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-18
 */
@Service
@Slf4j
public class MiniAccountExtendsServiceImpl extends ServiceImpl<MiniAccountExtendsMapper, MiniAccountExtends> implements IMiniAccountExtendsService {

    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;

    @Autowired
    private IMemberLevelRuleProductService memberLevelRuleProductService;
    @Autowired
    private IMemberLevelRuleMessageService memberLevelRuleMessageService;
    @Autowired
    private IMemberLevelRuleService memberLevelRuleService;
    @Autowired
    private IMiniAccountService miniAccountService;
    @Autowired
    private Sender sender;

    @Autowired
    private IMiniAccountShopsService miniAccountShopsService;
    @Override
    public MiniAccountExtends findByUserId(String userId) {
        return this.baseMapper.selectOne(new QueryWrapper<MiniAccountExtends>().eq("user_id", userId));
    }



    @Override
    public Boolean portionAttributeModify(String userId, MiniAccountExtendsUpdateDto miniAccountExtendsUpdateDto) {
        if (miniAccountExtendsUpdateDto == null) {
            log.info("miniAccountExtendsUpdateDto 对象为null");
            return Boolean.FALSE;
        }
        Integer optionType = miniAccountExtendsUpdateDto.getOptionType();
        boolean isCorrect = optionType < CommonConstants.NUMBER_ZERO || optionType > CommonConstants.NUMBER_THREE;
        if (ObjectUtil.isNull(optionType) || isCorrect) {
            log.info("optionType={},操作类型无效", optionType);
            return Boolean.FALSE;
        }
        MiniAccountExtends accountExtends = findByShopUserId(userId);
        if (accountExtends == null) {
            log.info("userId={},用户扩展数据不存在", userId);
            return Boolean.FALSE;
        }
        MiniAccountExtends updateData = new MiniAccountExtends();
        updateData.setId(accountExtends.getId());
        Boolean flag = Boolean.FALSE;
        switch (optionType) {
            case 1:
                if (ObjectUtil.isEmpty(miniAccountExtendsUpdateDto.getCumsumType()) || ObjectUtil.isEmpty(miniAccountExtendsUpdateDto.getCumsumLimit())
                        || StrUtil.isEmpty(miniAccountExtendsUpdateDto.getBusinessId())) {
                    return Boolean.FALSE;
                }
                flag = consumptionAccumulation(accountExtends, miniAccountExtendsUpdateDto.getCumsumLimit(),
                        miniAccountExtendsUpdateDto.getCumsumType(), miniAccountExtendsUpdateDto.getBusinessId(), miniAccountExtendsUpdateDto.getLastDealTime());
                break;
            case 2:
                flag = Boolean.TRUE;
                break;
            case 3:
                flag = Boolean.TRUE;
                break;
            default:
                break;
        }
        return flag;
    }

    /**
     * 用户累加消费处理
     *
     * @param accountExtends 用户扩展数据
     * @param cumsumLimit    累加金额
     * @param cumsumType     累加类型  1积分消费  2-订单完成
     * @param businessId     业务id
     * @param lastDealTime   最后交易时间
     */
    private boolean consumptionAccumulation(MiniAccountExtends accountExtends, BigDecimal cumsumLimit, Integer cumsumType, String businessId, LocalDateTime lastDealTime) {
        boolean flag;
        MiniAccountExtends updateData = new MiniAccountExtends();
        updateData.setId(accountExtends.getId());
        updateData.setConsumeTotleMoney(accountExtends.getConsumeTotleMoney().add(cumsumLimit));
        updateData.setConsumeNum(accountExtends.getConsumeNum() + CommonConstants.NUMBER_ONE);
        updateData.setLastDealTime(lastDealTime);
        flag = this.updateById(updateData);
        if (!flag) {
            return Boolean.FALSE;
        }
        return flag;
    }

    @Override
    public void updateUserExtendsInfo(UpdateUserExtendsInfoDto updateUserExtendsInfoDto) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null) {
            throw new ServiceException("用户数据不存在", SystemCode.DATA_NOT_EXIST.getCode());
        }
        MiniAccountExtends miniAccountExtends = this.findByShopUserId(curUser.getUserId());
        if (miniAccountExtends == null) {
            throw new ServiceException("无效用户", SystemCode.DATA_NOT_EXIST.getCode());
        }
        MiniAccountExtends update = new MiniAccountExtends();
        update.setId(miniAccountExtends.getId());
        update.setLastChooseLcation(updateUserExtendsInfoDto.getLastChooseLocation());
        this.updateById(update);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderCompleted(OrderVo orderVo) {
        String tenantId = orderVo.getTenantId();
        TenantContextHolder.setTenantId(tenantId);
        MiniAccountExtends accountExtends = findByShopUserId(orderVo.getUserId());
        MiniAccount miniAccount = miniAccountService.getByShopUserId(orderVo.getUserId());
        if (ObjectUtil.isNull(accountExtends)) {
            log.debug("用户数据不存在,无法增加消息记录");
            return;
        }

        BigDecimal payAmount = BigDecimal.ZERO;
        for (OrderItemVo orderItemVo : orderVo.getOrderItemList()) {
            payAmount = payAmount.add(orderItemVo.getRealAmount());
        }

        //用户累加消费处理
        consumptionAccumulation(accountExtends,
                payAmount, CommonConstants.NUMBER_TWO, orderVo.getId().toString(), null);

        //判断是否启用会员商品升级
        String shopId = orderVo.getShopId();

        UpgradeMemberLevelMessage message = new UpgradeMemberLevelMessage();
        message.setOrderId(orderVo.getId());
        message.setShopId(orderVo.getShopId());
        message.setTenantId(orderVo.getTenantId());
        message.setMessageType(UpgradeTypeEnum.ORDER.getType());

        //判断订单类型-1.商超订单；2.非抽奖订单
        if(orderVo.getType().equals(OrderTypeEnum.MALL)&&
        orderVo.getMallOrderType() != ProductTypeEnum.PRIZE_PRODUCT.getStatus()){
            //发送升级会员等级消息-先处理佣金方案计算，在处理会员升级
            sender.sendUpgradeMemberLevelMessage(message);
        }

//        List<SpecialSetting> specialSettingList = remoteMiniInfoService.getSpecialSettingByShopId(shopId);
//        if(specialSettingList!=null&&specialSettingList.size()>0){
//            SpecialSetting specialSetting = specialSettingList.get(0);
//            if(specialSetting.getMemberSales() == CommonConstants.NUMBER_ONE){
//                //获取会员等级规则商品
//                LambdaQueryWrapper<MemberLevelRuleProduct>wrapper = new LambdaQueryWrapper<>();
//                wrapper.eq(MemberLevelRuleProduct::getDeleted,CommonConstants.NUMBER_ZERO);
//                List<MemberLevelRuleProduct> list = memberLevelRuleProductService.list(wrapper);
//                for (OrderItemVo orderItemVo : orderVo.getOrderItemList()) {
//                    BigDecimal realAmount = orderItemVo.getRealAmount();
//                    if(orderItemVo.getPriceType()!=null&&!orderItemVo.getPriceType().equals("")){
//                        //判断商品是否为会员价
//                        if(orderItemVo.getPriceType() == PriceTypeEnum.MEMBER.getStatus()){
//                            //判断是否属于会员等级规则商品
//                            if(list!=null&&list.size()>0){
//                                List<MemberLevelRuleProduct> dataList = list.stream().filter(e -> e.getProductId().equals(orderItemVo.getProductId()+"") && e.getSkuId().equals(orderItemVo.getProductSkuId()+""))
//                                        .collect(Collectors.toList());
//                                if(dataList!=null&&dataList.size()>0){
//                                    updateMemberAmount(accountExtends,miniAccount,realAmount,tenantId);
//                                }
//                            }else{
//                                //会员等级规则商品为空则所有商品都生效
//                                updateMemberAmount(accountExtends,miniAccount,realAmount,tenantId);
//                            }
//                        }
//                    }
//                }
//            }
//        }

    }

    /**
     * 更新会员消费金额，会员等级
     * @param accountExtends
     * @param miniAccount
     * @param realAmount
     * @param tenantId
     */
    private void updateMemberAmount(MiniAccountExtends accountExtends,MiniAccount miniAccount,BigDecimal realAmount,String tenantId){
        MiniAccountExtends updateData = new MiniAccountExtends();
        updateData.setId(accountExtends.getId());
        updateData.setMemberMoney(accountExtends.getMemberMoney().add(realAmount));
        this.updateById(updateData);
        //1.先获取会员等级规则
        LambdaQueryWrapper<MemberLevelRuleMessage>memberLevelRuleMessageLambdaQueryWrapper = new LambdaQueryWrapper<>();
        memberLevelRuleMessageLambdaQueryWrapper.eq(MemberLevelRuleMessage::getDeleted,CommonConstants.NUMBER_ZERO);
        MemberLevelRuleMessage memberLevelRuleMessage = memberLevelRuleMessageService.getOne(memberLevelRuleMessageLambdaQueryWrapper);
        if(memberLevelRuleMessage!=null&&!"".equals(memberLevelRuleMessage)){
            String type = memberLevelRuleMessage.getType();
            List<String> typeList = Arrays.stream(type.split(",")).collect(Collectors.toList());
            //获取会员等级顺序
            Integer sort = 0;
            if(StringUtils.isNotEmpty(miniAccount.getMemberLevelId())){
                LambdaQueryWrapper<MemberLevelRule>memberLevelRuleLambdaQueryWrapper = new LambdaQueryWrapper<>();
                memberLevelRuleLambdaQueryWrapper.eq(MemberLevelRule::getMemberLevelId,miniAccount.getMemberLevelId());
                List<MemberLevelRule> memberLevelRuleList = memberLevelRuleService.list(memberLevelRuleLambdaQueryWrapper);
                if(memberLevelRuleList!=null&&memberLevelRuleList.size()>0){
                    MemberLevelRule memberLevelRule = memberLevelRuleList.get(0);
                    if(memberLevelRule.getSort()!=null){
                        sort = memberLevelRule.getSort();
                    }
                }
            }
            if(typeList.contains(MemberLevelRuleTypeEnum.MEMBER_AMOUNT.getStatus())){
                //获取消费金额
                BigDecimal amount = accountExtends.getMemberMoney().add(realAmount);
                //1.查看消费总金额是否达到更新等级
                LambdaQueryWrapper<MemberLevelRule>queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MemberLevelRule::getTenantId,tenantId);
                queryWrapper.le(MemberLevelRule::getMemberAmountStart,amount);
                queryWrapper.orderByDesc(MemberLevelRule::getSort);
                List<MemberLevelRule> memberLevelRuleList = memberLevelRuleService.list(queryWrapper);
                if(memberLevelRuleList!=null&&memberLevelRuleList.size()>0){
                    MemberLevelRule memberLevelRule = memberLevelRuleList.get(0);
                    if(memberLevelRule.getSort()>sort){
                        miniAccount.setMemberLevelId(memberLevelRule.getMemberLevelId());
                        miniAccountService.updateById(miniAccount);
                    }
                }
            }
        }
    }

    @Override
    public MiniAccountExtends findByCurrentStatus(String userId) {
        return this.baseMapper.selectByCurrentStatus(userId, CommonConstants.NUMBER_ONE);
    }

    @Override
    public MiniAccountExtends findByShopUserId(String shopUserId) {
        return this.baseMapper.selectOne(new QueryWrapper<MiniAccountExtends>().eq("shop_user_id", shopUserId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyLastDealTime(String shopUserId, LocalDateTime lastDealTime,Integer mallOrderType, String shopId) {
        MiniAccountExtends miniAccountExtends = this.findByShopUserId(shopUserId);
        if (miniAccountExtends == null) {
            throw new ServiceException("无效用户");
        }
        // 更新用户的最后下单时间
        MiniAccountExtends updateData = new MiniAccountExtends();
        updateData.setId(miniAccountExtends.getId());
        updateData.setLastDealTime(lastDealTime);

        //如果订单类型为激活订单
        if(mallOrderType == ProductTypeEnum.ACTIVE_PRODUCT.getStatus()){
            updateData.setStatus(MiniAccountExtendsStatusEnum.YES.getStatus());
        }
        this.updateById(updateData);
        // 添加用户与商家的关联关系
        LambdaQueryWrapper<MiniAccountShops> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(MiniAccountShops::getUserId, miniAccountExtends.getUserId())
                .eq(MiniAccountShops::getShopId, shopId);
        // 查询数量
        long count = miniAccountShopsService.count(queryWrapper1);
        if(count == 0){
            MiniAccountShops  miniAccountShops = new MiniAccountShops();
            miniAccountShops.setUserId(miniAccountExtends.getUserId());
            miniAccountShops.setShopId(shopId);
            miniAccountShops.setTenantId(miniAccountExtends.getTenantId());
            miniAccountShopsService.save(miniAccountShops);
        }

    }

    @Override
    public MiniAccountExtends findByShopIdAndUserId( String userId) {
        return this.baseMapper.selectOne(new QueryWrapper<MiniAccountExtends>().eq("user_id", userId));
    }

    @Override
    public List<String> getTeamShopUserIds(String userId) {
        List<String>shopUserIds = this.baseMapper.getTeamShopUserIds(userId);
        return shopUserIds;
    }

    @Override
    public Integer getDirectMemberQty(Long userId,List<String>memberLevelIds) {
        return this.baseMapper.getDirectMemberQty(userId,memberLevelIds);
    }
}
