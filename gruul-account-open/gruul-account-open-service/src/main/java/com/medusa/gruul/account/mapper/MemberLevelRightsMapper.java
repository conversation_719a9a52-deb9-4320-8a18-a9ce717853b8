package com.medusa.gruul.account.mapper;

import java.util.List;

import com.medusa.gruul.account.api.entity.MemberLevelRights;
import com.medusa.gruul.account.model.vo.MemberLevelRightsVo;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 会员权益
 * @Author: jeecg-boot
 * @Date:   2022-02-23
 * @Version: V1.0
 */
public interface MemberLevelRightsMapper extends BaseMapper<MemberLevelRights> {
    /**
     * 根据会员权益id查询会员权益
     * @param rightsIdList 会员权益id
     * @return 会员权益
     * */
    List<MemberLevelRights> selectRightsList(@Param("rightsIdList") List<String> rightsIdList);
    /**
     * 查询全部会员权益
     *
     * @return 会员权益
     * */
    List<MemberLevelRightsVo> selectRightsVoAllList();
}
