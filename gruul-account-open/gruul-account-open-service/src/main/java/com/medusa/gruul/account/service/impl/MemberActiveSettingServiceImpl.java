package com.medusa.gruul.account.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MemberActiveProduct;
import com.medusa.gruul.account.api.entity.MemberActiveSetting;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.entity.MiniAccountExtends;
import com.medusa.gruul.account.api.enums.MemberActiveSettingStatusEnum;
import com.medusa.gruul.account.api.enums.MiniAccountExtendsStatusEnum;
import com.medusa.gruul.account.mapper.MemberActiveProductMapper;
import com.medusa.gruul.account.mapper.MemberActiveSettingMapper;
import com.medusa.gruul.account.model.dto.MemberActiveProductDto;
import com.medusa.gruul.account.model.dto.MemberActiveSettingDto;
import com.medusa.gruul.account.model.vo.MemberActiveProductVo;
import com.medusa.gruul.account.model.vo.MemberActiveSettingVo;
import com.medusa.gruul.account.service.IMemberActiveProductService;
import com.medusa.gruul.account.service.IMemberActiveSettingService;
import com.medusa.gruul.account.service.IMiniAccountExtendsService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.dto.CurUserDto;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:16 2025/6/3
 */
@Service
public class MemberActiveSettingServiceImpl extends ServiceImpl<MemberActiveSettingMapper, MemberActiveSetting>implements IMemberActiveSettingService {

    @Autowired
    private IMemberActiveProductService memberActiveProductService;

    @Autowired
    private IMiniAccountExtendsService miniAccountExtendsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(MemberActiveSettingDto dto) {

        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();

        Integer activeDays = dto.getActiveDays();
        Integer remainingDays = dto.getRemainingDays();
        Integer status = dto.getStatus();
        List<MemberActiveProductDto> productList = dto.getProductList();

        if(status == null){
            throw new ServiceException("会员有效期设置状态不能为空！");
        }
        if(activeDays == null){
            throw new ServiceException("有效期天数不能为空！");
        }
        if(remainingDays == null){
            throw new ServiceException("剩余天数提醒不能为空！");
        }

        if(activeDays != null && remainingDays != null
                && remainingDays>activeDays){
            throw new ServiceException("剩余天数不能大于等于有效期天数！");
        }

        //删除历史的激活会员商品数据
        List<MemberActiveProduct> list = memberActiveProductService.list();
        if(list!=null&&list.size()>0){
            for (MemberActiveProduct memberActiveProduct : list) {
                this.memberActiveProductService.removeById(memberActiveProduct.getId());
            }
        }
        LambdaQueryWrapper<MemberActiveSetting>lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MemberActiveSetting::getDeleted,CommonConstants.NUMBER_ZERO);
        MemberActiveSetting memberActiveSetting = this.getOne(lambdaQueryWrapper);

        if(memberActiveSetting == null){//新增
            memberActiveSetting = new MemberActiveSetting();
            BeanUtils.copyProperties(dto,memberActiveSetting);
            memberActiveSetting.setCreateUserName(curUserDto.getNikeName());
            memberActiveSetting.setCreateUserId(curUserDto.getUserId());
            this.save(memberActiveSetting);

        }else{//编辑
            BeanUtils.copyProperties(dto,memberActiveSetting);
            memberActiveSetting.setLastModifyUserName(curUserDto.getNikeName());
            memberActiveSetting.setLastModifyUserId(curUserDto.getUserId());
            this.updateById(memberActiveSetting);
        }

        //判断会员有效期设置是否停用,停用要将用户信息扩展表里所有为冻结状态的记录设置为正常状态
        if(dto.getStatus() == MemberActiveSettingStatusEnum.NO.getStatus()){
            LambdaQueryWrapper<MiniAccountExtends>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MiniAccountExtends::getDeleted,CommonConstants.NUMBER_ZERO);
            List<MiniAccountExtends> miniAccountExtendsList = miniAccountExtendsService.list(wrapper);
            if(miniAccountExtendsList!=null&&miniAccountExtendsList.size()>0){
                for (MiniAccountExtends miniAccountExtends : miniAccountExtendsList) {
                    miniAccountExtends.setStatus(MiniAccountExtendsStatusEnum.YES.getStatus());
                    miniAccountExtendsService.updateById(miniAccountExtends);
                }
            }
        }

        //新增激活会员商品数据
        if(productList!=null&&productList.size()>0){
            for (MemberActiveProductDto memberActiveProductDto : productList) {
                if(memberActiveProductDto.getProductId() == null){
                    throw new ServiceException("商品ID不能为空！");
                }
                if(memberActiveProductDto.getProductSkuId() == null){
                    throw new ServiceException("商品sku编号不能为空！");
                }
                if(memberActiveProductDto.getProductQuantity() == null){
                    throw new ServiceException("商品数量不能为空！");
                }
                MemberActiveProduct memberActiveProduct = new MemberActiveProduct();
                BeanUtils.copyProperties(memberActiveProductDto,memberActiveProduct);
                memberActiveProduct.setCreateUserName(curUserDto.getNikeName());
                memberActiveProduct.setCreateUserId(curUserDto.getUserId());
                this.memberActiveProductService.save(memberActiveProduct);
            }
        }


    }

    @Override
    public MemberActiveSettingVo getMemberActiveSetting() {
        MemberActiveSettingVo memberActiveSettingVo = new MemberActiveSettingVo();

        LambdaQueryWrapper<MemberActiveSetting>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberActiveSetting::getDeleted, CommonConstants.NUMBER_ZERO);
        MemberActiveSetting memberActiveSetting = this.getOne(wrapper);
        if(memberActiveSetting!=null){
            BeanUtils.copyProperties(memberActiveSetting,memberActiveSettingVo);
            List<MemberActiveProductVo>productList = this.memberActiveProductService.getList();
            memberActiveSettingVo.setProductList(productList);
        }

        return memberActiveSettingVo;


    }
}
