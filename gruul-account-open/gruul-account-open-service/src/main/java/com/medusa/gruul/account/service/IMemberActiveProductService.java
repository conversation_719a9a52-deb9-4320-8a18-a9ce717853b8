package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MemberActiveProduct;
import com.medusa.gruul.account.model.vo.MemberActiveProductVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:17 2025/6/3
 */
public interface IMemberActiveProductService extends IService<MemberActiveProduct> {
    /**
     * 获取激活会员商品列表
     * @return
     */
    List<MemberActiveProductVo> getList();
}
