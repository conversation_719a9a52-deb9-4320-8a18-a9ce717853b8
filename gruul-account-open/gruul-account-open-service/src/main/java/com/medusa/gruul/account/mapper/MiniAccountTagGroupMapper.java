package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.account.api.entity.MiniAccountTagGroup;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 用户所属分组表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-18
 */
@Repository
public interface MiniAccountTagGroupMapper extends BaseMapper<MiniAccountTagGroup> {
    /**
     * 根据标签id获取用户id
     * @param tagIds
     * @return
     */
    List<String> getUserIdByTagIds(@Param(value = "tagIds")  List<String> tagIds);

    /**
     * 根据标签id获取标签下的客户
     * @param tagId
     * @return
     */
    Integer getTagAccountNum(@Param(value = "tagId") Long tagId);
}
