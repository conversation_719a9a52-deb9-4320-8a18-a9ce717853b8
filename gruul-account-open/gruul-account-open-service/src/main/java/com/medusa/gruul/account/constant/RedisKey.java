package com.medusa.gruul.account.constant;

/**
 * redis key 变量类
 * <AUTHOR>
 */
public class RedisKey {

    /**
     *  存储用户通行票商家可用数量key
     * @param userId 用户id
     * @param miniAccountPassTicketId 用户购买通行票记录id
     * @param shopId 商家id
     * @return
     */
    public static String getUserPassTicketNumKey(String userId, String miniAccountPassTicketId, String shopId){
        String userKey = RedisConstant.PASS_TICKET_KEY.concat(userId).concat(":").concat(miniAccountPassTicketId).concat(":shopTimes:").concat(shopId);
        return userKey;
    }

    /**
     * 存储用户权益包商品商家可用数量key
     * @param userId
     * @param miniAccountPackageGoodsId
     * @param shopId
     * @return
     */
    public static String getUserPassPackageGoodsNumKey(String userId, String miniAccountPackageGoodsId, String shopId){
        String userKey = RedisConstant.PACKAGE_GOODS_KEY.concat(userId).concat(":").concat(miniAccountPackageGoodsId).concat(":shopTimes:").concat(shopId);
        return userKey;
    }


    /**
     *  存储用户通行票验证码key
     * @param userId 用户id
     * @param miniAccountPassTicketId 用户购买通行票记录id
     * @return
     */
    public static String getAccountPassTicketKey(String userId, String miniAccountPassTicketId){
        String userKey = RedisConstant.PASS_TICKET_KEY.concat(userId).concat(":").concat(miniAccountPassTicketId);
        return userKey;
    }

    /**
     * 存储用户优惠爱情验证码key
     * @param userId
     * @param miniAccountCouponId
     * @return
     */
    public static String getAccountCouponKey(String userId, String miniAccountCouponId){
        String userKey = RedisConstant.COUPON_KEY.concat(userId).concat(":").concat(miniAccountCouponId);
        return userKey;
    }
    /**
     * 存储用户权益包商品验证码key
     * @param userId
     * @param miniAccountPackageGoodsId
     * @return
     */
    public static String getAccountPackageGoodsKey(String userId, String miniAccountPackageGoodsId){
        String userKey = RedisConstant.PACKAGE_GOODS_KEY.concat(userId).concat(":").concat(miniAccountPackageGoodsId);
        return userKey;
    }

    /**
     *  存储通行票验证码key
     * @param code 验证码
     * @return
     */
    public static String getPassTicketCodeKey(String code){
        String codeKey = RedisConstant.PASS_TICKET_KEY.concat("code").concat(":").concat(code);
        return codeKey;
    }

    /**
     * 存储优惠券验证码key
     * @param code
     * @return
     */
    public static String getCouponCodeKey(String code){
        String codeKey = RedisConstant.COUPON_KEY.concat("code").concat(":").concat(code);
        return codeKey;
    }
    /**
     * 存储权益包商品验证码key
     * @param code
     * @return
     */
    public static String getPackageGoodsCodeKey(String code){
        String codeKey = RedisConstant.PACKAGE_GOODS_KEY.concat("code").concat(":").concat(code);
        return codeKey;
    }
}
