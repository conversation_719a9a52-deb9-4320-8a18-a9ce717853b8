package com.medusa.gruul.account.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MemberLevel;
import com.medusa.gruul.account.api.entity.MemberType;
import com.medusa.gruul.account.mapper.MemberLevelMapper;
import com.medusa.gruul.account.mapper.MemberTypeMapper;
import com.medusa.gruul.account.model.dto.AddOrUpdateMemberTypeDto;
import com.medusa.gruul.account.model.dto.MemberTypeDto;
import com.medusa.gruul.account.model.param.MemberTypeParam;
import com.medusa.gruul.account.model.vo.MemberTypeVo;
import com.medusa.gruul.account.service.IMemberTypeService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.DefaultTypeEnum;
import com.medusa.gruul.common.core.constant.enums.MemberTypeStatusEnum;
import com.medusa.gruul.common.core.constant.enums.RegionFlagEnum;
import com.medusa.gruul.common.core.constant.enums.RegionTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.dto.CurUserDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:42 2025/5/20
 */
@Service
public class MemberTypeServiceImpl extends ServiceImpl<MemberTypeMapper, MemberType>implements IMemberTypeService {

    @Autowired
    private MemberLevelMapper memberLevelMapper;

    /**
     * 新增/修改会员类型
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdateMemberType(AddOrUpdateMemberTypeDto dto) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        String userId = curUserDto.getUserId();
        String userName = curUserDto.getNikeName();
        List<MemberTypeDto> list = dto.getList();
        if(list!=null&&list.size()>0){
            for (MemberTypeDto memberTypeDto : list) {
                String id = memberTypeDto.getId();
                String name = memberTypeDto.getName();
                Integer regionFlag = memberTypeDto.getRegionFlag();
                Integer togetherFlag = memberTypeDto.getTogetherFlag();
                if(StringUtils.isEmpty(name)){
                    throw new ServiceException("会员类型名称不能为空！");
                }
                if(regionFlag == null || regionFlag.equals("")){
                    throw new ServiceException("区域类型标识不能为空！");
                }
                if(togetherFlag == null || togetherFlag.equals("")){
                    throw new ServiceException("并存类型标识不能为空！");
                }

                if(StringUtils.isNotEmpty(id)){//编辑
                    //判断记录是否存在
                    MemberType memberType = this.getById(id);
                    if(memberType == null || memberType.equals("")){
                        throw new ServiceException("会员类型不存在！");
                    }
                    //判断会员类型名称是否存在
                    if(StringUtils.isNotEmpty(name)){
                        LambdaQueryWrapper<MemberType>wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(MemberType::getName,name);
                        wrapper.ne(MemberType::getId,id);
                        int count = this.count(wrapper);
                        if(count>0){
                            throw new ServiceException("会员类型名称已存在，不能重复！");
                        }
                    }
                    //如果区域类型标识为是，需要判断是否存在启用并且区域类似标识为是的数据
                    if(regionFlag == RegionFlagEnum.YES.getStatus()){
                        LambdaQueryWrapper<MemberType>wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(MemberType::getStatus,MemberTypeStatusEnum.YES.getStatus());
                        wrapper.eq(MemberType::getRegionFlag,RegionFlagEnum.YES.getStatus());
                        wrapper.ne(MemberType::getId,id);
                        int count = this.count(wrapper);
                        if(count>0){
                            throw new ServiceException("已选择其他区域会员类型，请先将其他选择改为否或者停用该条记录！");
                        }

                        LambdaQueryWrapper<MemberLevel>queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(MemberLevel::getMemberTypeId,id);
                        queryWrapper.eq(MemberLevel::getDisable, CommonConstants.NUMBER_ZERO);
                        List<MemberLevel> memberLevelList = memberLevelMapper.selectList(queryWrapper);
                        if(memberLevelList!=null&&memberLevelList.size()>0){
                            List<MemberLevel> list1 = memberLevelList.stream().filter(e -> e.getRegionType() == RegionTypeEnum.DISTRICT.getStatus())
                                    .collect(Collectors.toList());
                            if(list1.size()>1){
                                throw new ServiceException("区域类型标识为是的会员类型，其区域类型为区/县，状态为启用的会员等级只能有一个");
                            }
                            List<MemberLevel> list2 = memberLevelList.stream().filter(e -> e.getRegionType() == RegionTypeEnum.CITY.getStatus())
                                    .collect(Collectors.toList());
                            if(list2.size()>1){
                                throw new ServiceException("区域类型标识为是的会员类型，其区域类型为市级，状态为启用的会员等级只能有一个");
                            }
                            List<MemberLevel> list3 = memberLevelList.stream().filter(e -> e.getRegionType() == RegionTypeEnum.PROVINCE.getStatus())
                                    .collect(Collectors.toList());
                            if(list3.size()>1){
                                throw new ServiceException("区域类型标识为是的会员类型，其区域类型为省级，状态为启用的会员等级只能有一个");
                            }
                        }

                    }
                    BeanUtils.copyProperties(memberTypeDto,memberType);

                    memberType.setLastModifyUserId(userId);
                    memberType.setLastModifyUserName(userName);
                    this.updateById(memberType);

                }else{//新增
                    //判断会员类型名称是否存在
                    if(StringUtils.isNotEmpty(name)){
                        LambdaQueryWrapper<MemberType>wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(MemberType::getName,name);
                        int count = this.count(wrapper);
                        if(count>0){
                            throw new ServiceException("会员类型名称已存在，不能重复！");
                        }
                    }
                    //如果区域类型标识为是，需要判断是否存在启用并且区域类似标识为是的数据
                    if(regionFlag == RegionFlagEnum.YES.getStatus()){
                        LambdaQueryWrapper<MemberType>wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(MemberType::getStatus,MemberTypeStatusEnum.YES.getStatus());
                        wrapper.eq(MemberType::getRegionFlag,RegionFlagEnum.YES.getStatus());
                        int count = this.count(wrapper);
                        if(count>0){
                            throw new ServiceException("已选择其他区域会员类型，请先将其他选择改为否或者停用该条记录！");
                        }
                    }
                    MemberType memberType = new MemberType();
                    BeanUtils.copyProperties(memberTypeDto,memberType);
                    //新增默认会员类型为否
                    memberType.setDefaultType(DefaultTypeEnum.NO.getStatus());
                    //新增会员类型状态为启用
                    memberType.setStatus(MemberTypeStatusEnum.YES.getStatus());

                    memberType.setCreateUserId(userId);
                    memberType.setCreateUserName(userName);
                    this.save(memberType);

                }
            }
        }else{
            throw new ServiceException("提交数据不能为空");
        }

    }

    /**
     * 删除会员类型
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMemberType(MemberTypeDto dto) {
        String id = dto.getId();
        if(StringUtils.isNotEmpty(id)){
            LambdaQueryWrapper<MemberLevel>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MemberLevel::getMemberTypeId,id);
            Integer count = memberLevelMapper.selectCount(wrapper);
            if(count>0){
                throw new ServiceException("已在会员卡中使用，无法删除！请先删除相应的会员卡记录");
            }
            this.removeById(id);
        }
    }

    /**
     * 获取会员类型列表
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MemberTypeVo> getMemberType(MemberTypeParam param) {
        List<MemberTypeVo> list = this.baseMapper.getMemberType(param);
        return  list;
    }

    /**
     * 启用/停用会员类型
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateStatus(MemberTypeDto dto) {

        String id = dto.getId();
        Integer status = dto.getStatus();
        String result = "";
        if(StringUtils.isEmpty(id)){
            throw new ServiceException("会员类型id不能为空");
        }
        if(status == null || status.equals("")){
            throw new ServiceException("会员类型状态不能为空");
        }
        if(status!=MemberTypeStatusEnum.NO.getStatus()&&status!=MemberTypeStatusEnum.YES.getStatus()){
            throw new ServiceException("会员类型状态必须为0-停用或者1-启用");
        }
        MemberType memberType = this.getById(id);
        if(memberType!=null&&!memberType.equals("")){

            Integer regionFlag = memberType.getRegionFlag();

            //判断如果是启用会员类型并且当前会员类型区域类型为是时候，是否存在启用并且区域类型为是的会员类型
            if(regionFlag == RegionFlagEnum.YES.getStatus()
                    &&status == MemberTypeStatusEnum.YES.getStatus()){
                LambdaQueryWrapper<MemberType>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(MemberType::getStatus,MemberTypeStatusEnum.YES.getStatus());
                wrapper.eq(MemberType::getRegionFlag,RegionFlagEnum.YES.getStatus());
                wrapper.ne(MemberType::getId,id);
                int count = this.count(wrapper);
                if(count>0){
                    throw new ServiceException("已选择其他区域会员类型，请先将其他选择改为否或者停用该条记录！");
                }
            }
            memberType.setStatus(status);
            this.updateById(memberType);
            if(status == MemberTypeStatusEnum.NO.getStatus()){
                result = "停用成功";
            }
            if(status == MemberTypeStatusEnum.YES.getStatus()){
                result = "启用成功";
            }
        }else{
            throw new ServiceException("会员类型不存在");
        }

        return result;
    }

    /**
     * 设置为默认会员类型
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefaultType(MemberTypeDto dto) {
        String id = dto.getId();
        if(StringUtils.isEmpty(id)){
            throw new ServiceException("会员类型id不能为空");
        }
        //其他记录默认会员类型改为否
        LambdaUpdateWrapper<MemberType>wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(MemberType::getDefaultType,DefaultTypeEnum.NO.getStatus());
        wrapper.ne(MemberType::getId,id);
        update(wrapper);
        //将该会员类型记录设置为默认
        LambdaUpdateWrapper<MemberType>wrapper1 = new LambdaUpdateWrapper<>();
        wrapper1.set(MemberType::getDefaultType,DefaultTypeEnum.YES.getStatus());
        wrapper1.eq(MemberType::getId,id);
        update(wrapper1);

    }

    @Override
    public List<MemberTypeVo> getMemberTypeList(String userId) {
        return this.baseMapper.getMemberTypeList(userId);
    }

    @Override
    public List<MemberType> getMemberTypeNoRegionFlagList() {

        LambdaQueryWrapper<MemberType>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberType::getDeleted,CommonConstants.NUMBER_ZERO);
        wrapper.eq(MemberType::getRegionFlag,RegionFlagEnum.NO.getStatus());
        wrapper.eq(MemberType::getStatus,MemberTypeStatusEnum.YES.getStatus());
        List<MemberType> list = this.list(wrapper);

        return list;
    }

    @Override
    public MemberType getRegionMemberType() {

        LambdaQueryWrapper<MemberType>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberType::getRegionFlag,RegionFlagEnum.YES.getStatus());
        wrapper.eq(MemberType::getDeleted,CommonConstants.NUMBER_ZERO);
        MemberType memberType = this.baseMapper.selectOne(wrapper);
        return memberType;
    }

    @Override
    public MemberType getDefaultMemberType() {
        LambdaQueryWrapper<MemberType>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberType::getDefaultType,DefaultTypeEnum.YES.getStatus());
        wrapper.eq(MemberType::getDeleted,CommonConstants.NUMBER_ZERO);
        MemberType memberType = this.baseMapper.selectOne(wrapper);
        return memberType;
    }
}
