package com.medusa.gruul.account.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.entity.MiniAccountCoupon;
import com.medusa.gruul.account.api.entity.MiniAccountCouponCode;

import com.medusa.gruul.account.api.model.param.MiniAccountCouponCodeParam;
import com.medusa.gruul.account.api.model.vo.ShopCouponCodeVo;
import com.medusa.gruul.account.api.model.vo.ShopsCouponCodeExcelVo;
import com.medusa.gruul.account.conf.AccountRedis;
import com.medusa.gruul.account.constant.RedisKey;
import com.medusa.gruul.account.mapper.MiniAccountCouponCodeMapper;
import com.medusa.gruul.account.model.dto.MiniAccountCouponCodeDto;
import com.medusa.gruul.account.model.vo.MiniAccountCouponCodeVo;
import com.medusa.gruul.account.service.IMiniAccountCouponCodeService;
import com.medusa.gruul.account.service.IMiniAccountCouponService;
import com.medusa.gruul.account.service.IMiniAccountService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.HuToolExcelUtils;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.api.enums.OrderStatusEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;

import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.enums.PromotionStatusEnum;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import com.medusa.gruul.shops.api.model.AccountCouponVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MiniAccountCouponCodeServiceImpl extends ServiceImpl<MiniAccountCouponCodeMapper, MiniAccountCouponCode> implements IMiniAccountCouponCodeService {

    @Autowired
    private IMiniAccountCouponService miniAccountCouponService;
    @Autowired
    private RemoteShopsService remoteShopsService;
    @Autowired
    private RemoteOrderService remoteOrderService;
    @Autowired
    private IMiniAccountService miniAccountService;
    /**
     * 添加记录，生成验证码
     * @param miniAccountCouponCodeDto
     * @return
     */
    @Override
    public MiniAccountCouponCode add(MiniAccountCouponCodeDto miniAccountCouponCodeDto) {
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        Long miniAccountCouponId = miniAccountCouponCodeDto.getMiniAccountCouponId();
        if(miniAccountCouponId == null){
            throw new ServiceException("用户优惠券id不能为空！");
        }
        MiniAccountCoupon miniAccountCoupon = miniAccountCouponService.getById(miniAccountCouponId);
        if(miniAccountCoupon == null){
            throw new ServiceException("用户优惠券id不存在！");
        }
        if(!userId.equals(miniAccountCoupon.getUserId()+"")){
            throw new ServiceException("优惠券不属于当前用户！");
        }
        if(miniAccountCoupon.getStatus() == PromotionStatusEnum.EXPIRED.getStatus().intValue()){
            throw new ServiceException("优惠券已失效！");
        }
        if(miniAccountCoupon.getStatus() == PromotionStatusEnum.USED.getStatus().intValue()){
            throw new ServiceException("优惠券已使用！");
        }
        Date endTime = miniAccountCoupon.getEndTime();
        Date nowDate = new Date();
        if(nowDate.getTime()>endTime.getTime()){
            throw new ServiceException("优惠券已过期");
        }
        String verifyCode = RandomUtil.randomNumbers(10);
        // 查询redis是否已存有此用户通行票记录的验证码
        String userKey = RedisKey.getAccountCouponKey(userId,miniAccountCoupon.getId() + "");
        AccountRedis accountRedis = new AccountRedis();
        // 过期毫秒数
        long time = DateUtil.between(new Date(), miniAccountCoupon.getEndTime(), DateUnit.MS);
        MiniAccountCouponCode couponCode = new MiniAccountCouponCode();
        BeanUtil.copyProperties(miniAccountCouponCodeDto, couponCode);
        couponCode.setUserId(Long.valueOf(userId));
        couponCode.setCouponId(miniAccountCoupon.getCouponId());
        couponCode.setStartTime(miniAccountCoupon.getStartTime());
        couponCode.setEndTime(miniAccountCoupon.getEndTime());
        couponCode.setStatus(PromotionStatusEnum.UN_USE.getStatus());

        // 设置值成功返回“OK”
        String result = accountRedis.setNxPx(userKey, verifyCode, time);
        if(!CommonConstants.REDIS_SUCCESS.equalsIgnoreCase(result)){
            // 不成功，表示redis已经有值，无需重新生成
            verifyCode = accountRedis.get(userKey);
            couponCode.setVerifyCode(verifyCode);
            return couponCode;
        }
        couponCode.setVerifyCode(verifyCode);
        this.save(couponCode);
        return couponCode;
    }

    /**
     * 获取优惠券验证码
     * @param miniAccountPassTicketCodeDto
     * @return
     */
    @Override
    public MiniAccountCouponCodeVo getCode(MiniAccountCouponCodeDto miniAccountPassTicketCodeDto) {
        MiniAccountCouponCodeVo codeVo = new MiniAccountCouponCodeVo();
        MiniAccountCouponCode code = this.add(miniAccountPassTicketCodeDto);
        BeanUtil.copyProperties(code, codeVo);
        QrConfig qrConfig = QrConfig.create();
        byte[] bytes = QrCodeUtil.generatePng(codeVo.getVerifyCode(), qrConfig);
        String qrCode = Base64.encode(bytes);
        codeVo.setQrCode(qrCode);
        return codeVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MiniAccountCouponCode verifyCode(String verifyCode) {
        CurUserDto user = CurUserUtil.getHttpCurUser();
        String verifyUserId = user.getUserId();
        String shopId = user.getShopId();
        ShopsPartner shopsPartner = remoteShopsService.getByShopId(Long.valueOf(shopId));
        if(StrUtil.isBlank(shopId)){
            throw new ServiceException("此账号无法核销", SystemCode.DATA_NOT_EXIST.getCode());
        }

        MiniAccountCouponCode couponCode = null;
        String codeKey = RedisKey.getCouponCodeKey(verifyCode);
        AccountRedis accountRedis = new AccountRedis();
        String status = accountRedis.get(codeKey);
        if(StrUtil.isNotBlank(status)){
            throw new ServiceException("此码已核销，不能重复核销", SystemCode.DATA_EXPIRED.getCode());
        }else{
            //未使用，则进行核销
            LambdaQueryWrapper<MiniAccountCouponCode> codeWrapper = new LambdaQueryWrapper<>();
            codeWrapper.eq(MiniAccountCouponCode::getVerifyCode, verifyCode).eq(MiniAccountCouponCode::getStatus, PromotionStatusEnum.UN_USE.getStatus());
            List<MiniAccountCouponCode> codeList = this.list(codeWrapper);
            if(CollectionUtil.isEmpty(codeList)){
                throw new ServiceException("该核销码不存在，无法核销", SystemCode.DATA_MANY.getCode());
            }
            if(CollectionUtil.isNotEmpty(codeList) && codeList.size() != 1){
                throw new ServiceException("此码暂不能核销，请联系运营平台管理员", SystemCode.DATA_MANY.getCode());
            }
            couponCode = codeList.get(0);
            if(DateUtil.compare(new Date(), couponCode.getEndTime()) > 0){
                throw new ServiceException("此码已过期，无法核销", SystemCode.DATA_EXPIRED.getCode());
            }
            MiniAccountCoupon miniAccountCoupon = this.miniAccountCouponService.getById(couponCode.getMiniAccountCouponId());
            AccountCouponVo accountCouponVo = remoteShopsService.getCouponById(miniAccountCoupon.getCouponId());
            // 判断商家能否使用
            if(accountCouponVo.getShopFlag()){
                //查询可用商家
                List<ShopsPartner> shopsPartnerList = this.remoteShopsService.getShopPartnerByCouponId(miniAccountCoupon.getCouponId());
                if(CollectionUtil.isEmpty(shopsPartnerList)){
                    throw new ServiceException("商家无法核销该核销码！", SystemCode.DATA_NOT_EXIST.getCode());
                }
                List<String> shopIdList = shopsPartnerList.stream().map(ShopsPartner::getShopId).collect(Collectors.toList());
                if(CollectionUtil.isEmpty(shopIdList) || !shopIdList.contains(shopId)){
                    throw new ServiceException("商家无法核销该核销码！", SystemCode.DATA_NOT_EXIST.getCode());
                }
            }

            couponCode.setStatus(PromotionStatusEnum.USED.getStatus());
            couponCode.setVerifyTime(new Date());
            couponCode.setVerifyUserId(Long.parseLong(verifyUserId));
            couponCode.setShopId(shopId);
            this.updateById(couponCode);
            // 用户优惠券记录改为已使用
            miniAccountCoupon.setStatus(PromotionStatusEnum.USED.getStatus());
            Long userId = miniAccountCoupon.getUserId();
            MiniAccount miniAccount = miniAccountService.getByShopUserId(userId+"");
            miniAccountCoupon.setUseName(miniAccount.getNikeName());
            miniAccountCoupon.setUseTime(new Date());
            miniAccountCoupon.setUseShopName(shopsPartner.getName());
            this.miniAccountCouponService.updateById(miniAccountCoupon);

            // 订单状态如果是待评价状态，则改为已完成
            Long orderId = miniAccountCoupon.getOrderId();
            Order order = remoteOrderService.getOrderById(orderId);
            if(null != order && order.getStatus() == OrderStatusEnum.WAIT_FOR_COMMENT){
                List<Long> orderIds = new ArrayList<>();
                orderIds.add(orderId);
                remoteOrderService.updateOrderStatus(orderIds, OrderStatusEnum.COMPLETE);
            }
            // 过期毫秒数
            long time = DateUtil.between(new Date(), miniAccountCoupon.getEndTime(), DateUnit.MS);
            // 设置redis记录为已使用
            String result = accountRedis.setNxPx(codeKey, PromotionStatusEnum.USED.getStatus().toString(), time);
            if(!CommonConstants.REDIS_SUCCESS.equalsIgnoreCase(result)){
                // 失败
                throw new ServiceException("核销失败，请重试", SystemCode.DATA_EXPIRED.getCode());
            }
            String accountCouponKey = RedisKey.getAccountCouponKey(miniAccountCoupon.getUserId() + "", miniAccountCoupon.getId() + "");
            accountRedis.del(accountCouponKey);
        }
        return couponCode;
    }

    @Override
    public IPage<ShopCouponCodeVo> pageShopUserVerifyCode(MiniAccountCouponCodeParam param) {
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        param.setVerifyUserId(userId);
        IPage<ShopCouponCodeVo> page = null;
        if(null != param.getStartTime()){
            String startTimeStr = DateUtil.format(param.getStartTime(), "yyyy-MM-dd 00:00:00");
            param.setStartTime(DateUtil.parse(startTimeStr));
        }
        if(null != param.getEndTime()){
            String endTimeStr = DateUtil.format(param.getEndTime(), "yyyy-MM-dd 23:59:59");
            param.setEndTime(DateUtil.parse(endTimeStr));
        }
        page = this.baseMapper.selectShopUserVerifyList(new Page<>(param.getCurrent(), param.getSize()), param);
        return page;
    }

    @Override
    public IPage<ShopCouponCodeVo> pageShopVerifyCode(MiniAccountCouponCodeParam miniAccountCouponCodeParam) {
        IPage<ShopCouponCodeVo> page = null;
        String sourceShopId = ShopContextHolder.getShopId();

        ShopsPartner shopsPartner = remoteShopsService.getShopsPartnerMain();
        String paramShopId = miniAccountCouponCodeParam.getShopId();

        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        if(null != shopsPartner && shopsPartner.getShopId().equals(paramShopId)){
            // 主店铺登录用户，查出所有数据
            miniAccountCouponCodeParam.setShopId(null);
        }
        if(null != miniAccountCouponCodeParam.getStartTime()){
            String startTimeStr = DateUtil.format(miniAccountCouponCodeParam.getStartTime(), "yyyy-MM-dd 00:00:00");
            miniAccountCouponCodeParam.setStartTime(DateUtil.parse(startTimeStr));
        }
        if(null != miniAccountCouponCodeParam.getEndTime()){
            String endTimeStr = DateUtil.format(miniAccountCouponCodeParam.getEndTime(), "yyyy-MM-dd 23:59:59");
            miniAccountCouponCodeParam.setEndTime(DateUtil.parse(endTimeStr));
        }

        page = this.baseMapper.selectShopVerifyList(new Page<>(miniAccountCouponCodeParam.getCurrent(), miniAccountCouponCodeParam.getSize()),
                miniAccountCouponCodeParam);
        ShopContextHolder.setShopId(sourceShopId);
        return page;
    }

    @Override
    public void exportShopCouponCode(MiniAccountCouponCodeParam param) {
        IPage<ShopsCouponCodeExcelVo> page = null;

        // 手动设置shopId成默认值，如此就可以不走多店铺的逻辑，这样查出来的数据才正确
        ShopsPartner shopsPartner = remoteShopsService.getShopsPartnerMain();
        String sourceShopId = ShopContextHolder.getShopId();

        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        if(null != shopsPartner && !shopsPartner.getShopId().equals(sourceShopId)){
            // 非主店铺登录用户
            param.setShopId(sourceShopId);
        }
        if(null != param.getStartTime()){
            String startTimeStr = DateUtil.format(param.getStartTime(), "yyyy-MM-dd 00:00:00");
            param.setStartTime(DateUtil.parse(startTimeStr));
        }
        if(null != param.getEndTime()){
            String endTimeStr = DateUtil.format(param.getEndTime(), "yyyy-MM-dd 23:59:59");
            param.setEndTime(DateUtil.parse(endTimeStr));
        }

        param.setCurrent(1);
        // 设置导出最大数量
        param.setSize(CommonConstants.MAX_EXPORT_SIZE);
        page = this.baseMapper.selectShopVerifyExcelList(new Page<>(param.getCurrent(), param.getSize()),
                param);
        ShopContextHolder.setShopId(sourceShopId);
        PageUtils<ShopsCouponCodeExcelVo> pageUtils =   new PageUtils<>(page);
        List<ShopsCouponCodeExcelVo> excelVoList = pageUtils.getList();
        String fileName = DateUtil.format(new Date(), "yyyyMMdd HHmmss") + RandomUtil.randomString(4) + "优惠券核销列表";
        boolean success = HuToolExcelUtils.list2xlsx(excelVoList, fileName, ShopsCouponCodeExcelVo.class);
        if(!success){
            throw new ServiceException("导出数据异常", SystemCode.FAILURE.getCode());
        }
    }

    @Override
    public AccountCouponVo getCouponByCode(String verifyCode) {
        MiniAccountCouponCode couponCode = null;
        String codeKey = RedisKey.getCouponCodeKey(verifyCode);
        AccountRedis accountRedis = new AccountRedis();
        String status = accountRedis.get(codeKey);
        if(StrUtil.isNotBlank(status)){
            throw new ServiceException("此码已核销，不能重复核销", SystemCode.DATA_EXPIRED.getCode());
        }else {
            //未使用，则进行核销
            LambdaQueryWrapper<MiniAccountCouponCode> codeWrapper = new LambdaQueryWrapper<>();
            codeWrapper.eq(MiniAccountCouponCode::getVerifyCode, verifyCode).eq(MiniAccountCouponCode::getStatus, PromotionStatusEnum.UN_USE.getStatus());
            List<MiniAccountCouponCode> codeList = this.list(codeWrapper);
            if(CollectionUtil.isEmpty(codeList)){
                throw new ServiceException("该核销码不存在，无法核销", SystemCode.DATA_MANY.getCode());
            }
            if(CollectionUtil.isNotEmpty(codeList) && codeList.size() != 1){
                throw new ServiceException("此码暂不能核销，请联系运营平台管理员", SystemCode.DATA_MANY.getCode());
            }
            couponCode = codeList.get(0);
            if (DateUtil.compare(new Date(), couponCode.getEndTime()) > 0) {
                throw new ServiceException("此码已过期，无法核销", SystemCode.DATA_EXPIRED.getCode());
            }
        }
        AccountCouponVo accountCouponVo = remoteShopsService.getCouponById(couponCode.getCouponId());
        return accountCouponVo;
    }


}
