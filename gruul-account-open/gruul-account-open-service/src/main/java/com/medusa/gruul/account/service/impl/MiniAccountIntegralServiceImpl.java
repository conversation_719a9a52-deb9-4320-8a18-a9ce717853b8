package com.medusa.gruul.account.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.entity.MiniAccountExtends;
import com.medusa.gruul.account.api.entity.MiniAccountIntegral;
import com.medusa.gruul.account.api.enums.MiniAccountIntegralSourceEnum;
import com.medusa.gruul.account.mapper.MiniAccountIntegralMapper;
import com.medusa.gruul.account.model.dto.BatchMiniAccountIntegralDto;
import com.medusa.gruul.account.model.dto.MiniAccountIntegralDto;
import com.medusa.gruul.account.model.param.IntegralRankingParam;
import com.medusa.gruul.account.model.param.MiniAccountIntegralManageParam;
import com.medusa.gruul.account.model.param.MiniAccountIntegralParam;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.service.IMiniAccountExtendsService;
import com.medusa.gruul.account.service.IMiniAccountIntegralService;
import com.medusa.gruul.account.service.IMiniAccountService;
import com.medusa.gruul.account.api.enums.MiniAccountIntegralTypeEnum;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.EnableStatusEnum;
import com.medusa.gruul.common.core.util.*;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.goods.api.enums.RuleTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.IntegralRuleVo;
import com.medusa.gruul.order.api.enums.OrderTypeEnum;
import com.medusa.gruul.order.api.model.OrderVo;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: plh
 * @Description: 用户积分服务实现类
 * @Date: Created in 15:38 2023/8/22
 */
@Service
@Log4j2
public class MiniAccountIntegralServiceImpl extends ServiceImpl<MiniAccountIntegralMapper, MiniAccountIntegral> implements IMiniAccountIntegralService {

    @Autowired
    private RemoteGoodsService remoteGoodsService;

    @Autowired
    private IMiniAccountService miniAccountService;

    @Autowired
    private IMiniAccountExtendsService miniAccountExtendsService;

    @Override
    public void addMiniAccountIntegral(MiniAccountIntegralDto miniAccountIntegralDto) {
        MiniAccountIntegral miniAccountIntegral = new MiniAccountIntegral();
        BeanUtils.copyProperties(miniAccountIntegralDto,miniAccountIntegral);
        this.baseMapper.insert(miniAccountIntegral);
    }

    @Override
    public PageUtils<AccountIntegralVo> searchAccountIntegral(MiniAccountIntegralParam miniAccountIntegralParam) {

        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        if (curUser == null) {
            throw new ServiceException("token错误", SystemCode.DATA_NOT_EXIST_CODE);
        }
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(curUser.getUserId());
        miniAccountIntegralParam.setUserId(miniAccountExtends.getUserId());
        IPage<AccountIntegralVo> accountIntegralVoIPage = this.baseMapper.selectAccountIntegral(new Page<>(miniAccountIntegralParam.getCurrent(), miniAccountIntegralParam.getSize()), miniAccountIntegralParam);
        if(accountIntegralVoIPage.getRecords()!=null&&accountIntegralVoIPage.getRecords().size()>0){
            for (AccountIntegralVo record : accountIntegralVoIPage.getRecords()) {
                for (MiniAccountIntegralTypeEnum miniAccountIntegralTypeEnum : MiniAccountIntegralTypeEnum.values()) {
                    if(miniAccountIntegralTypeEnum.getStatus() == record.getType()){
                        record.setTypeText(miniAccountIntegralTypeEnum.getDesc());
                        break;
                    }
                }
            }
        }
        return new PageUtils<>(accountIntegralVoIPage);
    }

    /**
     * 用户授权成功添加积分
     * @param miniAccount
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public  void loginAddIntegral(MiniAccount miniAccount){
        //1.自身添加积分
        this.selfAddIntegral(miniAccount);
        //2.上级，上上级添加积分
        this.parentAddIntegral(miniAccount);
    }
    /**
     * 自身添加积分
     * @param miniAccount
     */
    private void selfAddIntegral(MiniAccount miniAccount) {
        String tenantId = TenantContextHolder.getTenantId();
        IntegralRuleVo integralRuleVo = remoteGoodsService.getIntegralRuleVoByRuleType(RuleTypeEnum.NEW_USER.getSaleMode(),tenantId);
        if(integralRuleVo!=null&&integralRuleVo.getEnableStatus()!=null&&integralRuleVo.getEnableStatus()== EnableStatusEnum.YES.getStatus()){
            BigDecimal addIntegral = integralRuleVo.getIntegral();
            BigDecimal integral = miniAccount.getIntegral() == null?BigDecimal.ZERO:miniAccount.getIntegral();
            BigDecimal currentIntegral = miniAccount.getCurrentIntegral()== null?BigDecimal.ZERO:miniAccount.getCurrentIntegral();

            miniAccount.setIntegral(integral.add(addIntegral));
            miniAccount.setCurrentIntegral(currentIntegral.add(addIntegral));

            miniAccountService.updateById(miniAccount);
            MiniAccountIntegralDto miniAccountIntegralDto = new MiniAccountIntegralDto();
            miniAccountIntegralDto.setUserId(miniAccount.getUserId());
            miniAccountIntegralDto.setIntegral(addIntegral);
            miniAccountIntegralDto.setType(MiniAccountIntegralTypeEnum.REGISTER.getStatus());
            miniAccountIntegralDto.setRemark("注册获取积分");
            miniAccountIntegralDto.setSource(MiniAccountIntegralSourceEnum.SYSTEM.getStatus());
            miniAccountIntegralDto.setLastIntegral(currentIntegral);
            miniAccountIntegralDto.setTotalIntegral(miniAccount.getCurrentIntegral());

            this.addMiniAccountIntegral(miniAccountIntegralDto);
        }
    }
    @Override
    @Transactional
    public void rollback(Long orderId) {

        LambdaQueryWrapper<MiniAccountIntegral>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountIntegral::getOrderId,orderId);
        List<MiniAccountIntegral> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            for (MiniAccountIntegral miniAccountIntegral : list) {
                LambdaQueryWrapper<MiniAccount> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MiniAccount::getUserId,miniAccountIntegral.getUserId());
                MiniAccount miniAccount = miniAccountService.getOne(queryWrapper);

                BigDecimal currentIntegral = miniAccount.getCurrentIntegral() == null ? BigDecimal.ZERO : miniAccount.getCurrentIntegral();
                BigDecimal integral = miniAccount.getIntegral() == null ? BigDecimal.ZERO : miniAccount.getIntegral();

                miniAccount.setCurrentIntegral(currentIntegral.subtract(miniAccountIntegral.getIntegral()));
                miniAccount.setIntegral(integral.subtract(miniAccountIntegral.getIntegral()));
                miniAccountService.updateById(miniAccount);

                MiniAccountIntegralDto miniAccountIntegralDto = new MiniAccountIntegralDto();
                miniAccountIntegralDto.setUserId(miniAccountIntegral.getUserId());
                miniAccountIntegralDto.setIntegral(miniAccountIntegral.getIntegral().negate());
                miniAccountIntegralDto.setType(MiniAccountIntegralTypeEnum.DELETE_ORDER.getStatus());
                miniAccountIntegralDto.setRemark("订单删除");
                miniAccountIntegralDto.setLastIntegral(currentIntegral);
                miniAccountIntegralDto.setTotalIntegral(miniAccount.getCurrentIntegral());
                miniAccountIntegralDto.setSource(MiniAccountIntegralSourceEnum.SYSTEM.getStatus());

                this.addMiniAccountIntegralByOrder(miniAccountIntegralDto,TenantContextHolder.getTenantId());
            }
        }

    }

    @Override
    @Transactional
    public void batchUpdateIntegral(BatchMiniAccountIntegralDto batchMiniAccountIntegralDto) {
        List<MiniAccountIntegralDto> list = batchMiniAccountIntegralDto.getList();
        if(list!=null&&list.size()>0){
            for (MiniAccountIntegralDto miniAccountIntegralDto : list) {
                updateIntegral(miniAccountIntegralDto);
            }
        }
    }

    /**
     * 父级添加积分
     * @param miniAccount
     */
    private void parentAddIntegral(MiniAccount miniAccount){
        String tenantId = TenantContextHolder.getTenantId();
        IntegralRuleVo integralRuleVo = remoteGoodsService.getIntegralRuleVoByRuleType(RuleTypeEnum.LOGIN_USER.getSaleMode(),tenantId);
        if(integralRuleVo!=null&&integralRuleVo.getEnableStatus()!=null&&integralRuleVo.getEnableStatus()== EnableStatusEnum.YES.getStatus()){
            BigDecimal parentIntegral = integralRuleVo.getParentIntegral();//上级添加积分
            BigDecimal aboveParentIntegral = integralRuleVo.getAboveParentIntegral();
            String parentId = miniAccount.getParentId();
            if(parentId!=null&&!parentId.equals("")&&!parentId.equals("0")){
                LambdaQueryWrapper<MiniAccount>queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MiniAccount::getUserId,parentId);
                MiniAccount parentMiniAccount = miniAccountService.getOne(queryWrapper);

                BigDecimal integral = parentMiniAccount.getIntegral() == null ? BigDecimal.ZERO : parentMiniAccount.getIntegral();
                BigDecimal currentIntegral = parentMiniAccount.getCurrentIntegral() == null ? BigDecimal.ZERO : parentMiniAccount.getCurrentIntegral();

                parentMiniAccount.setIntegral(integral.add(parentIntegral));
                parentMiniAccount.setCurrentIntegral(currentIntegral.add(parentIntegral));

                miniAccountService.updateById(parentMiniAccount);
                MiniAccountIntegralDto miniAccountIntegralDto = new MiniAccountIntegralDto();
                miniAccountIntegralDto.setUserId(parentId);
                miniAccountIntegralDto.setIntegral(parentIntegral);
                miniAccountIntegralDto.setType(MiniAccountIntegralTypeEnum.RECOMMEND_JUNIOR.getStatus());
                miniAccountIntegralDto.setRemark("下级登录注册");
                miniAccountIntegralDto.setSource(MiniAccountIntegralSourceEnum.SYSTEM.getStatus());
                miniAccountIntegralDto.setLastIntegral(currentIntegral);
                miniAccountIntegralDto.setTotalIntegral(parentMiniAccount.getCurrentIntegral());
                this.addMiniAccountIntegral(miniAccountIntegralDto);

            }
            String aboveParentId = miniAccount.getAboveParentId();
            if(aboveParentId!=null&&!aboveParentId.equals("")&&!aboveParentId.equals("0")){
                LambdaQueryWrapper<MiniAccount>queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MiniAccount::getUserId,aboveParentId);
                MiniAccount aboveParentMiniAccount = miniAccountService.getOne(queryWrapper);

                BigDecimal integral = aboveParentMiniAccount.getIntegral() == null ? BigDecimal.ZERO :aboveParentMiniAccount.getIntegral();
                BigDecimal currentIntegral = aboveParentMiniAccount.getCurrentIntegral()  == null ? BigDecimal.ZERO :aboveParentMiniAccount.getCurrentIntegral();

                aboveParentMiniAccount.setIntegral(integral.add(aboveParentIntegral));
                aboveParentMiniAccount.setCurrentIntegral(currentIntegral.add(aboveParentIntegral));


                miniAccountService.updateById(aboveParentMiniAccount);
                MiniAccountIntegralDto miniAccountIntegralDto = new MiniAccountIntegralDto();
                miniAccountIntegralDto.setUserId(aboveParentId);
                miniAccountIntegralDto.setIntegral(aboveParentIntegral);
                miniAccountIntegralDto.setType(MiniAccountIntegralTypeEnum.RECOMMEND_JUNIOR.getStatus());
                miniAccountIntegralDto.setRemark("下下级登录注册");
                miniAccountIntegralDto.setSource(MiniAccountIntegralSourceEnum.SYSTEM.getStatus());
                miniAccountIntegralDto.setLastIntegral(currentIntegral);
                miniAccountIntegralDto.setTotalIntegral(aboveParentMiniAccount.getCurrentIntegral());

                this.addMiniAccountIntegral(miniAccountIntegralDto);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MiniAccount loginAddIntegralDay(MiniAccount miniAccount) {
        String tenantId = TenantContextHolder.getTenantId();
        IntegralRuleVo integralRuleVo = remoteGoodsService.getIntegralRuleVoByRuleType(RuleTypeEnum.LOGIN_DAY.getSaleMode(),tenantId);
        if(integralRuleVo!=null&&integralRuleVo.getEnableStatus()!=null&&integralRuleVo.getEnableStatus()== EnableStatusEnum.YES.getStatus()){
            BigDecimal addIntegral = integralRuleVo.getIntegral();//添加的积分
            BigDecimal integral = miniAccount.getIntegral()==null? BigDecimal.ZERO:miniAccount.getIntegral();//用户总积分
            BigDecimal currentIntegral = miniAccount.getCurrentIntegral()==null? BigDecimal.ZERO:miniAccount.getCurrentIntegral();//用户可用积分

            miniAccount.setIntegral(integral.add(addIntegral));
            miniAccount.setCurrentIntegral(currentIntegral.add(addIntegral));

            miniAccountService.updateById(miniAccount);
            MiniAccountIntegralDto miniAccountIntegralDto = new MiniAccountIntegralDto();
            miniAccountIntegralDto.setUserId(miniAccount.getUserId());
            miniAccountIntegralDto.setIntegral(addIntegral);
            miniAccountIntegralDto.setType(MiniAccountIntegralTypeEnum.LOGIN.getStatus());
            String date = LocalDateTimeUtils.formatTime(LocalDateTime.now(), "yyyy-MM-dd");
            miniAccountIntegralDto.setRemark(date+"登录获取积分");
            miniAccountIntegralDto.setSource(MiniAccountIntegralSourceEnum.SYSTEM.getStatus());
            miniAccountIntegralDto.setLastIntegral(currentIntegral);
            miniAccountIntegralDto.setTotalIntegral(miniAccount.getCurrentIntegral());

            this.addMiniAccountIntegral(miniAccountIntegralDto);
        }
        return miniAccount;
    }

    @Override
    @Transactional
    public void handleIntegral(OrderVo orderVo) {
        log.info("积分订单信息：{}", JSONObject.toJSONString(orderVo));
        //获取用户信息
        String shopUserId = orderVo.getUserId();
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(shopUserId);
        if(null == miniAccountExtends){
            log.info("用户不存在：" , shopUserId);
            return;
        }
        LambdaQueryWrapper<MiniAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MiniAccount::getUserId,miniAccountExtends.getUserId());
        MiniAccount miniAccount = miniAccountService.getOne(queryWrapper);
        log.info("订单类型："+orderVo.getType().getDesc());
        if(orderVo.getType().getCode()!= OrderTypeEnum.INTEGRAL.getCode()){
            if(orderVo.getType().getCode()== OrderTypeEnum.TICKET.getCode()){//添加通惠证订单积分
                Integer type = RuleTypeEnum.PAY_TICKET.getSaleMode();
                addOrderIntegral(orderVo,miniAccount,type);
            }else{//添加普通订单积分
                Integer type = RuleTypeEnum.PAY_ORDER.getSaleMode();
                addOrderIntegral(orderVo,miniAccount,type);
            }
            //上级添加订单销售积分
            Integer type = RuleTypeEnum.JUNIOR_PAY_ORDER.getSaleMode();
            String tenantId = orderVo.getTenantId();
            IntegralRuleVo integralRuleVo = remoteGoodsService.getIntegralRuleVoByRuleType(type,tenantId);
            log.info("获取下级下单获积分规则",integralRuleVo);
            if(integralRuleVo!=null&&integralRuleVo.getEnableStatus()!=null&&integralRuleVo.getEnableStatus()==EnableStatusEnum.YES.getStatus()
                    &&integralRuleVo.getIntegral().compareTo(BigDecimal.ZERO)>0){
                log.info("开始递归添加用户上级积分....");
                addJuniorSalesIntegral(orderVo,miniAccount,integralRuleVo);
            }

        }

    }

    /**
     * 递归-判断用户是否有上级，有上级添加订单销售积分
     * @param orderVo
     * @param miniAccount
     * @param integralRuleVo
     */
    private void addJuniorSalesIntegral(OrderVo orderVo, MiniAccount miniAccount, IntegralRuleVo integralRuleVo) {
        String parentId = miniAccount.getParentId();
        String nikeName = miniAccount.getNikeName();
        String tenantId = orderVo.getTenantId();
        LambdaQueryWrapper<MiniAccount>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccount::getUserId,parentId);
        List<MiniAccount> list = miniAccountService.list(wrapper);
        if(list!=null&&list.size()>0){
            MiniAccount parentMiniAccount = list.get(0);
            log.info(parentMiniAccount.getNikeName()+"添加销售积分.......");
            //获取添加的积分
            BigDecimal integral = integralRuleVo.getIntegral();
            log.info("添加的积分",integral);
            BigDecimal allIntegral = parentMiniAccount.getIntegral() ==null?BigDecimal.ZERO:parentMiniAccount.getIntegral();
            BigDecimal currentIntegral = parentMiniAccount.getCurrentIntegral()==null?BigDecimal.ZERO:parentMiniAccount.getCurrentIntegral();
            parentMiniAccount.setIntegral(allIntegral.add(integral));
            parentMiniAccount.setCurrentIntegral(currentIntegral.add(integral));
            log.info("开始更新上级用户积分信息",parentMiniAccount);
            miniAccountService.updateById(parentMiniAccount);
            MiniAccountIntegralDto miniAccountIntegralDto = new MiniAccountIntegralDto();
            miniAccountIntegralDto.setUserId(parentMiniAccount.getUserId());
            miniAccountIntegralDto.setIntegral(integral);
            miniAccountIntegralDto.setOrderId(orderVo.getId()+"");
            miniAccountIntegralDto.setType(MiniAccountIntegralTypeEnum.JUNIOR_BUY.getStatus());
            miniAccountIntegralDto.setRemark("下级下单获积分");

            miniAccountIntegralDto.setLastIntegral(currentIntegral);
            miniAccountIntegralDto.setTotalIntegral(parentMiniAccount.getCurrentIntegral());
            miniAccountIntegralDto.setSource(MiniAccountIntegralSourceEnum.SYSTEM.getStatus());
            log.info("开始更新上级用户积分明细",miniAccountIntegralDto);
            this.addMiniAccountIntegralByOrder(miniAccountIntegralDto,tenantId);

            addJuniorSalesIntegral(orderVo,parentMiniAccount,integralRuleVo);
        }else{
            log.info("用户【"+nikeName+"】没有上级客户，结束");
        }

    }

    @Override
    public void deductionIntegral(OrderVo orderVo) {
        log.info("积分订单信息：{}", JSONObject.toJSONString(orderVo));
        if(orderVo.getType().getCode()== OrderTypeEnum.INTEGRAL.getCode()){//减少积分兑换订单积分
            //获取用户信息
            String shopUserId = orderVo.getUserId();
            MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(shopUserId);
            LambdaQueryWrapper<MiniAccount> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MiniAccount::getUserId,miniAccountExtends.getUserId());
            MiniAccount miniAccount = miniAccountService.getOne(queryWrapper);
            log.info("订单类型："+orderVo.getType().getDesc());
            exchangeIntegral(orderVo,miniAccount);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIntegral(MiniAccountIntegralDto miniAccountIntegralDto) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        String userId = miniAccountIntegralDto.getUserId();
        BigDecimal integral = miniAccountIntegralDto.getIntegral();
        MiniAccount miniAccount = miniAccountService.getOne(new QueryWrapper<MiniAccount>().eq("user_id", userId));
        Integer type = miniAccountIntegralDto.getType();


        MiniAccountIntegral miniAccountIntegral = new MiniAccountIntegral();
        miniAccountIntegral.setType(type);
        miniAccountIntegral.setUserId(miniAccount.getUserId());
        miniAccountIntegral.setCreateUserId(Long.valueOf(curUser.getUserId()));
        miniAccountIntegral.setCreateUserName(curUser.getNikeName());
        miniAccountIntegral.setPlatformUserId(curUser.getUserId());
        miniAccountIntegral.setPlatformUserName(curUser.getNikeName());



        BigDecimal oldIntegral = miniAccount.getIntegral() == null ? BigDecimal.ZERO : miniAccount.getIntegral();
        BigDecimal oldCurrentIntegral = miniAccount.getCurrentIntegral() == null ? BigDecimal.ZERO : miniAccount.getCurrentIntegral();

        if(type== MiniAccountIntegralTypeEnum.ADD.getStatus()){
            miniAccount.setIntegral(oldIntegral.add(integral));
            miniAccount.setCurrentIntegral(oldCurrentIntegral.add(integral));
            miniAccountIntegral.setRemark("手动增加积分");
            miniAccountIntegral.setIntegral(integral);
        }else if(type== MiniAccountIntegralTypeEnum.SUBTRACT.getStatus()){
            miniAccount.setIntegral(oldIntegral.subtract(integral));
            miniAccount.setCurrentIntegral(oldCurrentIntegral.subtract(integral));
            miniAccountIntegral.setRemark("手动减少积分");
            miniAccountIntegral.setIntegral(integral.negate());
        }
        miniAccountIntegral.setLastIntegral(oldCurrentIntegral);
        miniAccountIntegral.setTotalIntegral(miniAccount.getCurrentIntegral());
        miniAccountIntegral.setSource(MiniAccountIntegralSourceEnum.BACKEND.getStatus());
        miniAccountService.updateById(miniAccount);
        this.save(miniAccountIntegral);
    }

    @Override
    public PageUtils<IntegralRankingVo> searchIntegralRanking(IntegralRankingParam param) {
        IPage<IntegralRankingVo> page = this.baseMapper.searchIntegralRanking(new Page<>(param.getCurrent(), param.getSize()), param);
        return new PageUtils<>(page);
    }

    @Override
    public void exportIntegralRanking(IntegralRankingParam param) {
        param.setCurrent(1);
        // 设置导出最大数量
        param.setSize(CommonConstants.MAX_EXPORT_SIZE);

        IPage<IntegralRankingVo> page = this.baseMapper.searchIntegralRanking(new Page<>(param.getCurrent(), param.getSize()), param);

        List<IntegralRankingExcelVo>excelVoList = new ArrayList<>();
        for (IntegralRankingVo integralRankingVo : page.getRecords()) {
            IntegralRankingExcelVo integralRankingExcelVo = new IntegralRankingExcelVo();
            BeanUtils.copyProperties(integralRankingVo,integralRankingExcelVo);
            excelVoList.add(integralRankingExcelVo);
        }

        String fileName = DateUtil.format(new Date(), "yyyyMMdd HHmmss") + RandomUtil.randomString(4) + "积分排行列表";
        boolean success = HuToolExcelUtils.list2xlsx(excelVoList, fileName, IntegralRankingExcelVo.class);
        if(!success){
            throw new ServiceException("导出数据异常", SystemCode.FAILURE.getCode());
        }
    }

    @Override
    public PageUtils<MiniAccountIntegralManageVo> searchMiniAccountIntegralManage(MiniAccountIntegralManageParam param) {
        IPage<MiniAccountIntegralManageVo> page = this.baseMapper.searchMiniAccountIntegralManage(new Page<>(param.getCurrent(), param.getSize()), param);
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if(page.getRecords()!=null&&page.getRecords().size()>0){
            for (MiniAccountIntegralManageVo record : page.getRecords()) {
                String firstLoginTime = record.getFirstLoginTime();
                LocalDateTime localDateTime = LocalDateTime.parse(firstLoginTime, formatter);
                long daysBetween = ChronoUnit.DAYS.between(localDateTime, now);
                record.setUseDays(daysBetween);
                for (MiniAccountIntegralTypeEnum miniAccountIntegralTypeEnum : MiniAccountIntegralTypeEnum.values()) {
                    if(miniAccountIntegralTypeEnum.getStatus() == record.getType()){
                        record.setTypeText(miniAccountIntegralTypeEnum.getDesc());
                        break;
                    }
                }
            }
        }
        return new PageUtils<>(page);
    }

    @Override
    public void exportMiniAccountIntegralManage(MiniAccountIntegralManageParam param) {
        // 设置导出最大限制
        HuToolExcelUtils.exportParamToMax(param);
        PageUtils<MiniAccountIntegralManageVo> pageUtils = searchMiniAccountIntegralManage(param);
        List<MiniAccountIntegralManageVo> dataList = pageUtils.getList();
        // 使用Lambda转换方式导出Excel
        HuToolExcelUtils.exportData(dataList, "积分明细", item -> new MiniAccountIntegralManageExcelVo());
    }


    /**
     * 兑换积分
     * @param orderVo
     * @param miniAccount
     */
    private void exchangeIntegral(OrderVo orderVo,MiniAccount miniAccount){
        log.info("兑换积分.......");
        BigDecimal allIntegral = orderVo.getAllIntegral();
        BigDecimal currentIntegral = miniAccount.getCurrentIntegral() == null ? BigDecimal.ZERO : miniAccount.getCurrentIntegral();
        BigDecimal usedIntegral = miniAccount.getUsedIntegral() == null ? BigDecimal.ZERO : miniAccount.getUsedIntegral();
        if(allIntegral!=null){
            miniAccount.setCurrentIntegral(currentIntegral.subtract(allIntegral));
            miniAccount.setUsedIntegral(usedIntegral.add(allIntegral));
            miniAccountService.updateById(miniAccount);
            MiniAccountIntegralDto miniAccountIntegralDto = new MiniAccountIntegralDto();
            miniAccountIntegralDto.setUserId(miniAccount.getUserId());
            miniAccountIntegralDto.setIntegral(allIntegral.negate());
            miniAccountIntegralDto.setType(MiniAccountIntegralTypeEnum.SALE_EXCHANGE.getStatus());
            miniAccountIntegralDto.setOrderId(orderVo.getId()+"");
            miniAccountIntegralDto.setRemark("兑换商品");
            miniAccountIntegralDto.setLastIntegral(currentIntegral);
            miniAccountIntegralDto.setTotalIntegral(miniAccount.getCurrentIntegral());
            miniAccountIntegralDto.setSource(MiniAccountIntegralSourceEnum.SYSTEM.getStatus());
            String tenantId = orderVo.getTenantId();
            this.addMiniAccountIntegralByOrder(miniAccountIntegralDto,tenantId);
        }

    }
    /**
     * 添加订单积分
     * @param orderVo
     * @param miniAccount
     * @param type
     */
    private void addOrderIntegral(OrderVo orderVo,MiniAccount miniAccount,Integer type){
        String tenantId = orderVo.getTenantId();
        IntegralRuleVo integralRuleVo = remoteGoodsService.getIntegralRuleVoByRuleType(type,tenantId);
        if(integralRuleVo!=null&&integralRuleVo.getEnableStatus()!=null&&integralRuleVo.getEnableStatus()==EnableStatusEnum.YES.getStatus()
                &&integralRuleVo.getIntegral().compareTo(BigDecimal.ZERO)>0){
            log.info("添加订单积分.......");
            log.info("获取积分规则",integralRuleVo);
            BigDecimal needIntegral = integralRuleVo.getIntegral();
            BigDecimal amount = integralRuleVo.getAmount();
            //获取添加的积分
            BigDecimal integral = BigDecimal.ZERO;
            if(amount.compareTo(BigDecimal.ZERO)>0){
                integral = needIntegral.divide(amount, 2, BigDecimal.ROUND_HALF_UP).multiply(orderVo.getPayAmount());
            }
            BigDecimal allIntegral = miniAccount.getIntegral() == null ? BigDecimal.ZERO : miniAccount.getIntegral();
            BigDecimal currentIntegral = miniAccount.getCurrentIntegral() == null ? BigDecimal.ZERO : miniAccount.getCurrentIntegral();

            miniAccount.setIntegral(allIntegral.add(integral));
            miniAccount.setCurrentIntegral(currentIntegral.add(integral));

            log.info("开始更新用户积分信息",miniAccount);
            miniAccountService.updateById(miniAccount);
            MiniAccountIntegralDto miniAccountIntegralDto = new MiniAccountIntegralDto();
            miniAccountIntegralDto.setUserId(miniAccount.getUserId());
            miniAccountIntegralDto.setIntegral(integral);
            miniAccountIntegralDto.setOrderId(orderVo.getId()+"");

            if(type == RuleTypeEnum.PAY_TICKET.getSaleMode()){
                miniAccountIntegralDto.setType(MiniAccountIntegralTypeEnum.BUY_PASS_TICKET.getStatus());
                miniAccountIntegralDto.setRemark("购买通惠证");
            }else{
                miniAccountIntegralDto.setType(MiniAccountIntegralTypeEnum.PRODUCT_BUY.getStatus());
                miniAccountIntegralDto.setRemark("购买商品");
            }
            miniAccountIntegralDto.setSource(MiniAccountIntegralSourceEnum.SYSTEM.getStatus());
            miniAccountIntegralDto.setLastIntegral(currentIntegral);
            miniAccountIntegralDto.setTotalIntegral(miniAccount.getCurrentIntegral());

            log.info("开始更新用户积分明细",miniAccountIntegralDto);

            this.addMiniAccountIntegralByOrder(miniAccountIntegralDto,tenantId);
        }

    }

    /**
     * 订单消息添加积分记录
     * @param miniAccountIntegralDto
     * @param tenantId
     */
    private void addMiniAccountIntegralByOrder(MiniAccountIntegralDto miniAccountIntegralDto, String tenantId) {
        MiniAccountIntegral miniAccountIntegral = new MiniAccountIntegral();
        BeanUtils.copyProperties(miniAccountIntegralDto,miniAccountIntegral);
        miniAccountIntegral.setTenantId(tenantId);
        this.baseMapper.insert(miniAccountIntegral);
    }
}
