package com.medusa.gruul.account.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccountCoupon;
import com.medusa.gruul.account.api.entity.MiniAccountPackageGoods;
import com.medusa.gruul.account.api.entity.MiniAccountPackageGoodsCode;
import com.medusa.gruul.account.api.entity.MiniAccountPackageOrder;
import com.medusa.gruul.account.api.enums.PackageOrderStatusEnum;
import com.medusa.gruul.account.api.enums.PackageStatusEnum;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageOrderParam;
import com.medusa.gruul.account.api.model.vo.MiniAccountCouponSearchVo;
import com.medusa.gruul.account.mapper.MiniAccountCouponMapper;
import com.medusa.gruul.account.mapper.MiniAccountPackageGoodsMapper;
import com.medusa.gruul.account.mapper.MiniAccountPackageOrderMapper;
import com.medusa.gruul.account.model.param.ApiMiniAccountPackageOrderParam;
import com.medusa.gruul.account.model.vo.ApiMiniAccountPackageOrderVo;
import com.medusa.gruul.account.model.vo.ApiPackageGoodsVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageOrderExcelVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageOrderVo;
import com.medusa.gruul.account.service.IMiniAccountPackageGoodsCodeService;
import com.medusa.gruul.account.service.IMiniAccountPackageGoodsService;
import com.medusa.gruul.account.service.IMiniAccountPackageOrderService;
import com.medusa.gruul.afs.api.model.UpdatePackageOrderStatusMessage;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.HuToolExcelUtils;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.goods.api.entity.PackageCoupon;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ProductAllPackageVo;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.order.api.model.OrderItemVo;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.vo.RelationInfoVo;
import com.medusa.gruul.shops.api.enums.PromotionStatusEnum;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import com.medusa.gruul.shops.api.model.AccountCouponVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: plh
 * @Description: 会员权益包购买记录ServiceImpl
 * @Date: Created in 15:14 2024/9/5
 */
@Service
public class MiniAccountPackageOrderServiceImpl extends ServiceImpl<MiniAccountPackageOrderMapper, MiniAccountPackageOrder> implements IMiniAccountPackageOrderService {

    @Autowired
    private RemoteGoodsService remoteGoodsService;
    @Autowired
    private RemoteShopsService remoteShopsService;


    @Autowired
    private MiniAccountCouponMapper miniAccountCouponMapper;

    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;

    @Autowired
    private IMiniAccountPackageGoodsService miniAccountPackageGoodsService;
    @Autowired
    private IMiniAccountPackageGoodsCodeService miniAccountPackageGoodsCodeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMiniAccountPackageOrderByMq(OrderVo orderVo) {
        String tenantId = orderVo.getTenantId();
        String shopId = orderVo.getShopId();
        TenantContextHolder.setTenantId(tenantId);
        ShopContextHolder.setShopId(shopId);
        String userId = orderVo.getUserId();
        LocalDateTime payTime = orderVo.getPayTime();
        List<OrderItemVo> orderItemList = orderVo.getOrderItemList();
        if(orderItemList!=null&&orderItemList.size()>0){
            for (OrderItemVo orderItemVo : orderItemList) {
                //购买数量只能是一个，不然会影响互斥商品
                Integer productQuantity = orderItemVo.getProductQuantity();
                for(int i=0;i<productQuantity;i++){
                    //1.添加会员权益包购买记录
                    MiniAccountPackageOrder miniAccountPackageOrder = new MiniAccountPackageOrder();
                    miniAccountPackageOrder.setUserId(Long.valueOf(userId));//小程序用户id
                    miniAccountPackageOrder.setPackageId(orderItemVo.getProductId());//权益包id
                    miniAccountPackageOrder.setPackageName(orderItemVo.getProductName());//权益包名称
                    miniAccountPackageOrder.setOrderId(orderItemVo.getOrderId());//订单id
                    miniAccountPackageOrder.setPayTime(payTime);//支付时间
                    miniAccountPackageOrder.setPayAmount(orderItemVo.getRealAmount());//支付金额
                    miniAccountPackageOrder.setProductQuantity(1);//购买数量

                    miniAccountPackageOrder.setDepartmentId(orderVo.getDepartmentId());//部门id
                    miniAccountPackageOrder.setDepartmentCode(orderVo.getDepartmentCode());//部门编码
                    miniAccountPackageOrder.setDepartmentName(orderVo.getDepartmentName());//部门名称

                    miniAccountPackageOrder.setStoreFrontId(orderVo.getStoreFrontId());//门店id
                    miniAccountPackageOrder.setStoreFrontCode(orderVo.getStoreFrontCode());//门店编码
                    miniAccountPackageOrder.setStoreFrontName(orderVo.getStoreFrontName());//门店名称

                    miniAccountPackageOrder.setEmployeeId(orderVo.getEmployeeId());//职员id
                    miniAccountPackageOrder.setEmployeeOutId(orderVo.getEmployeeOutId());//职员标识
                    miniAccountPackageOrder.setEmployeeName(orderVo.getEmployeeName());//职员名称

                    miniAccountPackageOrder.setAccountId(orderVo.getAccountId());//用户id
                    miniAccountPackageOrder.setAccountName(orderVo.getAccountName());//用户名称

                    //保存下单时，下单人关联的仓库
                    RelationInfoVo relationInfoVo = remoteMiniInfoService.getRelationInfoByAccountId(orderVo.getAccountId());
                    miniAccountPackageOrder.setStockId(relationInfoVo.getStockId());//仓库id
                    miniAccountPackageOrder.setStockCode(relationInfoVo.getStockCode());//仓库标识
                    miniAccountPackageOrder.setStockName(relationInfoVo.getStockName());//仓库名称



                    ProductVo productVo = remoteGoodsService.findProductById(orderItemVo.getProductId());

                    miniAccountPackageOrder.setPackageStartTime(LocalDate.parse(productVo.getPackageStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss")).atStartOfDay());
                    miniAccountPackageOrder.setPackageEndTime(LocalDate.parse(productVo.getPackageEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss")).atStartOfDay());
                    miniAccountPackageOrder.setPackageShowStartTime(LocalDate.parse(productVo.getPackageShowStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss")).atStartOfDay());
                    miniAccountPackageOrder.setPackageShowEndTime(LocalDate.parse(productVo.getPackageShowEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss")).atStartOfDay());

                    this.save(miniAccountPackageOrder);
                    //2.添加会员权益包购买商品记录
                    Long mainId = miniAccountPackageOrder.getId();
                    List<ProductAllPackageVo> list = remoteGoodsService.findProductPackageByPackageId(orderItemVo.getProductId());
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    if(list != null && list.size() > 0){
                        for (ProductAllPackageVo productAllPackageVo : list) {
                            MiniAccountPackageGoods miniAccountPackageGoods = new MiniAccountPackageGoods();
                            miniAccountPackageGoods.setMainId(mainId);//主表id
                            miniAccountPackageGoods.setProductId(productAllPackageVo.getProductId());//商品id
                            miniAccountPackageGoods.setProductName(productAllPackageVo.getProductName());//商品名称
                            miniAccountPackageGoods.setSkuId(productAllPackageVo.getSkuId());//规格id
                            miniAccountPackageGoods.setSkuName(productAllPackageVo.getSkuName());//规格名称
                            miniAccountPackageGoods.setAllTimes(productAllPackageVo.getUseNumber());//总次数
                            miniAccountPackageGoods.setAlreadyTimes(0);//已用次数
                            miniAccountPackageGoods.setStatus(PackageStatusEnum.NO_EFFECTIVE.getStatus());//状态
                            miniAccountPackageGoods.setStartTime(productAllPackageVo.getStartTime());//开始时间
                            miniAccountPackageGoods.setEndTime(productAllPackageVo.getEndTime());//结束时间
                            if(null != productAllPackageVo.getUseDays() && productAllPackageVo.getUseDays() > 0){
                                Date endTime = DateUtil.offsetDay(DateUtil.date(), productAllPackageVo.getUseDays());
                                miniAccountPackageGoods.setStartTime(LocalDateTime.parse(DateUtil.format(DateUtil.date(), "yyyy-MM-dd 00:00:00"), formatter));//开始时间
                                miniAccountPackageGoods.setEndTime(LocalDateTime.parse(DateUtil.format(endTime, "yyyy-MM-dd 23:59:59"), formatter));//结束时间
                            }
                            miniAccountPackageGoods.setMutexGoodId(productAllPackageVo.getMutexGoodId());//互斥商品id
                            miniAccountPackageGoods.setMutexFlag(productAllPackageVo.getMutexFlag());//是否互斥商品->0.否，1.是
                            miniAccountPackageGoods.setPrice(productAllPackageVo.getPrice());//实售价
                            miniAccountPackageGoods.setOriginalPrice(productAllPackageVo.getOriginalPrice());//指导价（划线价）
                            miniAccountPackageGoods.setAmount(productAllPackageVo.getAmount());//金额
                            miniAccountPackageGoods.setNotTerm(productAllPackageVo.getNotTerm());//无期限->0.否;1.是
                            miniAccountPackageGoods.setNotTime(productAllPackageVo.getNotTime());//不限次数->0.否;1.是
                            miniAccountPackageGoods.setPackageId(orderItemVo.getProductId());//权益包id
                            miniAccountPackageGoods.setAdjustmentPrice(productAllPackageVo.getAdjustmentPrice());//调后价格
                            miniAccountPackageGoods.setCostPrice(productAllPackageVo.getCostPrice());//成本价
                            miniAccountPackageGoods.setOrderId(miniAccountPackageOrder.getOrderId());//订单id
                            miniAccountPackageGoods.setUserId(miniAccountPackageOrder.getUserId().toString());

                            // 单次频率天数
                            miniAccountPackageGoods.setDaysRate(productAllPackageVo.getDaysRate());
                            miniAccountPackageGoodsService.save(miniAccountPackageGoods);
                        }
                    }
                    //3.添加权益包赠送优惠券
                    //查询权益包赠送的优惠券
                    List<PackageCoupon> packageCouponList = remoteGoodsService.findPackageCouponByPackageId(orderItemVo.getProductId());
                    if(packageCouponList!=null&&packageCouponList.size()>0){
                        for (PackageCoupon packageCoupon : packageCouponList) {
                            String couponId = packageCoupon.getCouponId();
                            //获取优惠券
                            AccountCouponVo accountCouponVo = remoteShopsService.getCouponById(Long.valueOf(couponId));
                            if(accountCouponVo!=null){
                                MiniAccountCoupon miniAccountCoupon = new MiniAccountCoupon();
                                miniAccountCoupon.setStartTime(accountCouponVo.getStartTime());
                                miniAccountCoupon.setEndTime(accountCouponVo.getEndTime());
                                Date newDate = new Date();
                                Date startTime = accountCouponVo.getStartTime();
                                Date endTime = accountCouponVo.getEndTime();
                                //根据使用时间范围判断优惠卷是未用还是失效状态
                                miniAccountCoupon.setStatus(PromotionStatusEnum.EXPIRED.getStatus());
                                if(newDate.getTime()>=startTime.getTime()&&newDate.getTime()<=endTime.getTime()){
                                    miniAccountCoupon.setStatus(PromotionStatusEnum.UN_USE.getStatus());
                                }
                                miniAccountCoupon.setUserId(Long.valueOf(userId));
                                miniAccountCoupon.setCouponId(accountCouponVo.getId());
                                miniAccountCoupon.setCouponName(accountCouponVo.getCouponName());
                                miniAccountCoupon.setPromotion(accountCouponVo.getPromotion());
                                miniAccountCouponMapper.insert(miniAccountCoupon);
                            }
                        }
                    }

                }
            }
        }
    }

    @Override
    public IPage<MiniAccountPackageOrderVo> getPageList(MiniAccountPackageOrderParam miniAccountPackageOrderParam) {
        IPage<MiniAccountPackageOrderVo> pageList = this.baseMapper.getPageList(new Page<>(miniAccountPackageOrderParam.getCurrent(), miniAccountPackageOrderParam.getSize()),
                miniAccountPackageOrderParam);
        List<MiniAccountPackageOrderVo> records = pageList.getRecords();
        //查询订单是否全部完成核销
        if(records!=null&&records.size()>0){
            for (MiniAccountPackageOrderVo record : records) {
                Long id = record.getId();
                record.setWriteOffFlag(1);
                LambdaQueryWrapper<MiniAccountPackageGoods>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(MiniAccountPackageGoods::getMainId,id);
                List<MiniAccountPackageGoods> list = miniAccountPackageGoodsService.list(wrapper);
                if(list!=null&&list.size()>0){
                    for (MiniAccountPackageGoods miniAccountPackageGoods : list) {
                        if(miniAccountPackageGoods.getNotTime()==0
                                &&miniAccountPackageGoods.getAlreadyTimes()<miniAccountPackageGoods.getAllTimes()){//权益包商品不是无限次兑换并且兑换次数小于总次数
                            record.setWriteOffFlag(0);
                        }
                    }
                }
            }
        }
        return pageList;
    }
    @Override
    public IPage<ApiMiniAccountPackageOrderVo> getApiMiniAccountPackageOrder(ApiMiniAccountPackageOrderParam param) {
        String userId = CurUserUtil.getMiniReqeustAccountInfo().getUserId();
        param.setUserId(userId);
        IPage<ApiMiniAccountPackageOrderVo> page = this.baseMapper.getApiMiniAccountPackageOrder(new Page<>(param.getCurrent(), param.getSize()), param);
        if(page!=null&&page.getRecords()!=null&&page.getRecords().size()>0){
            for (ApiMiniAccountPackageOrderVo record : page.getRecords()) {
                Long id = record.getId();
                LambdaQueryWrapper<MiniAccountPackageGoods>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(MiniAccountPackageGoods::getMainId,id);
                if(param.getStatus()!=2){
                    wrapper.ne(MiniAccountPackageGoods::getStatus,PromotionStatusEnum.EXPIRED.getStatus());
                }
                wrapper.eq(MiniAccountPackageGoods::getUserId,CurUserUtil.getMiniReqeustAccountInfo().getShopUserId());
                wrapper.last(" order by all_times - already_times = 0 asc");
                List<MiniAccountPackageGoods> miniAccountPackageGoods = miniAccountPackageGoodsService.list(wrapper);
                record.setPackageGoodsList(miniAccountPackageGoods);
            }
        }
        return page;
    }
    @Override
    public void updateMiniAccountPackageOrderStatus() {
        String userId = CurUserUtil.getMiniReqeustAccountInfo().getShopUserId();
        LambdaQueryWrapper<MiniAccountPackageOrder>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountPackageOrder::getUserId,userId);
        //未核销的订单需要判断，是否全部核销完成

        wrapper.eq(MiniAccountPackageOrder::getStatus, PackageOrderStatusEnum.NO_COMPLETE.getStatus());
        List<MiniAccountPackageOrder> list = this.baseMapper.selectList(wrapper);
        if(list!=null&&list.size()>0){
            for (MiniAccountPackageOrder miniAccountPackageOrder : list) {
                Long miniAccountPackageOrderId = miniAccountPackageOrder.getId();
                LambdaQueryWrapper<MiniAccountPackageGoods>queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MiniAccountPackageGoods::getMainId,miniAccountPackageOrderId);
                List<MiniAccountPackageGoods> accountPackageGoodsList = miniAccountPackageGoodsService.list(queryWrapper);
                //是否核销：0.未核销；1已核销
                Integer isComplete = 1;
                //是否过期
                Integer isExpire = 1;
                LocalDateTime now = LocalDateTime.now();
                if(accountPackageGoodsList!=null&&accountPackageGoodsList.size()>0){
                    for (MiniAccountPackageGoods miniAccountPackageGoods : accountPackageGoodsList) {
                        Integer notTerm = miniAccountPackageGoods.getNotTerm();//无期限
                        Integer notTime = miniAccountPackageGoods.getNotTime();//不限次数
                        Integer allTimes = miniAccountPackageGoods.getAllTimes();//总次数
                        Integer alreadyTimes = miniAccountPackageGoods.getAlreadyTimes();//使用次数
                        LocalDateTime endTime = miniAccountPackageGoods.getEndTime();//结束时间
                        if(notTime == 1){//商品可以
                            isComplete = 0;
                        }else{
                            if(allTimes-alreadyTimes>0){//剩余次数大于使用次数
                                isComplete = 0;
                            }
                        }
                        if(notTerm == 1){//商品可以无限期兑换
                            isExpire = 0;
                        }else{
                            if(now.isBefore(endTime)){//当前时间在结束时间之前，未核销
                                isExpire = 0;
                            }
                        }
                    }
                }
                //订单核销状态已核销
                if(isComplete == 1){
                    miniAccountPackageOrder.setStatus(PackageOrderStatusEnum.COMPLETE.getStatus());
                    this.baseMapper.updateById(miniAccountPackageOrder);
                }else{
                    if(isExpire == 1){
                        miniAccountPackageOrder.setStatus(PackageOrderStatusEnum.INVALID.getStatus());
                        this.baseMapper.updateById(miniAccountPackageOrder);
                    }
                }
            }
        }

    }

    @Override
    public Boolean vailPackageOrder(Long orderId) {
        Boolean result = true;
        LambdaQueryWrapper<MiniAccountPackageOrder>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountPackageOrder::getOrderId,orderId);
        List<MiniAccountPackageOrder> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            for (MiniAccountPackageOrder miniAccountPackageOrder : list) {
                Integer status = miniAccountPackageOrder.getStatus();
                //未核销商品才允许申请退款
                if(status!=0){
                    result = false;
                }else{
                    Long miniAccountPackageOrderId = miniAccountPackageOrder.getId();
                    LambdaQueryWrapper<MiniAccountPackageGoods>queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(MiniAccountPackageGoods::getMainId,miniAccountPackageOrderId);
                    List<MiniAccountPackageGoods> packageGoodsList = miniAccountPackageGoodsService.list(queryWrapper);
                    if(packageGoodsList!=null&&packageGoodsList.size()>0){
                        for (MiniAccountPackageGoods miniAccountPackageGoods : packageGoodsList) {
                            if(miniAccountPackageGoods.getStatus() != PromotionStatusEnum.UN_USE.getStatus()
                            &&miniAccountPackageGoods.getStatus() != CommonConstants.NUMBER_ZERO){
                                result = false;
                            }else{
                                if(miniAccountPackageGoods.getAlreadyTimes()>0){
                                    result = false;
                                }
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    @Override
    @Transactional
    public void updatePackageOrderStatus(UpdatePackageOrderStatusMessage message) {
        String orderId = message.getOrderId();
        Integer status = message.getStatus();
        LambdaQueryWrapper<MiniAccountPackageOrder>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountPackageOrder::getOrderId,orderId);
        List<MiniAccountPackageOrder> list = this.baseMapper.selectList(wrapper);
        if(list!=null&&list.size()>0){
            for (MiniAccountPackageOrder miniAccountPackageOrder : list) {
                LambdaQueryWrapper<MiniAccountPackageGoods>queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MiniAccountPackageGoods::getMainId,miniAccountPackageOrder.getId());
                List<MiniAccountPackageGoods> packageGoodsList = miniAccountPackageGoodsService.list(queryWrapper);
                if(status!=null){
                    if(packageGoodsList!=null&&packageGoodsList.size()>0){
                        for (MiniAccountPackageGoods miniAccountPackageGoods : packageGoodsList) {
                            miniAccountPackageGoods.setOldStatus(miniAccountPackageGoods.getStatus());
                            miniAccountPackageGoods.setStatus(status);
                            miniAccountPackageGoodsService.updateById(miniAccountPackageGoods);
                        }
                    }
                    miniAccountPackageOrder.setOldStatus(miniAccountPackageOrder.getStatus());
                    miniAccountPackageOrder.setStatus(2);
                    this.updateById(miniAccountPackageOrder);
                }else{
                    if(packageGoodsList!=null&&packageGoodsList.size()>0){
                        for (MiniAccountPackageGoods miniAccountPackageGoods : packageGoodsList) {
                            miniAccountPackageGoods.setStatus(miniAccountPackageGoods.getOldStatus());
                            miniAccountPackageGoods.setOldStatus(null);
                            miniAccountPackageGoodsService.updateById(miniAccountPackageGoods);
                        }
                    }
                    miniAccountPackageOrder.setStatus(miniAccountPackageOrder.getOldStatus());
                    miniAccountPackageOrder.setOldStatus(null);
                    this.updateById(miniAccountPackageOrder);
                }
            }
        }
    }

    @Override
    @Transactional
    public void deleteByOrderId(Long orderId) {
        LambdaQueryWrapper<MiniAccountPackageOrder>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountPackageOrder::getOrderId,orderId);
        List<MiniAccountPackageOrder> list = this.list(wrapper);
        if(list!=null&&list.size()>0){
            for (MiniAccountPackageOrder miniAccountPackageOrder : list) {

                LambdaQueryWrapper<MiniAccountPackageGoods>queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MiniAccountPackageGoods::getOrderId,orderId);
                List<MiniAccountPackageGoods> apiPackageGoods = miniAccountPackageGoodsService.list(queryWrapper);


                if(apiPackageGoods!=null&&apiPackageGoods.size()>0){
                    for (MiniAccountPackageGoods apiPackageGood : apiPackageGoods) {
                        LambdaQueryWrapper<MiniAccountPackageGoodsCode>codeLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        codeLambdaQueryWrapper.eq(MiniAccountPackageGoodsCode::getMiniAccountPackageGoodsId,apiPackageGood.getId());
                        List<MiniAccountPackageGoodsCode> miniAccountPackageGoodsCodeList = miniAccountPackageGoodsCodeService.list(codeLambdaQueryWrapper);
                        if(miniAccountPackageGoodsCodeList!=null&&miniAccountPackageGoodsCodeList.size()>0){
                            for (MiniAccountPackageGoodsCode miniAccountPackageGoodsCode : miniAccountPackageGoodsCodeList) {
                                miniAccountPackageGoodsCodeService.removeById(miniAccountPackageGoodsCode.getId());
                            }
                        }
                        miniAccountPackageGoodsService.removeById(apiPackageGood.getId());
                    }
                }
                this.removeById(miniAccountPackageOrder.getId());
            }
        }
    }

    /**
     * 导出销售记录
     */
    public void exportPackageOrder(MiniAccountPackageOrderParam miniAccountPackageOrderParam){
        HuToolExcelUtils.exportParamToMax(miniAccountPackageOrderParam);
        IPage<MiniAccountPackageOrderVo> pageList = this.getPageList(miniAccountPackageOrderParam);

        HuToolExcelUtils.exportData(pageList.getRecords(),"销售列表",source-> {
            MiniAccountPackageOrderExcelVo excelVo = new MiniAccountPackageOrderExcelVo();
            excelVo.setOrderId(source.getOrderId()+"");
            return  excelVo;
        }, Stream.of("productQuantity","payAmount").collect(Collectors.toList()));
    }
}
