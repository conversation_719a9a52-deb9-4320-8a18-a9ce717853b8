package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountCoupon;
import com.medusa.gruul.account.api.model.AccountCouponDto;
import com.medusa.gruul.account.api.model.param.MiniAccountCouponSearchParam;
import com.medusa.gruul.account.api.model.vo.MiniAccountCouponSearchVo;
import com.medusa.gruul.account.api.model.vo.MiniAccountCouponVo;
import com.medusa.gruul.account.api.model.MiniAccountCouponByOrderDto;
import com.medusa.gruul.account.model.dto.MiniAccountCouponDto;
import com.medusa.gruul.account.model.param.MiniAccountCouponParam;
import com.medusa.gruul.account.api.model.vo.MiniAccountCouponByOrderVo;
import com.medusa.gruul.goods.api.model.dto.api.MiniOrderCouponDto;
import com.medusa.gruul.shops.api.model.SendCouponMessage;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:41 2024/8/27
 */
public interface IMiniAccountCouponService extends IService<MiniAccountCoupon> {

    /**
     * 小程序-领取优惠券
     * @param miniAccountCouponDto
     * @return
     */
    MiniAccountCoupon add(MiniAccountCouponDto miniAccountCouponDto);

    /**
     * 小程序-获取用户领取优惠券数
     * @return
     */
    Integer getMiniAccountCouponNum();

    /**
     * 根据优惠券id获取小程序用户领取优惠次数
     * @param couponId
     * @return
     */
    Integer getMiniAccountCouponNumByCouponId(Long couponId,String userId);

    /**
     * 分页分页获取用户优惠券信息
     * @param miniAccountCouponParam
     * @return
     */
    IPage<MiniAccountCouponVo> pageMyCoupon(MiniAccountCouponParam miniAccountCouponParam);

    /**
     * 获取用户可用的优惠券
     * @return
     */
    List<MiniAccountCouponByOrderVo> getCouponByUser(MiniAccountCouponByOrderDto miniAccountCouponByOrderDto);


    /**
     * 获取用户可用的优惠券
     * @return
     */
    List<MiniAccountCouponByOrderVo> getCouponByUser2(MiniAccountCouponByOrderDto miniAccountCouponByOrderDto);

    /**
     * 用户优惠券标记为已使用
     * @param accountCouponDto
     * @return
     */
    Boolean updateAccountCouponOk(AccountCouponDto accountCouponDto);

    /**
     * 用户优惠券标记为未使用
     * @param accountCouponDto
     * @return
     */
    Boolean updateAccountCouponNo(AccountCouponDto accountCouponDto);


    /**
     * 查询商家核销的记录
     * @return
     */
    IPage<MiniAccountCouponSearchVo> getMiniAccountCouponSearchVo(MiniAccountCouponSearchParam miniAccountCouponSearchParam);

    /**
     * 发送优惠券
     * @param message
     */
    void sendCoupon(SendCouponMessage message);


    /**
     * 导出优惠券明细
     * @param param 查询参数
     */
    void exportMiniAccountCouponSearch(MiniAccountCouponSearchParam param);
}
