package com.medusa.gruul.account.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MemberLevelRights;
import com.medusa.gruul.account.model.vo.MemberLevelRightsVo;

import java.util.List;

/**
 * @Description: 会员权益
 * @Author: jeecg-boot
 * @Date:   2022-02-23
 * @Version: V1.0
 */
public interface IMemberLevelRightsService extends IService<MemberLevelRights> {
    /**
     * 根据会员权益id查询会员权益
     *
     */

    List<MemberLevelRights>  selectRightsList(List<String> rightsIdList);
/**
 * 查询全部会员权益
 *
 */
    List<MemberLevelRightsVo>  selectRightsVoAllList();


    List<MemberLevelRights> selectRightsListByEnable(List<String> rightsIdList);

}
