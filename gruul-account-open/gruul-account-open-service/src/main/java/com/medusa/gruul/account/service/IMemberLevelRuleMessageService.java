package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MemberLevelRuleMessage;
import com.medusa.gruul.account.model.dto.MemberLevelRuleMessageDto;
import com.medusa.gruul.account.model.param.MemberLevelRuleMessageParam;
import com.medusa.gruul.account.model.vo.MemberLevelRuleMessageVo;
import com.medusa.gruul.account.model.vo.UpgradeMemberDataVo;
import com.medusa.gruul.common.core.util.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:11 2025/3/18
 */
public interface IMemberLevelRuleMessageService extends IService<MemberLevelRuleMessage> {

    /**
     * 添加或者编辑会员等级规则信息
     * @param memberLevelRuleMessageDto
     * @return
     */
    String addOrUpdateMemberLevelRuleMessage(MemberLevelRuleMessageDto memberLevelRuleMessageDto);

    /**
     * 获取会员等级规则信息
     * @return
     */
    MemberLevelRuleMessageVo getMemberLevelRuleMessage(MemberLevelRuleMessageParam param);

    /**
     * 获取会员等级规则列表
     * @return
     */
    List<MemberLevelRuleMessageVo>getMemberLevelRuleMessageList();
    /**
     * 根据会员类型id获取跳转首页标识
     * @return
     */
    Integer getHomeFlagByMemberTypeId(Long memberTypeId);

    /**
     * 获取会员等级升级数据
     * @param memberTypeId
     * @return
     */
    List<UpgradeMemberDataVo> getUpgradeMemberData(Long memberTypeId);
}
