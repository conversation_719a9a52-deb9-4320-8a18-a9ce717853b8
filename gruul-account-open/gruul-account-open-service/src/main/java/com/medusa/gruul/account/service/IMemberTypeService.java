package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MemberType;
import com.medusa.gruul.account.model.dto.AddOrUpdateMemberTypeDto;
import com.medusa.gruul.account.model.dto.MemberTypeDto;
import com.medusa.gruul.account.model.param.MemberTypeParam;
import com.medusa.gruul.account.model.vo.MemberTypeVo;
import com.medusa.gruul.common.core.util.PageUtils;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:42 2025/5/20
 */
public interface IMemberTypeService extends IService<MemberType> {

    /**
     * 新增/修改会员类型
     * @param dto
     */
    void addOrUpdateMemberType(AddOrUpdateMemberTypeDto dto);

    /**
     * 删除会员类型
     * @param dto
     */
    void deleteMemberType(MemberTypeDto dto);

    /**
     * 获取会员类型列表
     * @return
     */
    List<MemberTypeVo> getMemberType(MemberTypeParam param);

    /**
     * 启用/停用会员类型
     * @param dto
     */
    String updateStatus(MemberTypeDto dto);

    /**
     * 设置为默认会员类型
     * @param dto
     */
    void setDefaultType(MemberTypeDto dto);

    /**
     * 根据用户id获取会员类型
     * @param userId
     * @return
     */
    List<MemberTypeVo> getMemberTypeList(String userId);

    /**
     * 获取非区域类型会员类型
     * @return
     */
    List<MemberType> getMemberTypeNoRegionFlagList();

    /**
     * 获取区域会员类型
     * @return
     */
    MemberType getRegionMemberType();

    /**
     * 获取默认会员类型
     * @return
     */
    MemberType getDefaultMemberType();
}
