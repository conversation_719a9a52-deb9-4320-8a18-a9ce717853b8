package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.model.dto.MemberLevelDto;
import com.medusa.gruul.account.model.vo.MemberLevePriceVo;
import com.medusa.gruul.account.model.vo.MemberLevelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-02
 */
@Mapper
public interface MemberLevelMapper extends BaseMapper<MemberLevel> {
    /**
     * 添加会员卡信息
     *
     * @param memberLevel
     *
     */
    void add(MemberLevel memberLevel);

    /**
     * 修改等级
     *
     * @param miniAccountMember
     *
     */
    void updateLevel(MiniAccountMemberInformation miniAccountMember);
    /**
     * 续费
     *
     * @param memberLevel
     *
     */
    void renew(MemberLevel memberLevel);
    /**
     * 停用会员卡
     *
     * @param memberId
     *
     */
    void deactivate(String memberId);
    /**
     * 启用会员卡
     *
     * @param memberId
     *
     */
    void enable(String memberId);

    /**
     * 查询会员卡信息
     * @return com.medusa.gruul.account.api.entity.MiniAccountMemberLevel
     *
     */
    List<MemberLevel> selectMemberLevelList(@Param("memberTypeId") Long memberTypeId);

    /**
     * 查询全部会员等级信息
     *@return com.medusa.gruul.account.model.vo
     *
     */
    List<MemberLevelVo> selectMemberLevelAllList();


    /**
     * 修改会员等级表信息
     *@param memberLevel 会员等级
     *
     */
    void update(MemberLevel memberLevel);

    /**
     * 删除会员等级表信息
     *@param memberLevel 会员等级
     *
     */
    void deleteMemberLevel(MemberLevel memberLevel);





    /**
     * 查询权益信息
     * @param rightslIdList
     * @return com.medusa.gruul.account.api.entity.MiniAccountMemberLevelRights
     *
     */
    List<MemberLevelRights> selectRightsAndInterestsList(@Param("rightsIdList")List<String> rightslIdList);
    /**
     * 查询权益信息
     *@return com.medusa.gruul.account.api.entity.MiniAccountMemberLevelRights
     *
     */
    List<MemberLevelRights> selectRights();


    /**
     * 查询会员等级价格数据
     *
     * @param productId        商品id
     * @return com.medusa.gruul.account.model.vo.MemberLevePriceVo
     */
    List<MemberLevePriceVo> selectMemberLevePrice(Long productId);

    /**
     * 外部系统查询会员卡列表
     * @param userListDtoPage
     * @param paramMap
     * @return
     */
    IPage<MemberLevelDto> selectExternalByMemberLeverList(Page<MemberLevelVo> userListDtoPage, @Param("paramMap") Map<String, Object> paramMap);

    /**
     * 批量修改会员卡发送状态
     *
     * @param accountIds the order ids
     * @param    sendStatus
     * @return void
     * <AUTHOR>
     * @date 2022 /05/24 09:12
     */
    void updateSendStatus(@Param(value = "accountIds")List<String> accountIds,@Param(value = "sendStatus")String sendStatus);

    /**
     * 根据会员类型id获取启用会员等级
     * @param memberTypeId
     * @return
     */
    List<MemberLevelVo> getMemberLevelByMemberTypeId(@Param("memberTypeId") Long memberTypeId);

    /**
     * 根据区域类型获取区域类型为是会员等级数量
     * @param regionType
     * @param id
     * @return
     */
    Integer getRegionCountByRegionType(@Param("regionType")Integer regionType, @Param("id")String id);
}
