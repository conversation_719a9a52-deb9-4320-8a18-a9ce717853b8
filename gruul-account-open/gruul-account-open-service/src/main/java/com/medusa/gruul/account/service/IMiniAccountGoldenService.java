package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountGolden;
import com.medusa.gruul.account.model.dto.MiniAccountGoldenDto;
import com.medusa.gruul.account.model.param.MiniAccountCommissionManageParam;
import com.medusa.gruul.account.model.param.MiniAccountGoldenManageParam;
import com.medusa.gruul.account.model.param.MiniAccountGoldenParam;
import com.medusa.gruul.account.model.vo.MiniAccountCommissionManageVo;
import com.medusa.gruul.account.model.vo.MiniAccountGoldenManageVo;
import com.medusa.gruul.account.model.vo.MiniAccountGoldenVo;
import com.medusa.gruul.account.model.vo.UserGoldenVo;
import com.medusa.gruul.common.core.util.PageUtils;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:25 2025/6/11
 */
public interface IMiniAccountGoldenService extends IService<MiniAccountGolden> {
    /**
     * 添加金豆
     * @param dto
     */
    void addMiniAccountGolden(MiniAccountGoldenDto dto);

    /**
     * 获取当前金豆明细
     * @return
     */
    UserGoldenVo getMiniAccountGoldenVo();

    /**
     * 分页查询用户个人的金豆明细记录
     * @param miniAccountCommissionParam
     * @return
     */
    IPage<MiniAccountGoldenVo> pageMyGolden(MiniAccountGoldenParam miniAccountCommissionParam);

    /**
     * 分页查询金豆明细
     * @param param
     * @return
     */
    PageUtils<MiniAccountGoldenManageVo> searchMiniAccountGoldenDet(MiniAccountGoldenManageParam param);

    /**
     * 导出金豆明细
     * @param param
     */
    void exportMiniAccountGoldenManage(MiniAccountGoldenManageParam param);
}
