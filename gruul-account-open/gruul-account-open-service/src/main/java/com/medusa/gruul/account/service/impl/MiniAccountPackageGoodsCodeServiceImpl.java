package com.medusa.gruul.account.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.api.enums.PackageStatusEnum;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageGoodsCodeParam;
import com.medusa.gruul.account.api.model.param.PackageGoodsCodeDetailParam;
import com.medusa.gruul.account.api.model.vo.*;
import com.medusa.gruul.account.conf.AccountRedis;
import com.medusa.gruul.account.constant.RedisKey;
import com.medusa.gruul.account.mapper.MiniAccountPackageGoodsCodeMapper;
import com.medusa.gruul.account.api.model.ManageVerifyPackageGoodsStaticDto;
import com.medusa.gruul.account.model.dto.MiniAccountPackageGoodsDto;
import com.medusa.gruul.account.model.dto.VerifyGoodsDto;
import com.medusa.gruul.account.model.param.ApiPackageGoodsCodeParam;
import com.medusa.gruul.account.model.vo.ApiPackageGoodsCodeVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageGoodsCodeVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageGoodsVo;
import com.medusa.gruul.account.mq.Sender;
import com.medusa.gruul.account.service.IMiniAccountPackageGoodsCodeService;
import com.medusa.gruul.account.service.IMiniAccountPackageGoodsService;
import com.medusa.gruul.account.service.IMiniAccountPackageOrderService;
import com.medusa.gruul.account.web.remote.RemoteMiniAccount;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.HuToolExcelUtils;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.entity.ProductStock;
import com.medusa.gruul.goods.api.entity.SkuStock;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.vo.manager.ProductVo;
import com.medusa.gruul.goods.api.param.OperateStockDto;
import com.medusa.gruul.order.api.entity.OrderItem;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.order.api.model.CreateOrderOutStockMessage;
import com.medusa.gruul.order.api.model.StockOutOrderDetDto;
import com.medusa.gruul.platform.api.feign.RemoteMiniInfoService;
import com.medusa.gruul.platform.api.model.vo.RelationInfoVo;
import com.medusa.gruul.platform.api.model.vo.StoreFrontOrderVo;
import com.medusa.gruul.shops.api.enums.PromotionStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.*;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:14 2024/9/10
 */
@Service
public class MiniAccountPackageGoodsCodeServiceImpl extends ServiceImpl<MiniAccountPackageGoodsCodeMapper, MiniAccountPackageGoodsCode> implements IMiniAccountPackageGoodsCodeService {

    @Autowired
    private IMiniAccountPackageGoodsService miniAccountPackageGoodsService;

    @Autowired
    private IMiniAccountPackageOrderService miniAccountPackageOrderService;

    @Autowired
    private RemoteOrderService remoteOrderService;
    @Autowired
    private RemoteMiniInfoService remoteMiniInfoService;
    @Autowired
    private RemoteGoodsService remoteGoodsService;
    @Autowired
    private RemoteMiniAccount remoteMiniAccount;
    @Autowired
    private Sender sender;


    @Override
    public MiniAccountPackageGoodsCode add(MiniAccountPackageGoodsDto miniAccountPackageGoodsDto) {
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        //查询用户权益包商品记录
        MiniAccountPackageGoods accountPackageGoods = miniAccountPackageGoodsService.getById(miniAccountPackageGoodsDto.getMiniAccountPackageGoodsId());
        if(null == accountPackageGoods){
            throw new ServiceException("权益包商品不存在", SystemCode.DATA_NOT_EXIST.getCode());
        }
        MiniAccountPackageOrder miniAccountPackageOrder = miniAccountPackageOrderService.getById(accountPackageGoods.getMainId());
        if(null == miniAccountPackageOrder){
            throw new ServiceException("权益包订单不存在", SystemCode.DATA_NOT_EXIST.getCode());
        }
        if(null == accountPackageGoods.getSourceUserId() && miniAccountPackageOrder.getUserId() != Long.parseLong(userId)){
            throw new ServiceException("不能使用不是自己的权益包商品", SystemCode.DATA_NOT_EXIST.getCode());
        }
        if(null != accountPackageGoods.getSourceUserId() && miniAccountPackageOrder.getUserId().longValue() != accountPackageGoods.getSourceUserId()){
            throw new ServiceException("非赠品不能使用", SystemCode.DATA_NOT_EXIST.getCode());
        }
        if(accountPackageGoods.getStatus() == PackageStatusEnum.NO_EFFECTIVE.getStatus().intValue()){
            throw new ServiceException("未生效", SystemCode.DATA_EXPIRED.getCode());
        }
        if(accountPackageGoods.getStatus() == PackageStatusEnum.EXPIRED.getStatus().intValue()){
            throw new ServiceException("已失效", SystemCode.DATA_EXPIRED.getCode());
        }
        String verifyCode = RandomUtil.randomNumbers(10);

        // 查询redis是否已存有此用户通行票记录的验证码
        String userKey = RedisKey.getAccountPackageGoodsKey(userId,accountPackageGoods.getId() + "");

        AccountRedis accountRedis = new AccountRedis();
        Date startDate = Date.from(accountPackageGoods.getStartTime().atZone( ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(accountPackageGoods.getEndTime().atZone( ZoneId.systemDefault()).toInstant());
        // 过期毫秒数
        long time = DateUtil.between(new Date(), endDate, DateUnit.MS);
        MiniAccountPackageGoodsCode miniAccountPackageGoodsCode = new MiniAccountPackageGoodsCode();
        miniAccountPackageGoodsCode.setUserId(userId);
        miniAccountPackageGoodsCode.setProductId(accountPackageGoods.getProductId());
        miniAccountPackageGoodsCode.setMiniAccountPackageGoodsId(miniAccountPackageGoodsDto.getMiniAccountPackageGoodsId());
        miniAccountPackageGoodsCode.setStartTime(startDate);
        miniAccountPackageGoodsCode.setEndTime(endDate);
        miniAccountPackageGoodsCode.setShopId(accountPackageGoods.getShopId());
        miniAccountPackageGoodsCode.setStatus(PackageStatusEnum.UN_USE.getStatus());
        miniAccountPackageGoodsCode.setPackageId(accountPackageGoods.getPackageId());
        miniAccountPackageGoodsCode.setSourceMiniAccountPackageGoodsId(accountPackageGoods.getSourceMiniAccountPackageGoodsId());
        miniAccountPackageGoodsCode.setSourceUserId(accountPackageGoods.getSourceUserId());
        // 设置值成功返回“OK”
        String result = accountRedis.setNxPx(userKey, verifyCode, time);
        if(!CommonConstants.REDIS_SUCCESS.equalsIgnoreCase(result)){
            // 不成功，表示redis已经有值，无需重新生成
            verifyCode = accountRedis.get(userKey);
            // 从数据库将此验证码记录的数据查询出来赋值id，供转赠功能使用
            miniAccountPackageGoodsCode = this.getOne(new LambdaQueryWrapper<>(MiniAccountPackageGoodsCode.class).eq(MiniAccountPackageGoodsCode::getVerifyCode, verifyCode)
                    .eq(MiniAccountPackageGoodsCode::getUserId, userId).eq(MiniAccountPackageGoodsCode::getMiniAccountPackageGoodsId, accountPackageGoods.getId()));
            miniAccountPackageGoodsCode.setVerifyCode(verifyCode);
            return miniAccountPackageGoodsCode;
        }
        miniAccountPackageGoodsCode.setVerifyCode(verifyCode);
        this.save(miniAccountPackageGoodsCode);
        return miniAccountPackageGoodsCode;
    }

    @Override
    public MiniAccountPackageGoodsCodeVo getCode(MiniAccountPackageGoodsDto miniAccountPackageGoodsDto) {
        MiniAccountPackageGoodsCodeVo codeVo = new MiniAccountPackageGoodsCodeVo();
        MiniAccountPackageGoodsCode code = this.add(miniAccountPackageGoodsDto);
        BeanUtil.copyProperties(code, codeVo);
        QrConfig qrConfig = QrConfig.create();
        byte[] bytes = QrCodeUtil.generatePng(codeVo.getVerifyCode(), qrConfig);
        String qrCode = Base64.encode(bytes);
        codeVo.setQrCode(qrCode);
        return codeVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MiniAccountPackageGoodsCode writeOffPackageGoods(MiniAccountPackageGoodsDto miniAccountPackageGoodsDto) {

        MiniAccountPackageGoodsCodeVo miniAccountPackageGoodsCodeVo = this.pcGetCode(miniAccountPackageGoodsDto);
        VerifyGoodsDto dto = new VerifyGoodsDto();
        dto.setVerifyCode(miniAccountPackageGoodsCodeVo.getVerifyCode());
        dto.setVerifyGoodsId(miniAccountPackageGoodsDto.getVerifyGoodsId());
        dto.setVerifySkuId(miniAccountPackageGoodsDto.getVerifySkuId());
        MiniAccountPackageGoodsCode miniAccountPackageGoodsCode = this.verifyCode(dto);

        return miniAccountPackageGoodsCode;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MiniAccountPackageGoodsCode verifyCode(VerifyGoodsDto dto) {
        String verifyCode = dto.getVerifyCode();
        Long verifyGoodsId = dto.getVerifyGoodsId();
        Long verifySkuId = dto.getVerifySkuId();
        CurUserDto user = CurUserUtil.getHttpCurUser();
        String verifyUserId = user.getUserId();
        String shopId = user.getShopId();
        if(StrUtil.isBlank(shopId)){
            throw new ServiceException("此账号无法核销", SystemCode.DATA_NOT_EXIST.getCode());
        }
        MiniAccountPackageGoodsCode packageGoodsCode = null;
        String codeKey = RedisKey.getPackageGoodsCodeKey(verifyCode);
        AccountRedis accountRedis = new AccountRedis();
        String status = accountRedis.get(codeKey);
        if(StrUtil.isNotBlank(status)){
            throw new ServiceException("此码已核销，不能重复核销", SystemCode.DATA_EXPIRED.getCode());
        }else{
            //未使用，则进行核销
            LambdaQueryWrapper<MiniAccountPackageGoodsCode> codeWrapper = new LambdaQueryWrapper<>();
            codeWrapper.eq(MiniAccountPackageGoodsCode::getVerifyCode, verifyCode).eq(MiniAccountPackageGoodsCode::getStatus, PackageStatusEnum.UN_USE.getStatus());
            List<MiniAccountPackageGoodsCode> codeList = this.list(codeWrapper);
            if(CollectionUtil.isEmpty(codeList)){
                throw new ServiceException("该核销码不存在，无法核销", SystemCode.DATA_MANY.getCode());
            }
            if(CollectionUtil.isNotEmpty(codeList) && codeList.size() != 1){
                throw new ServiceException("此码暂不能核销，请联系运营平台管理员", SystemCode.DATA_MANY.getCode());
            }
            packageGoodsCode = codeList.get(0);
            MiniAccountPackageGoods accountPackageGoods = this.miniAccountPackageGoodsService.getById(packageGoodsCode.getMiniAccountPackageGoodsId());
            MiniAccountPackageOrder accountPackageOrder = this.miniAccountPackageOrderService.getById(accountPackageGoods.getMainId());
            Integer notTime = accountPackageGoods.getNotTime();
            Integer notTerm = accountPackageGoods.getNotTerm();
            //有限期商品需要判断是否过期
            if(notTerm!=1){
                if(DateUtil.compare(new Date(), packageGoodsCode.getEndTime()) > 0){
                    throw new ServiceException("此码已过期，无法核销", SystemCode.DATA_EXPIRED.getCode());
                }
            }

            //查询权益包商品可以次数
            Integer allTimes = accountPackageGoods.getAllTimes();
            Integer alreadyTimes = accountPackageGoods.getAlreadyTimes()+1;

            Integer useableTimes = allTimes - alreadyTimes;
            //有使用次数商品需要判断是否核销完
            if(notTime!=1){
                if(useableTimes < 0){
                    throw new ServiceException("权益包商品次数已用完，核销失败", SystemCode.ITEM_SOLD_OUT.getCode());
                }
            }
            //如果是赠送包，要判断源权益包是否还有使用次数
            MiniAccountPackageGoods sourcePackageGoods = null;
            if(null != accountPackageGoods.getSourceMiniAccountPackageGoodsId()){
                sourcePackageGoods = this.miniAccountPackageGoodsService.getById(accountPackageGoods.getSourceMiniAccountPackageGoodsId());
                if(null != sourcePackageGoods){
                    Integer sourceAlreadyTimes = sourcePackageGoods.getAlreadyTimes()+1;
                    int sourceUseableTimes = sourcePackageGoods.getAllTimes() - sourceAlreadyTimes;
                    if(sourceUseableTimes < 0){
                        throw new ServiceException("源权益包商品次数已用完，核销失败", SystemCode.ITEM_SOLD_OUT.getCode());
                    }
                }
            }

            //判断该权益包商品是否有单次频率天数限制
            Integer daysRate = accountPackageGoods.getDaysRate();
            if(null != daysRate && daysRate > 0){
                Date now = new Date();
                // 查询该用户使用权益包商品最新一次的核销时间，只查询一条记录
                LambdaQueryWrapper<MiniAccountPackageGoodsCode> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MiniAccountPackageGoodsCode::getMiniAccountPackageGoodsId, accountPackageGoods.getId())
                        .eq(MiniAccountPackageGoodsCode::getUserId, packageGoodsCode.getUserId())
                        .eq(MiniAccountPackageGoodsCode::getStatus, PackageStatusEnum.USED.getStatus())
                        .orderByDesc(MiniAccountPackageGoodsCode::getVerifyTime);
                List<MiniAccountPackageGoodsCode> miniAccountPackageGoodsCodeList = this.list(queryWrapper);
                if(CollectionUtil.isNotEmpty(miniAccountPackageGoodsCodeList)){
                    MiniAccountPackageGoodsCode miniAccountPackageGoodsCode = miniAccountPackageGoodsCodeList.get(0);
                    String verifyDay = DateUtil.format(miniAccountPackageGoodsCode.getVerifyTime(), "yyyy-MM-dd");
                    if(DateUtil.betweenDay(miniAccountPackageGoodsCode.getVerifyTime(), now, true) < daysRate){
                        throw new ServiceException("该用户在" + verifyDay + "已使用过该商品，请勿重复使用", SystemCode.DATA_EXPIRED.getCode());
                    }
                }
            }

            packageGoodsCode.setCanTimes(useableTimes);
            packageGoodsCode.setStatus(PackageStatusEnum.USED.getStatus());
            packageGoodsCode.setVerifyTime(new Date());
            packageGoodsCode.setVerifyUserId(Long.parseLong(verifyUserId));

            //核销人门店关联信息
            RelationInfoVo relationInfoVo = remoteMiniInfoService.getRelationInfoByAccountId(verifyUserId);

            packageGoodsCode.setEmployeeId(relationInfoVo.getEmployeeId());//职员id
            packageGoodsCode.setEmpId(relationInfoVo.getEmployeeOutId());//职员外部系统id
            packageGoodsCode.setEmpName(relationInfoVo.getEmployeeName());//职员名称

            packageGoodsCode.setDepartmentId(relationInfoVo.getDepartmentId());//部门id
            packageGoodsCode.setDepartmentCode(relationInfoVo.getDepartmentCode());//部门标识
            packageGoodsCode.setDepartmentName(relationInfoVo.getDepartmentName());//部门名称

            packageGoodsCode.setStoreFrontId(relationInfoVo.getStoreFrontId());//门店id
            packageGoodsCode.setStoreFrontCode(relationInfoVo.getStoreFrontCode());//门店标识
            packageGoodsCode.setStoreFrontName(relationInfoVo.getStoreFrontName());//门店名称

            packageGoodsCode.setAccountId(relationInfoVo.getAccountId());//核销人用户ID
            packageGoodsCode.setAccountName(relationInfoVo.getAccountName());//核销人名称

            packageGoodsCode.setStockId(relationInfoVo.getStockId());//仓库id
            packageGoodsCode.setStockCode(relationInfoVo.getStockCode());//仓库标识
            packageGoodsCode.setStockName(relationInfoVo.getStockName());//仓库名称
            packageGoodsCode.setVerifyGoodsId(verifyGoodsId);//核销商品id
            packageGoodsCode.setVerifySkuId(verifySkuId);//核销商品规格id


            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            String format = sdf.format(date);
            String verifyNo = "HXTB"+format;
            packageGoodsCode.setVerifyNo(verifyNo);
            this.updateById(packageGoodsCode);
            //获取核销商品信息
            ProductVo product = remoteGoodsService.findProductById(verifyGoodsId);
            //获取核销商品规格
            SkuStock skuStock = remoteGoodsService.findSkuStockById(verifySkuId);

            //处理库存
            List<Long> skuIds=new ArrayList<>();
            skuIds.add(skuStock.getId());
            List<ProductStock> productStockList=remoteGoodsService.productStockBySkuIds(skuIds,Long.valueOf(packageGoodsCode.getStockId()));
            if(CollectionUtils.isEmpty(productStockList)){
                throw new ServiceException("库存数量不足，核销失败！");
            }

            //库存数据
            ProductStock productStock = productStockList.get(0);
            BigDecimal stock=productStock.getStock();
            //商品数量
            Integer num = 1;
            //剩余数量
            BigDecimal number =stock.subtract(new BigDecimal(num));
            productStock.setStock(number);
            if(number.compareTo(new BigDecimal("0")) <= 0){
                throw new ServiceException("库存数量不足，核销失败！");
            }
            List<ProductStock>productStocks = new ArrayList<>();
            productStocks.add(productStock);
            boolean success=remoteGoodsService.batchProductSubtractStock(productStocks);
            if(!success){
                throw new ServiceException("修改商品仓库库存失败，核销失败");
            }
            OperateStockDto operateStockDto = new OperateStockDto();
            operateStockDto.setSkuId(productStock.getSkuId());
            operateStockDto.setNumber(1);
            List<OperateStockDto> operateStockDtoList = new ArrayList<>();
            operateStockDtoList.add(operateStockDto);
            //扣除库存
            boolean goodsSuccess = remoteGoodsService.batchSubtractStock(operateStockDtoList);
            if(!goodsSuccess){
                throw new ServiceException("修改商品总库存失败，核销失败");
            }

            CreateOrderOutStockMessage createOrderOutStockMessage = new CreateOrderOutStockMessage();
            createOrderOutStockMessage.setOrderId(accountPackageOrder.getOrderId()+"");

            createOrderOutStockMessage.setEmployeeId(relationInfoVo.getEmployeeId());//职员id
            createOrderOutStockMessage.setEmployeeOutId(relationInfoVo.getEmployeeOutId());//职员标识
            createOrderOutStockMessage.setEmployeeName(relationInfoVo.getEmployeeName());//职员名称

            createOrderOutStockMessage.setDepartmentId(relationInfoVo.getDepartmentId());//部门id
            createOrderOutStockMessage.setDepartmentCode(relationInfoVo.getDepartmentCode());//部门标识
            createOrderOutStockMessage.setDepartmentName(relationInfoVo.getDepartmentName());//部门名称

            createOrderOutStockMessage.setStoreFrontId(relationInfoVo.getStoreFrontId());//门店id
            createOrderOutStockMessage.setStoreFrontCode(relationInfoVo.getStoreFrontCode());//门店标识
            createOrderOutStockMessage.setStoreFrontName(relationInfoVo.getStoreFrontName());//门店名称

            createOrderOutStockMessage.setAccountId(relationInfoVo.getAccountId());//用户id
            createOrderOutStockMessage.setAccountName(relationInfoVo.getAccountName());//用户名称

            createOrderOutStockMessage.setStockId(relationInfoVo.getStockId());//仓库id
            createOrderOutStockMessage.setStockCode(relationInfoVo.getStockCode());//仓库标识
            createOrderOutStockMessage.setStockName(relationInfoVo.getStockName());//仓库名称


            createOrderOutStockMessage.setBuyStockId(accountPackageOrder.getStockId());//购买权益包仓库id
            createOrderOutStockMessage.setBuyStockCode(accountPackageOrder.getStockCode());//购买权益包仓库标识
            createOrderOutStockMessage.setBuyStockName(accountPackageOrder.getStockName());//购买权益包仓库名称


            createOrderOutStockMessage.setUserId(accountPackageOrder.getUserId()+"");
            createOrderOutStockMessage.setSendStatus(0);
            createOrderOutStockMessage.setShopId(accountPackageOrder.getShopId());
            createOrderOutStockMessage.setTenantId(accountPackageOrder.getTenantId());
            createOrderOutStockMessage.setOutTime(DateUtil.format(date,"yyyy-MM-dd"));

            StockOutOrderDetDto stockOutOrderDetDto = new StockOutOrderDetDto();
            stockOutOrderDetDto.setProductId(product.getId()+"");
            stockOutOrderDetDto.setSkuId(skuStock.getId()+"");
            stockOutOrderDetDto.setProductName(product.getName());
            stockOutOrderDetDto.setProductCode(product.getClassCode());
            stockOutOrderDetDto.setNumber(1);
            //出库单单价取调后单价
            stockOutOrderDetDto.setPrice(accountPackageGoods.getAdjustmentPrice());

            BigDecimal qty = BigDecimal.valueOf(1);
            BigDecimal price = BigDecimal.ZERO;
            if(accountPackageGoods.getAdjustmentPrice()!=null){
                price = accountPackageGoods.getAdjustmentPrice();
            }
            BigDecimal allAmount = BigDecimal.ZERO;
            stockOutOrderDetDto.setAmount(qty.multiply(price));
            allAmount = allAmount.add(stockOutOrderDetDto.getAmount());
            List<StockOutOrderDetDto>detDtoList = new ArrayList<>();
            detDtoList.add(stockOutOrderDetDto);
            createOrderOutStockMessage.setAllAmount(allAmount);
            createOrderOutStockMessage.setDetList(detDtoList);

            //发送生成出库单消息
            sender.sendOrderStockOutMessage(createOrderOutStockMessage);


            // 过期毫秒数
            Date endDate = Date.from(accountPackageGoods.getEndTime().atZone( ZoneId.systemDefault()).toInstant());
            long time = DateUtil.between(new Date(), endDate, DateUnit.MS);

            // 用户权益包商品记录改为已使用
            accountPackageGoods.setAlreadyTimes(alreadyTimes);
            //判断是否为无限次数，不是则判断是否使用完
            if(accountPackageGoods.getNotTime()!=1){
                if(accountPackageGoods.getAlreadyTimes()>=accountPackageGoods.getAllTimes()){
                    accountPackageGoods.setStatus(PackageStatusEnum.USED.getStatus());
                }
            }
            this.miniAccountPackageGoodsService.updateById(accountPackageGoods);

            //判断互斥商品，如果有则将其已使用次数改为购买次数
            LambdaQueryWrapper<MiniAccountPackageGoods>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MiniAccountPackageGoods::getMutexGoodId,sourcePackageGoods != null ? sourcePackageGoods.getMutexGoodId() : accountPackageGoods.getMutexGoodId());
            wrapper.ne(MiniAccountPackageGoods::getId,sourcePackageGoods != null ? sourcePackageGoods.getId() : accountPackageGoods.getId());
            wrapper.eq(MiniAccountPackageGoods::getMainId,sourcePackageGoods != null ? sourcePackageGoods.getMainId() : accountPackageGoods.getMainId());
            List<MiniAccountPackageGoods> list = this.miniAccountPackageGoodsService.list(wrapper);
            if(list!=null && !list.isEmpty()){
                for (MiniAccountPackageGoods miniAccountPackageGoods : list) {
                    miniAccountPackageGoods.setStatus(PackageStatusEnum.EXPIRED.getStatus());
                    miniAccountPackageGoods.setAlreadyTimes(miniAccountPackageGoods.getAllTimes());
                    this.miniAccountPackageGoodsService.updateById(miniAccountPackageGoods);
                }
            }
            //如果是赠送包，修改源权益包的使用次数
            if(null != accountPackageGoods.getSourceMiniAccountPackageGoodsId()){
                sourcePackageGoods = this.miniAccountPackageGoodsService.getById(accountPackageGoods.getSourceMiniAccountPackageGoodsId());
                if(null != sourcePackageGoods){
                    sourcePackageGoods.setAlreadyTimes(sourcePackageGoods.getAlreadyTimes()+1);
                    //判断是否为无限次数，不是则判断是否使用完
                    if(sourcePackageGoods.getNotTime()!=1){
                        if(sourcePackageGoods.getAlreadyTimes()>=sourcePackageGoods.getAllTimes()){
                            sourcePackageGoods.setStatus(PackageStatusEnum.USED.getStatus());
                        }
                    }
                    this.miniAccountPackageGoodsService.updateById(sourcePackageGoods);
                }
            }

            // 设置redis记录为已使用
            String result = accountRedis.setNxPx(codeKey, PromotionStatusEnum.USED.getStatus().toString(), time);
            if(!CommonConstants.REDIS_SUCCESS.equalsIgnoreCase(result)){
                // 失败
                throw new ServiceException("核销失败，请重试", SystemCode.DATA_EXPIRED.getCode());
            }
            String accountPackageGoodsKey = RedisKey.getAccountPackageGoodsKey(accountPackageOrder.getUserId() + "", accountPackageGoods.getId() + "");
            accountRedis.del(accountPackageGoodsKey);
        }
        return packageGoodsCode;
    }

    @Override
    public IPage<ShopPackageGoodsCodeVo> pageShopUserVerifyCode(MiniAccountPackageGoodsCodeParam param) {
        if(null != param.getStartTime()){
            String startTimeStr = DateUtil.format(param.getStartTime(), "yyyy-MM-dd 00:00:00");
            param.setStartTime(DateUtil.parse(startTimeStr));
        }
        if(null != param.getEndTime()){
            String endTimeStr = DateUtil.format(param.getEndTime(), "yyyy-MM-dd 23:59:59");
            param.setEndTime(DateUtil.parse(endTimeStr));
        }
        IPage<ShopPackageGoodsCodeVo>page = this.baseMapper.selectShopVerifyList(new Page<>(param.getCurrent(),param.getSize()),
                param);
        List<ShopPackageGoodsCodeVo> records = page.getRecords();
        if(records!=null&&records.size()>0){
            for (ShopPackageGoodsCodeVo record : records) {
                String packageOrderId = record.getPackageOrderId();
                LambdaQueryWrapper<MiniAccountPackageGoods>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(MiniAccountPackageGoods::getMainId,packageOrderId);
                //获取权益包中所有未核销商品的汇总数量
                List<MiniAccountPackageGoods> list = miniAccountPackageGoodsService.list(wrapper);
                int num = 0;
                if(list!=null&&list.size()>0){
                    for (MiniAccountPackageGoods miniAccountPackageGoods : list) {
                        if(miniAccountPackageGoods.getNotTime()==0
                                &&miniAccountPackageGoods.getAllTimes()>miniAccountPackageGoods.getAlreadyTimes()){
                            num+=1;
                        }
                    }
                }
                record.setGoodsNum(num);
            }
        }
        return page;
    }

    @Override
    public IPage<PackageGoodsCodeDetailVo> pagePackageGoodsCodeDetail(PackageGoodsCodeDetailParam param) {
        if(null != param.getStartTime()){
            String startTimeStr = DateUtil.format(param.getStartTime(), "yyyy-MM-dd 00:00:00");
            param.setStartTime(DateUtil.parse(startTimeStr));
        }
        if(null != param.getEndTime()){
            String endTimeStr = DateUtil.format(param.getEndTime(), "yyyy-MM-dd 23:59:59");
            param.setEndTime(DateUtil.parse(endTimeStr));
        }
        IPage<PackageGoodsCodeDetailVo>page = this.baseMapper.pagePackageGoodsCodeDetail(new Page<>(param.getCurrent(),param.getSize()),
                param);

        return page;
    }

    @Override
    public void exportPackageGoodsCodeDetail(PackageGoodsCodeDetailParam param) {
        HuToolExcelUtils.exportParamToMax( param);
        IPage<PackageGoodsCodeDetailVo> voIPage = this.pagePackageGoodsCodeDetail(param);
        HuToolExcelUtils.exportData(voIPage.getRecords(),"核销明细",source->{
            PackageGoodsCodeDetailExcelVo detailVo = new PackageGoodsCodeDetailExcelVo();
            String format ="yyyy-MM-dd";
            detailVo.setPackageShowTime(
                    DateUtil.format(source.getPackageShowStartTime(),format) +"~"+
                            DateUtil.format(source.getPackageShowEndTime(), format));
            detailVo.setPackageTime( DateUtil.format(source.getPackageStartTime(),format) +"~"+
                    DateUtil.format(source.getPackageEndTime(), format));
            detailVo.setVerifyTime(DateUtil.format(source.getVerifyTime(), "yyyy-MM-dd HH:mm:ss"));
            if (source.getMutexFlag()!= null){
                detailVo.setMutexFlag(source.getMutexFlag()==1?"是":"否");
            }
            if (source.getNotTime()!= null){
                detailVo.setNotTime(source.getNotTime()==1?"是":"否");
            }
            return detailVo;
        });


    }

    @Override
    public MiniAccountPackageGoodsVo getPackageGoodsByCode(String verifyCode) {
        MiniAccountPackageGoodsVo miniAccountPackageGoodsVo = new MiniAccountPackageGoodsVo();
        String codeKey = RedisKey.getPackageGoodsCodeKey(verifyCode);
        AccountRedis accountRedis = new AccountRedis();
        String status = accountRedis.get(codeKey);
        if(StrUtil.isNotBlank(status)){
            throw new ServiceException("此码已核销，不能重复核销", SystemCode.DATA_EXPIRED.getCode());
        }else{
            //未使用，则进行核销
            LambdaQueryWrapper<MiniAccountPackageGoodsCode> codeWrapper = new LambdaQueryWrapper<>();
            codeWrapper.eq(MiniAccountPackageGoodsCode::getVerifyCode, verifyCode).eq(MiniAccountPackageGoodsCode::getStatus, PackageStatusEnum.UN_USE.getStatus());
            List<MiniAccountPackageGoodsCode> codeList = this.list(codeWrapper);
            if(CollectionUtil.isEmpty(codeList)){
                throw new ServiceException("该核销码不存在，无法核销", SystemCode.DATA_MANY.getCode());
            }
            if(CollectionUtil.isNotEmpty(codeList) && codeList.size() != 1){
                throw new ServiceException("此码暂不能核销，请联系运营平台管理员", SystemCode.DATA_MANY.getCode());
            }
            MiniAccountPackageGoodsCode miniAccountPackageGoodsCode = codeList.get(0);
            MiniAccountPackageGoods miniAccountPackageGoods = miniAccountPackageGoodsService.getById(miniAccountPackageGoodsCode.getMiniAccountPackageGoodsId());
            BeanUtil.copyProperties(miniAccountPackageGoods, miniAccountPackageGoodsVo);
            miniAccountPackageGoodsVo.setUserId(miniAccountPackageGoodsCode.getUserId());
        }
        return miniAccountPackageGoodsVo;
    }

    @Override
    public void exportShopPassTicketCode(MiniAccountPackageGoodsCodeParam param) {
        HuToolExcelUtils.exportParamToMax(param);
        IPage<ShopPackageGoodsCodeVo> voIPage = this.pageShopUserVerifyCode(param);
        HuToolExcelUtils.exportData(voIPage.getRecords(),"核销列表",source->{
            ShopPackageGoodsCodeExcelVo target = new ShopPackageGoodsCodeExcelVo();
            target.setVerifyTime(DateUtil.format(source.getVerifyTime(), "yyyy-MM-dd HH:mm:ss"));
            target.setProductNumber(1);//固定为1？
            return target;
        });

    }

    /**
     * 分页获取小程序用户核销记录和被其赠送出去已被核销的记录
     * @param param
     * @return
     */
    @Override
    public IPage<ApiPackageGoodsCodeVo> pageApiPackageGoodsCode(ApiPackageGoodsCodeParam param) {
        String userId = CurUserUtil.getMiniReqeustAccountInfo().getShopUserId();
        param.setUserId(userId);
        IPage<ApiPackageGoodsCodeVo> page = this.baseMapper.pageApiPackageGoodsCode(new Page<>(param.getCurrent(), param.getSize()), param);
        return page;
    }

    /**
     * 门店、员工核销权益包数量汇总
     * @param manageVerifyPackageGoodsStaticDto
     * @return
     */
    @Override
    public List<ManageVerifyPackageGoodsStaticVo> manageVerifyPackageGoodsStatic(ManageVerifyPackageGoodsStaticDto manageVerifyPackageGoodsStaticDto) {
        List<ManageVerifyPackageGoodsStaticVo> manageVerifyPackageGoodsStaticVoList = this.baseMapper.manageVerifyPackageGoodsStatic(manageVerifyPackageGoodsStaticDto);
        return manageVerifyPackageGoodsStaticVoList;
    }

    /**
     * 门店、员工核销权益包数量汇总合计
     * @param manageVerifyPackageGoodsStaticDto
     * @return
     */
    @Override
    public ManageVerifyPackageGoodsStaticVo manageVerifyPackageGoodsStaticTotal(ManageVerifyPackageGoodsStaticDto manageVerifyPackageGoodsStaticDto) {
        // 查询合计记录
        ManageVerifyPackageGoodsStaticVo manageVerifyPackageGoodsStaticVo = this.baseMapper.manageVerifyPackageGoodsStaticTotal(manageVerifyPackageGoodsStaticDto);
        return manageVerifyPackageGoodsStaticVo;
    }

    @Override
    public MiniAccountPackageGoodsCodeVo pcGetCode(MiniAccountPackageGoodsDto miniAccountPackageGoodsDto) {
        MiniAccountPackageGoodsCodeVo codeVo = new MiniAccountPackageGoodsCodeVo();
        MiniAccountPackageGoodsCode code = this.pcAdd(miniAccountPackageGoodsDto);
        BeanUtil.copyProperties(code, codeVo);
        QrConfig qrConfig = QrConfig.create();
        byte[] bytes = QrCodeUtil.generatePng(codeVo.getVerifyCode(), qrConfig);
        String qrCode = Base64.encode(bytes);
        codeVo.setQrCode(qrCode);
        return codeVo;
    }
    private MiniAccountPackageGoodsCode pcAdd(MiniAccountPackageGoodsDto miniAccountPackageGoodsDto) {
        //查询用户权益包商品记录
        MiniAccountPackageGoods accountPackageGoods = miniAccountPackageGoodsService.getById(miniAccountPackageGoodsDto.getMiniAccountPackageGoodsId());

        if(null == accountPackageGoods){
            throw new ServiceException("权益包商品不存在", SystemCode.DATA_NOT_EXIST.getCode());
        }
        MiniAccountPackageOrder miniAccountPackageOrder = miniAccountPackageOrderService.getById(accountPackageGoods.getMainId());
        if(null == miniAccountPackageOrder){
            throw new ServiceException("权益包订单不存在", SystemCode.DATA_NOT_EXIST.getCode());
        }
        if(accountPackageGoods.getStatus() == PackageStatusEnum.EXPIRED.getStatus().intValue()){
            throw new ServiceException("已失效", SystemCode.DATA_EXPIRED.getCode());
        }
        Long userId = miniAccountPackageOrder.getUserId();
        String verifyCode = RandomUtil.randomNumbers(10);

        // 查询redis是否已存有此用户通行票记录的验证码
        String userKey = RedisKey.getAccountPackageGoodsKey(userId+"",accountPackageGoods.getId() + "");

        AccountRedis accountRedis = new AccountRedis();
        Date startDate = Date.from(accountPackageGoods.getStartTime().atZone( ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(accountPackageGoods.getEndTime().atZone( ZoneId.systemDefault()).toInstant());
        // 过期毫秒数
        long time = DateUtil.between(new Date(), endDate, DateUnit.MS);
        MiniAccountPackageGoodsCode miniAccountPackageGoodsCode = new MiniAccountPackageGoodsCode();
        miniAccountPackageGoodsCode.setUserId(userId+"");
        miniAccountPackageGoodsCode.setProductId(accountPackageGoods.getProductId());
        miniAccountPackageGoodsCode.setMiniAccountPackageGoodsId(miniAccountPackageGoodsDto.getMiniAccountPackageGoodsId());
        miniAccountPackageGoodsCode.setStartTime(startDate);
        miniAccountPackageGoodsCode.setEndTime(endDate);
        miniAccountPackageGoodsCode.setShopId(accountPackageGoods.getShopId());
        miniAccountPackageGoodsCode.setStatus(PackageStatusEnum.UN_USE.getStatus());
        miniAccountPackageGoodsCode.setPackageId(accountPackageGoods.getPackageId());
        // 设置值成功返回“OK”
        String result = accountRedis.setNxPx(userKey, verifyCode, time);
        if(!CommonConstants.REDIS_SUCCESS.equalsIgnoreCase(result)){
            // 不成功，表示redis已经有值，无需重新生成
            verifyCode = accountRedis.get(userKey);
            miniAccountPackageGoodsCode.setVerifyCode(verifyCode);
            return miniAccountPackageGoodsCode;
        }
        miniAccountPackageGoodsCode.setVerifyCode(verifyCode);
        this.save(miniAccountPackageGoodsCode);
        return miniAccountPackageGoodsCode;
    }
}
