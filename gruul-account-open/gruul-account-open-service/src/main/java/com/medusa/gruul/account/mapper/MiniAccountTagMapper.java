package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccountTag;
import com.medusa.gruul.account.api.model.param.ChooseTagParam;
import com.medusa.gruul.account.api.model.vo.ChooseTagVo;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 用户标签表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-18
 */
public interface MiniAccountTagMapper extends BaseMapper<MiniAccountTag> {

    /**
     * 获取可添加的标签
     * @param page
     * @param param
     * @return
     */
    IPage<ChooseTagVo> getChooseTag(Page<ChooseTagVo> page, @Param("param") ChooseTagParam param);

    /**
     * 根据根据标签id获取可添加标签详情
     * @param tagId
     * @return
     */
    ChooseTagVo getChooseTagById(@Param("tagId")String tagId);
}
