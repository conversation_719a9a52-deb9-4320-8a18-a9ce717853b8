package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:13 2025/7/21
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MiniAccountGoldenChangeParam 实体", description = "会员-金豆变动明细 param")
@Data
public class MiniAccountGoldenChangeParam extends QueryParam {

    @ApiModelProperty(value = "金豆变动明细id")
    private Long id;

    @ApiModelProperty(value = "用户名称")
    private String nikeName;

    @ApiModelProperty(value = "用户手机")
    private String phone;

    @ApiModelProperty(value = "审核人用户名称")
    private String auditUserName;

    @ApiModelProperty(value = "审核状态:100->待审核，101->审核通过，200->驳回")
    private Integer auditStatus;

}
