package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会员-通行票查询实体
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MiniAccountPassTicketParam 实体", description = "会员-通行票 param")
@Data
public class MiniAccountPassTicketParam extends QueryParam {


    /**
     * 状态:100->未用;101->已用;200->已失效
     */
    @ApiModelProperty(value = "状态:100->未用;101->已用;200->已失效")
    private Integer status;

}
