package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MemberType;
import com.medusa.gruul.account.model.param.MemberTypeParam;
import com.medusa.gruul.account.model.vo.MemberTypeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:39 2025/5/20
 */
@Mapper
public interface MemberTypeMapper extends BaseMapper<MemberType> {

    /**
     * 获取会员类型列表
     * @return
     */
    List<MemberTypeVo> getMemberType(@Param("param") MemberTypeParam param);

    /**
     * 根据用户id获取会员类型列表
     * @param userId
     * @return
     */
    List<MemberTypeVo> getMemberTypeList(@Param("userId")String userId);
}
