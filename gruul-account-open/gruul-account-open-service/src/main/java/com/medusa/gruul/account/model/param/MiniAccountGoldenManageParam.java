package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:55 2025/7/21
 */
@Data
@ApiModel(value = "MiniAccountGoldenManageParam 实体", description = "后台查询金豆明细param")
public class MiniAccountGoldenManageParam extends QueryParam {

    @ApiModelProperty(value = "用户名称")
    private String nikeName;

    @ApiModelProperty(value = "用户电话")
    private String phone;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "佣金类型:1->佣金；3->平级；4->级差；5->团队；6->循环分佣；104->订单消费；105->订单取消；400->金豆手动变更；")
    private Integer commissionType;

    @ApiModelProperty(value = "变更方式:1->增加;2->减少")
    private Integer way;
    @ApiModelProperty(value = "分佣标题")
    private String remark;

}
