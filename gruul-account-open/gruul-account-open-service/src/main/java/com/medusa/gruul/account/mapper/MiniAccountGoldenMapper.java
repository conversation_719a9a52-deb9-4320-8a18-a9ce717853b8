package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccountGolden;
import com.medusa.gruul.account.model.param.MiniAccountCommissionManageParam;
import com.medusa.gruul.account.model.param.MiniAccountCommissionParam;
import com.medusa.gruul.account.model.param.MiniAccountGoldenManageParam;
import com.medusa.gruul.account.model.param.MiniAccountGoldenParam;
import com.medusa.gruul.account.model.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:24 2025/6/11
 */
@Mapper
public interface MiniAccountGoldenMapper extends BaseMapper<MiniAccountGolden> {
    /**
     * 获取用户金豆信息
     * @param userId
     * @return
     */
    UserGoldenVo getUserGoldenVo(@Param(value = "userId")String userId);

    /**
     * 小程序分页查询金豆明细
     * @param page
     * @param param
     * @return
     */
    IPage<MiniAccountGoldenVo> pageMyGolden(Page<MiniAccountGoldenVo> page, @Param("paramMap") MiniAccountGoldenParam param);

    /**
     * 分页查询金豆明细
     * @param page
     * @param param
     * @return
     */
    IPage<MiniAccountGoldenManageVo> searchMiniAccountGoldenDet(Page<MiniAccountGoldenManageVo> page, @Param("paramMap") MiniAccountGoldenManageParam param);
}
