package com.medusa.gruul.account.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.entity.MiniAccountBank;
import com.medusa.gruul.account.mapper.MiniAccountBankMapper;
import com.medusa.gruul.account.model.dto.MiniAccountBankDto;
import com.medusa.gruul.account.model.param.MiniAccountBankParam;
import com.medusa.gruul.account.model.vo.MiniAccountBankVo;
import com.medusa.gruul.account.service.IMiniAccountBankService;
import com.medusa.gruul.account.service.IMiniAccountService;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.dto.CurUserDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:20 2025/5/27
 */
@Service
public class MiniAccountBankServiceImpl extends ServiceImpl<MiniAccountBankMapper, MiniAccountBank>implements IMiniAccountBankService {

    @Autowired
    private IMiniAccountService miniAccountService;

    /**
     * 新增用户银行卡
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMiniAccountBank(MiniAccountBankDto dto) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = miniAccountService.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(StringUtils.isEmpty(dto.getBankName())){
            throw new ServiceException("开户行不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(StringUtils.isEmpty(dto.getBankNo())){
            throw new ServiceException("卡号不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(StringUtils.isEmpty(dto.getBankUserName())){
            throw new ServiceException("开户人不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(dto.getBankNo().length()<16){
            throw new ServiceException("卡号必须大于16位数！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        String regex = "^[a-zA-Z0-9]+$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(dto.getBankNo());
        if(!matcher.matches()){
            throw new ServiceException("卡号只允许填写数字或英文！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        LambdaQueryWrapper<MiniAccountBank>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountBank::getBankNo,dto.getBankNo());
        int count = this.count(wrapper);
        if(count>0){
            throw new ServiceException("卡号重复，请检查！", SystemCode.DATA_NOT_EXIST_CODE);
        }

        MiniAccountBank miniAccountBank = new MiniAccountBank();
        BeanUtils.copyProperties(dto,miniAccountBank);
        miniAccountBank.setCreateUserName(miniAccount.getNikeName());
        miniAccountBank.setCreateUserId(miniAccount.getUserId());
        miniAccountBank.setUserId(miniAccount.getUserId());
        this.save(miniAccountBank);

    }

    /**
     * 编辑用户银行卡
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editMiniAccountBank(MiniAccountBankDto dto) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = miniAccountService.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(StringUtils.isEmpty(dto.getId())){
            throw new ServiceException("银行卡id不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        if(StringUtils.isNotEmpty(dto.getBankNo())){
            if(dto.getBankNo().length()<16){
                throw new ServiceException("卡号必须大于16位数！", SystemCode.DATA_NOT_EXIST_CODE);
            }
            String regex = "^[a-zA-Z0-9]+$";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(dto.getBankNo());
            if(!matcher.matches()){
                throw new ServiceException("卡号只允许填写数字或英文！", SystemCode.DATA_NOT_EXIST_CODE);
            }
        }
        MiniAccountBank miniAccountBank = this.getById(dto.getId());

        if(miniAccountBank == null || miniAccountBank.equals("")){
            throw new ServiceException("银行卡不存在！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        LambdaQueryWrapper<MiniAccountBank>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniAccountBank::getBankNo,dto.getBankNo());
        wrapper.ne(MiniAccountBank::getId,dto.getId());
        int count = this.count(wrapper);
        if(count>0){
            throw new ServiceException("卡号重复，请检查！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        BeanUtils.copyProperties(dto,miniAccountBank);
        miniAccountBank.setLastModifyUserName(miniAccount.getNikeName());
        miniAccountBank.setLastModifyUserId(miniAccount.getUserId());
        this.updateById(miniAccountBank);
    }

    /**
     * 删除用户银行卡
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeMiniAccountBank(MiniAccountBankDto dto) {
        if(StringUtils.isEmpty(dto.getId())){
            throw new ServiceException("银行卡id不能为空！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        MiniAccountBank miniAccountBank = this.getById(dto.getId());

        if(miniAccountBank == null || miniAccountBank.equals("")){
            throw new ServiceException("银行卡不存在！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        this.removeById(dto.getId());
    }

    /**
     * 分页查询银行卡
     * @param param
     * @return
     */
    @Override
    public PageUtils<MiniAccountBankVo> queryList(MiniAccountBankParam param) {
        CurUserDto curUser = CurUserUtil.getHttpCurUser();
        MiniAccount miniAccount = miniAccountService.getByShopUserId(curUser.getUserId());
        if (miniAccount == null) {
            throw new ServiceException("数据错误！", SystemCode.DATA_NOT_EXIST_CODE);
        }
        param.setUserId(miniAccount.getUserId());
        IPage<MiniAccountBankVo>page = this.baseMapper.queryList(new Page<MiniAccountBankVo>(param.getCurrent(),param.getSize()),param);
        for (MiniAccountBankVo miniAccountBankVo : page.getRecords()) {
            String bankNo = miniAccountBankVo.getBankNo();
            if(StringUtils.isNotEmpty(bankNo)){
                String leftText = bankNo.substring(0,4);
                int length = bankNo.length();
                String rightText =bankNo.substring(length-4,length);
                String centerText = "";
                for(int i=0;i<length-8;i++){
                    centerText+="*";
                }
                miniAccountBankVo.setBankNo(leftText+centerText+rightText);
            }

        }
        return new PageUtils<>(page);
    }

    @Override
    public List<MiniAccountBankVo> getMiniAccountBank(String userId) {
        List<MiniAccountBankVo>bankList = this.baseMapper.getMiniAccountBank(userId);
        return bankList;
    }


}
