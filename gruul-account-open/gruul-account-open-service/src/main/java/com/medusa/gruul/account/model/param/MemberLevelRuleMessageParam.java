package com.medusa.gruul.account.model.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:58 2025/5/21
 */
@Data
@ApiModel(value = "MemberLevelRuleMessageParam 实体", description = "会员等级规则查询 param")
public class MemberLevelRuleMessageParam {

    /**
     * 会员类型id
     */
    @ApiModelProperty(value = "会员类型id")
    private Long memberTypeId;

}
