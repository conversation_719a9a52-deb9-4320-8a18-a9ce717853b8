package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccountCoupon;
import com.medusa.gruul.account.api.entity.MiniAccountPassTicket;
import com.medusa.gruul.account.api.model.param.MiniAccountCouponSearchParam;
import com.medusa.gruul.account.api.model.vo.MiniAccountCouponSearchVo;
import com.medusa.gruul.account.model.param.DistributionOrderParam;
import com.medusa.gruul.account.model.vo.DistributionOrderVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:38 2024/8/27
 */
@Repository
public interface MiniAccountCouponMapper extends BaseMapper<MiniAccountCoupon> {

    /**
     * 获取用户分销列表
     * @param page
     * @param miniAccountCouponSearchParam
     * @return
     */
    IPage<MiniAccountCouponSearchVo> getMiniAccountCouponSearchVo(Page<MiniAccountCouponSearchVo> page,
                                                                  @Param("paramMap") MiniAccountCouponSearchParam miniAccountCouponSearchParam);
}
