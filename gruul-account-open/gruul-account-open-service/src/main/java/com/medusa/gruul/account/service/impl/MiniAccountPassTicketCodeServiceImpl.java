package com.medusa.gruul.account.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccountPassTicket;
import com.medusa.gruul.account.api.entity.MiniAccountPassTicketCode;
import com.medusa.gruul.account.api.model.vo.ShopPassTicketCodeVo;
import com.medusa.gruul.account.api.model.vo.ShopsPassTicketCodeExcelVo;
import com.medusa.gruul.account.conf.AccountRedis;
import com.medusa.gruul.account.constant.RedisKey;
import com.medusa.gruul.account.mapper.MiniAccountPassTicketCodeMapper;
import com.medusa.gruul.account.model.dto.MiniAccountPassTicketCodeDto;
import com.medusa.gruul.account.api.model.param.MiniAccountPassTicketCodeParam;
import com.medusa.gruul.account.model.vo.MiniAccountPassTicketCodeVo;
import com.medusa.gruul.account.service.IMiniAccountPassTicketCodeService;
import com.medusa.gruul.account.service.IMiniAccountPassTicketService;
import com.medusa.gruul.afs.api.entity.AfsOrder;
import com.medusa.gruul.afs.api.enums.AfsOrderStatusEnum;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.HuToolExcelUtils;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.data.tenant.ShopContextHolder;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.order.api.entity.Order;
import com.medusa.gruul.order.api.enums.OrderStatusEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;
import com.medusa.gruul.shops.api.entity.ShopsPartner;
import com.medusa.gruul.shops.api.enums.PromotionStatusEnum;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员-通行票验证码服务类
 */
@Service(value = "miniAccountPassTicketCodeServiceImpl")
public class MiniAccountPassTicketCodeServiceImpl  extends ServiceImpl<MiniAccountPassTicketCodeMapper, MiniAccountPassTicketCode> implements IMiniAccountPassTicketCodeService {

    @Autowired
    private IMiniAccountPassTicketService miniAccountPassTicketService;
    @Autowired
    private RemoteShopsService remoteShopsService;
    @Autowired
    private RemoteOrderService remoteOrderService;

    /**
     * 添加记录
     * @param miniAccountPassTicketCodeDto
     * @return
     */
    @Override
    public MiniAccountPassTicketCode add(MiniAccountPassTicketCodeDto miniAccountPassTicketCodeDto) {
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        // 查询用户通行票记录
        MiniAccountPassTicket accountTicket = miniAccountPassTicketService.getById(miniAccountPassTicketCodeDto.getMiniAccountPassTicketId());
        if(null == accountTicket || accountTicket.getUserId().longValue() != Long.parseLong(userId)){
            throw new ServiceException("请先购买此商品", SystemCode.DATA_NOT_EXIST.getCode());
        }
        if(accountTicket.getStatus() == PromotionStatusEnum.EXPIRED.getStatus().intValue()){
            throw new ServiceException("已失效", SystemCode.DATA_EXPIRED.getCode());
        }
        // 判断订单是否存在未完成的售后工单
        AfsOrder afsOrder = remoteOrderService.selectByOrderIdAndProductSkuId(accountTicket.getOrderId(), accountTicket.getPassTicketId());
        if(null != afsOrder && afsOrder.getStatus() != AfsOrderStatusEnum.CLOSE){
            throw new ServiceException("订单对应的售后工单尚未关闭，不能进行此操作", SystemCode.DATA_EXPIRED.getCode());
        }
        String verifyCode = RandomUtil.randomNumbers(10);
        // 查询redis是否已存有此用户通行票记录的验证码
        String userKey = RedisKey.getAccountPassTicketKey(userId,accountTicket.getId() + "");
        AccountRedis accountRedis = new AccountRedis();
        // 过期毫秒数
        long time = DateUtil.between(new Date(), accountTicket.getEndTime(), DateUnit.MS);
        MiniAccountPassTicketCode ticketCode = new MiniAccountPassTicketCode();
        BeanUtil.copyProperties(miniAccountPassTicketCodeDto, ticketCode);
        ticketCode.setUserId(userId);
        ticketCode.setPassTicketId(accountTicket.getPassTicketId());
        ticketCode.setStartTime(accountTicket.getStartTime());
        ticketCode.setEndTime(accountTicket.getEndTime());
        ticketCode.setStatus(PromotionStatusEnum.UN_USE.getStatus());
        // 设置值成功返回“OK”
        String result = accountRedis.setNxPx(userKey, verifyCode, time);
        if(!CommonConstants.REDIS_SUCCESS.equalsIgnoreCase(result)){
            // 不成功，表示redis已经有值，无需重新生成
            verifyCode = accountRedis.get(userKey);
            ticketCode.setVerifyCode(verifyCode);
            return ticketCode;
        }
        ticketCode.setVerifyCode(verifyCode);
        this.save(ticketCode);
        // 设置验证码的redis记录-未核销
        //String codeKey = RedisKey.getPassTicketCodeKey(verifyCode);
        //accountRedis.setNxPx(codeKey, PromotionStatusEnum.UN_USE.getStatus() + "", time);
        return ticketCode;
    }

    /**
     * 获取通行票验证码
     * @param miniAccountPassTicketCodeDto
     * @return
     */
    @Override
    public MiniAccountPassTicketCodeVo getCode(MiniAccountPassTicketCodeDto miniAccountPassTicketCodeDto) {
        MiniAccountPassTicketCodeVo codeVo = new MiniAccountPassTicketCodeVo();
        MiniAccountPassTicketCode code = this.add(miniAccountPassTicketCodeDto);
        BeanUtil.copyProperties(code, codeVo);
        QrConfig qrConfig = QrConfig.create();
        byte[] bytes = QrCodeUtil.generatePng(codeVo.getVerifyCode(), qrConfig);
        String qrCode = Base64.encode(bytes);
        codeVo.setQrCode(qrCode);
        return codeVo;
    }

    /**
     * 核销通行票验证码
     * @param verifyCode 核销码
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MiniAccountPassTicketCode verifyCode(String verifyCode) {
        CurUserDto user = CurUserUtil.getHttpCurUser();
        String verifyUserId = user.getUserId();
        String shopId = user.getShopId();
        if(StrUtil.isBlank(shopId)){
            throw new ServiceException("此账号无法核销", SystemCode.DATA_NOT_EXIST.getCode());
        }
        MiniAccountPassTicketCode ticketCode = null;
        String codeKey = RedisKey.getPassTicketCodeKey(verifyCode);
        AccountRedis accountRedis = new AccountRedis();
        String status = accountRedis.get(codeKey);
        if(StrUtil.isNotBlank(status)){
            throw new ServiceException("此码已核销，不能重复核销", SystemCode.DATA_EXPIRED.getCode());
        }else{
            //未使用，则进行核销
            LambdaQueryWrapper<MiniAccountPassTicketCode> codeWrapper = new LambdaQueryWrapper<>();
            codeWrapper.eq(MiniAccountPassTicketCode::getVerifyCode, verifyCode).eq(MiniAccountPassTicketCode::getStatus, PromotionStatusEnum.UN_USE.getStatus());
            List<MiniAccountPassTicketCode> codeList = this.list(codeWrapper);
            if(CollectionUtil.isEmpty(codeList)){
                throw new ServiceException("该核销码不存在，无法核销", SystemCode.DATA_MANY.getCode());
            }
            if(CollectionUtil.isNotEmpty(codeList) && codeList.size() != 1){
                throw new ServiceException("此码暂不能核销，请联系运营平台管理员", SystemCode.DATA_MANY.getCode());
            }
            ticketCode = codeList.get(0);
            if(DateUtil.compare(new Date(), ticketCode.getEndTime()) > 0){
                throw new ServiceException("此码已过期，无法核销", SystemCode.DATA_EXPIRED.getCode());
            }

            MiniAccountPassTicket miniAccountTicket = this.miniAccountPassTicketService.getById(ticketCode.getMiniAccountPassTicketId());
            // 判断订单是否存在未完成的售后工单
            AfsOrder afsOrder = remoteOrderService.selectByOrderIdAndProductSkuId(miniAccountTicket.getOrderId(), miniAccountTicket.getPassTicketId());
            if(null != afsOrder && afsOrder.getStatus() != AfsOrderStatusEnum.CLOSE){
                throw new ServiceException("订单对应的售后工单尚未关闭，不能进行此操作", SystemCode.DATA_EXPIRED.getCode());
            }
            ShopPassTicket shopPassTicket = remoteShopsService.getPassTicketById(miniAccountTicket.getPassTicketId());
            // 判断商家能否使用
            if(shopPassTicket.getShopFlag()){
                //查询可用商家
                List<ShopsPartner> shopsPartnerList = this.remoteShopsService.getShopPartnerByTicketId(miniAccountTicket.getPassTicketId());

                if(CollectionUtil.isEmpty(shopsPartnerList)){
                    throw new ServiceException("商家无法核销该核销码！", SystemCode.DATA_NOT_EXIST.getCode());
                }
                List<String> shopIdList = shopsPartnerList.stream().map(ShopsPartner::getShopId).collect(Collectors.toList());
                if(CollectionUtil.isEmpty(shopIdList) || !shopIdList.contains(shopId)){
                    throw new ServiceException("商家无法核销该核销码！", SystemCode.DATA_NOT_EXIST.getCode());
                }
            }

            ticketCode.setStatus(PromotionStatusEnum.USED.getStatus());
            ticketCode.setVerifyTime(new Date());
            ticketCode.setVerifyUserId(Long.parseLong(verifyUserId));
            ticketCode.setShopId(shopId);
            this.updateById(ticketCode);
            // 用户通行票记录改为已使用
            miniAccountTicket.setStatus(PromotionStatusEnum.USED.getStatus());
            this.miniAccountPassTicketService.updateById(miniAccountTicket);

            // 订单状态如果是待评价状态，则改为已完成
            Long orderId = miniAccountTicket.getOrderId();
            Order order = remoteOrderService.getOrderById(orderId);
            if(null != order && order.getStatus() == OrderStatusEnum.WAIT_FOR_COMMENT){
                List<Long> orderIds = new ArrayList<>();
                orderIds.add(orderId);
                remoteOrderService.updateOrderStatus(orderIds, OrderStatusEnum.COMPLETE);
            }

            // 过期毫秒数
            long time = DateUtil.between(new Date(), miniAccountTicket.getEndTime(), DateUnit.MS);
            // 查询通行票每个商家使用次数
            int useableTimes = shopPassTicket.getUseableTimes();
            String numKey = RedisKey.getUserPassTicketNumKey(miniAccountTicket.getUserId() + "", miniAccountTicket.getId() + "", shopId);
            accountRedis.setNxPx(numKey, useableTimes + "", time);
            // 次数减一
            Long useableNum = accountRedis.decr(numKey);
            if(useableNum.longValue() < 0){
                throw new ServiceException("该商家次数已用完，核销失败", SystemCode.ITEM_SOLD_OUT.getCode());
            }
            // 设置redis记录为已使用
            String result = accountRedis.setNxPx(codeKey, PromotionStatusEnum.USED.getStatus().toString(), time);
            if(!CommonConstants.REDIS_SUCCESS.equalsIgnoreCase(result)){
                //次数还原回去加1
                accountRedis.incr(numKey);
                // 失败
                throw new ServiceException("核销失败，请重试", SystemCode.DATA_EXPIRED.getCode());
            }
            String accountPassTicketKey = RedisKey.getAccountPassTicketKey(miniAccountTicket.getUserId() + "", miniAccountTicket.getId() + "");
            accountRedis.del(accountPassTicketKey);
        }
        return ticketCode;
    }

    /**
     * 商家用户查询自己核销的记录
     * @param param
     * @return
     */
    @Override
    public IPage<ShopPassTicketCodeVo> pageShopUserVerifyCode(MiniAccountPassTicketCodeParam param) {
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        param.setVerifyUserId(userId);
        IPage<ShopPassTicketCodeVo> page = null;
        if(null != param.getStartTime()){
            String startTimeStr = DateUtil.format(param.getStartTime(), "yyyy-MM-dd 00:00:00");
            param.setStartTime(DateUtil.parse(startTimeStr));
        }
        if(null != param.getEndTime()){
            String endTimeStr = DateUtil.format(param.getEndTime(), "yyyy-MM-dd 23:59:59");
            param.setEndTime(DateUtil.parse(endTimeStr));
        }
        page = this.baseMapper.selectShopUserVerifyList(new Page<>(param.getCurrent(), param.getSize()), param);
        return page;
    }

    /**
     * 查询商家核销的记录
     * @param miniAccountPassTicketCodeParam
     * @return
     */
    @Override
    public IPage<ShopPassTicketCodeVo> pageShopVerifyCode(MiniAccountPassTicketCodeParam miniAccountPassTicketCodeParam) {
        IPage<ShopPassTicketCodeVo> page = null;
        String sourceShopId = ShopContextHolder.getShopId();

        ShopsPartner shopsPartner = remoteShopsService.getShopsPartnerMain();
        String paramShopId = miniAccountPassTicketCodeParam.getShopId();

        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        if(null != shopsPartner && shopsPartner.getShopId().equals(paramShopId)){
            // 主店铺登录用户，查出所有数据
            miniAccountPassTicketCodeParam.setShopId(null);
        }
        if(null != miniAccountPassTicketCodeParam.getStartTime()){
            String startTimeStr = DateUtil.format(miniAccountPassTicketCodeParam.getStartTime(), "yyyy-MM-dd 00:00:00");
            miniAccountPassTicketCodeParam.setStartTime(DateUtil.parse(startTimeStr));
        }
        if(null != miniAccountPassTicketCodeParam.getEndTime()){
            String endTimeStr = DateUtil.format(miniAccountPassTicketCodeParam.getEndTime(), "yyyy-MM-dd 23:59:59");
            miniAccountPassTicketCodeParam.setEndTime(DateUtil.parse(endTimeStr));
        }

        page = this.baseMapper.selectShopVerifyList(new Page<>(miniAccountPassTicketCodeParam.getCurrent(), miniAccountPassTicketCodeParam.getSize()),
                miniAccountPassTicketCodeParam);
        ShopContextHolder.setShopId(sourceShopId);
        return page;
    }
    @Override
    public void exportShopPassTicketCode(MiniAccountPassTicketCodeParam miniAccountPassTicketCodeParam) {
        IPage<ShopsPassTicketCodeExcelVo> page = null;

        // 手动设置shopId成默认值，如此就可以不走多店铺的逻辑，这样查出来的数据才正确
        ShopsPartner shopsPartner = remoteShopsService.getShopsPartnerMain();
        String sourceShopId = ShopContextHolder.getShopId();

        ShopContextHolder.setShopId(CommonConstants.DEFAULT_SHOP_ID);
        if(null != shopsPartner && !shopsPartner.getShopId().equals(sourceShopId)){
            // 非主店铺登录用户
            miniAccountPassTicketCodeParam.setShopId(sourceShopId);
        }
        if(null != miniAccountPassTicketCodeParam.getStartTime()){
            String startTimeStr = DateUtil.format(miniAccountPassTicketCodeParam.getStartTime(), "yyyy-MM-dd 00:00:00");
            miniAccountPassTicketCodeParam.setStartTime(DateUtil.parse(startTimeStr));
        }
        if(null != miniAccountPassTicketCodeParam.getEndTime()){
            String endTimeStr = DateUtil.format(miniAccountPassTicketCodeParam.getEndTime(), "yyyy-MM-dd 23:59:59");
            miniAccountPassTicketCodeParam.setEndTime(DateUtil.parse(endTimeStr));
        }

        miniAccountPassTicketCodeParam.setCurrent(1);
        // 设置导出最大数量
        miniAccountPassTicketCodeParam.setSize(CommonConstants.MAX_EXPORT_SIZE);
        page = this.baseMapper.selectShopVerifyExcelList(new Page<>(miniAccountPassTicketCodeParam.getCurrent(), miniAccountPassTicketCodeParam.getSize()),
                miniAccountPassTicketCodeParam);
        ShopContextHolder.setShopId(sourceShopId);
        PageUtils<ShopsPassTicketCodeExcelVo> pageUtils =   new PageUtils<>(page);
        List<ShopsPassTicketCodeExcelVo> excelVoList = pageUtils.getList();
        String fileName = DateUtil.format(new Date(), "yyyyMMdd HHmmss") + RandomUtil.randomString(4) + "核销列表";
        boolean success = HuToolExcelUtils.list2xlsx(excelVoList, fileName, ShopsPassTicketCodeExcelVo.class);
        if(!success){
            throw new ServiceException("导出数据异常", SystemCode.FAILURE.getCode());
        }
    }



    /**
     * 通过验证码获取通惠证记录
     * @param verifyCode 核销码
     * @return
     */
    @Override
    public ShopPassTicket getPassTicketByCode(String verifyCode) {
        MiniAccountPassTicketCode ticketCode = null;
        String codeKey = RedisKey.getPassTicketCodeKey(verifyCode);
        AccountRedis accountRedis = new AccountRedis();
        String status = accountRedis.get(codeKey);
        if(StrUtil.isNotBlank(status)){
            throw new ServiceException("此码已核销，不能重复核销", SystemCode.DATA_EXPIRED.getCode());
        }else {
            //未使用，则进行核销
            LambdaQueryWrapper<MiniAccountPassTicketCode> codeWrapper = new LambdaQueryWrapper<>();
            codeWrapper.eq(MiniAccountPassTicketCode::getVerifyCode, verifyCode).eq(MiniAccountPassTicketCode::getStatus, PromotionStatusEnum.UN_USE.getStatus());
            List<MiniAccountPassTicketCode> codeList = this.list(codeWrapper);
            if(CollectionUtil.isEmpty(codeList)){
                throw new ServiceException("该核销码不存在，无法核销", SystemCode.DATA_MANY.getCode());
            }
            if(CollectionUtil.isNotEmpty(codeList) && codeList.size() != 1){
                throw new ServiceException("此码暂不能核销，请联系运营平台管理员", SystemCode.DATA_MANY.getCode());
            }
            ticketCode = codeList.get(0);
            if (DateUtil.compare(new Date(), ticketCode.getEndTime()) > 0) {
                throw new ServiceException("此码已过期，无法核销", SystemCode.DATA_EXPIRED.getCode());
            }
        }
        ShopPassTicket shopPassTicket = remoteShopsService.getPassTicketById(ticketCode.getPassTicketId());
        return shopPassTicket;
    }
}
