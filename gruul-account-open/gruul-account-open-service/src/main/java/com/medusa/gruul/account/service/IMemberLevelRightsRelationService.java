package com.medusa.gruul.account.service;

import com.medusa.gruul.account.api.entity.MemberLevelRightsRelation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 会员等级、权益关联表
 * @Author: qsx
 * @Date:   2022-02-23
 * @Version: V1.0
 */
public interface IMemberLevelRightsRelationService extends IService<MemberLevelRightsRelation> {
    /**
     * 查询会员权益信息
     * @param memberLevelIdList
     *@return 会员权益信息
     */
    List<MemberLevelRightsRelation> selectMemberPowerList(List<String> memberLevelIdList);
    /**
     * 删除会员权益信息
     *@param memberLevelId
     *
     */
    void deleteMemberRights(String memberLevelId);
}
