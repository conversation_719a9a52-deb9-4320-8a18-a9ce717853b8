package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MemberActiveSetting;
import com.medusa.gruul.account.model.dto.MemberActiveSettingDto;
import com.medusa.gruul.account.model.vo.MemberActiveSettingVo;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:15 2025/6/3
 */
public interface IMemberActiveSettingService extends IService<MemberActiveSetting> {

    /**
     * 添加或更新会员有效期设置
     * @param dto
     */
    void addOrUpdate(MemberActiveSettingDto dto);

    /**
     * 获取会员有效期设置
     */
    MemberActiveSettingVo getMemberActiveSetting();
}
