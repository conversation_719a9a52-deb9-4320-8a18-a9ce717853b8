package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccountGoldenChange;
import com.medusa.gruul.account.model.param.MiniAccountGoldenChangeParam;
import com.medusa.gruul.account.model.vo.MiniAccountGoldenChangeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:36 2025/7/21
 */
@Mapper
public interface MiniAccountGoldenChangeMapper extends BaseMapper<MiniAccountGoldenChange> {
    /**
     * 分页查询金豆变更记录
     * @param objectPage
     * @param param
     * @return
     */
    IPage<MiniAccountGoldenChangeVo> queryList(Page<MiniAccountGoldenChangeVo> objectPage, @Param("paramMap") MiniAccountGoldenChangeParam param);

    /**
     * 根据id查询金豆变更记录
     * @param param
     * @return
     */
    MiniAccountGoldenChangeVo getMiniAccountGoldenChange(@Param("paramMap") MiniAccountGoldenChangeParam param);
}
