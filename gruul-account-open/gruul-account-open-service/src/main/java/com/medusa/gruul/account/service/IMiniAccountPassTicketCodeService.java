package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountPassTicketCode;
import com.medusa.gruul.account.api.model.vo.ShopPassTicketCodeVo;
import com.medusa.gruul.account.model.dto.MiniAccountPassTicketCodeDto;
import com.medusa.gruul.account.api.model.param.MiniAccountPassTicketCodeParam;
import com.medusa.gruul.account.model.vo.MiniAccountPassTicketCodeVo;
import com.medusa.gruul.shops.api.entity.ShopPassTicket;

/**
 *会员-通行票验证码服务接口
 * <AUTHOR>
 */
public interface IMiniAccountPassTicketCodeService extends IService<MiniAccountPassTicketCode> {


    /**
     * 添加记录，生成验证码
     * @param miniAccountPassTicketCodeDto
     * @return
     */
    MiniAccountPassTicketCode add(MiniAccountPassTicketCodeDto miniAccountPassTicketCodeDto);

    /**
     * 获取通行票验证码
     * @param miniAccountPassTicketCodeDto
     * @return
     */
    MiniAccountPassTicketCodeVo getCode(MiniAccountPassTicketCodeDto miniAccountPassTicketCodeDto);

    /**
     * 核销通行票验证码
     * @param verifyCode 核销码
     * @return
     */
    MiniAccountPassTicketCode verifyCode(String verifyCode);

    /**
     * 商家用户查询自己核销的记录
     * @return
     */
    IPage<ShopPassTicketCodeVo> pageShopUserVerifyCode(MiniAccountPassTicketCodeParam miniAccountPassTicketCodeParam);

    /**
     * 查询商家核销的记录
     * @return
     */
    IPage<ShopPassTicketCodeVo> pageShopVerifyCode(MiniAccountPassTicketCodeParam miniAccountPassTicketCodeParam);

    /**
     * 导出商家核销的记录
     * @param miniAccountPassTicketCodeParam
     */
    void exportShopPassTicketCode(MiniAccountPassTicketCodeParam miniAccountPassTicketCodeParam);


    /**
     * 通过验证码获取通惠证记录
     * @param verifyCode 核销码
     * @return
     */
    ShopPassTicket getPassTicketByCode(String verifyCode);
}
