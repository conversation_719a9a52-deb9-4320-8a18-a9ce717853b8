package com.medusa.gruul.account.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.api.enums.MemberActiveSettingStatusEnum;
import com.medusa.gruul.account.api.model.AccountInfoDto;
import com.medusa.gruul.account.api.model.vo.ApiMemberLevelVo;
import com.medusa.gruul.account.conf.SnowflakeProperty;
import com.medusa.gruul.account.mapper.MemberLevelMapper;
import com.medusa.gruul.account.mapper.MemberTypeMapper;
import com.medusa.gruul.account.model.dto.MemberLevelDto;
import com.medusa.gruul.account.model.dto.MemberLevelRightsDto;
import com.medusa.gruul.account.model.param.MemberLevelParam;
import com.medusa.gruul.account.model.param.MemberLevelRelationParam;
import com.medusa.gruul.account.model.param.MemberLevelRuleMessageParam;
import com.medusa.gruul.account.model.param.MiniAccountMemberUpParam;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.service.*;
import com.medusa.gruul.account.api.enums.ExternalAccountEnum;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.MemberFlagEnum;
import com.medusa.gruul.common.core.constant.enums.MemberTypeStatusEnum;
import com.medusa.gruul.common.core.constant.enums.RegionFlagEnum;
import com.medusa.gruul.common.core.constant.enums.RegionTypeEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.DateUtils;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.common.core.util.SystemCode;
import com.medusa.gruul.common.dto.CurUserDto;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员等级
 *
 * <AUTHOR>
 * @since 2019-12-02
 */
@Service
@Slf4j
public class MemberLevelServiceImpl extends ServiceImpl<MemberLevelMapper, MemberLevel> implements IMemberLevelService {

    @Autowired
    private IMemberLevelRightsRelationService memberLevelRightsRelationService;
    @Autowired
    private IMemberLevelRightsService memberLevelRightsService;
    @Autowired
    private SnowflakeProperty snowflakeProperty;
    @Autowired
    private IMiniAccountService miniAccountService;
    @Autowired
    private IMemberLevelRuleService memberLevelRuleService;
    @Autowired
    private IMemberLevelRuleMessageService memberLevelRuleMessageService;
    @Autowired
    private IMemberLevelService memberLevelService;
    @Autowired
    private RemoteGoodsService remoteGoodsService;
    @Autowired
    private RemoteShopsService remoteShopsService;
    @Autowired
    private IMemberTypeService memberTypeService;
    @Autowired
    private IMemberLevelRelationService memberLevelRelationService;
    @Autowired
    private IMemberActiveSettingService memberActiveSettingService;
    @Autowired
    private IMiniAccountMemberUpService miniAccountMemberUpService;
    @Autowired
    private IMiniAccountExtendsService miniAccountExtendsService;

    @Autowired
    private RemoteOrderService remoteOrderService;


    @Override
    public List<MemberLevel> getByIdList(List<String> tagIdList) {
        return this.baseMapper.selectList(new QueryWrapper<MemberLevel>().in("id", tagIdList));
    }
    /**
     * 停用会员等级
     * <AUTHOR>
     * @date 2022/3/8 10:32
     * @param memberLevel
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    @Override
    public Map<String, Object> disable(MemberLevel memberLevel) {

        //看该等级在会员表里是否有应用，如果有则不允许停用
        int num=miniAccountService.count(new LambdaQueryWrapper<MiniAccount>().eq(MiniAccount::getMemberLevelId, memberLevel.getId()));
        boolean fl=false;
        //启用
        int enable = 0 ;
        //停用
        int deactivate = 1 ;

        if(deactivate == memberLevel.getDisable()){
            LambdaQueryWrapper<MemberLevelRule>wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MemberLevelRule::getDirectLowMemberLevelId,memberLevel.getId());
            int count = this.memberLevelRuleService.count(wrapper);
            if(count>0){
                throw new ServiceException("会员等级已关联直推最大会员等级，停用失败", SystemCode.DATA_DELETE_FAILED.getCode());
            }

            LambdaQueryWrapper<MemberLevelRule>wrapper2 = new LambdaQueryWrapper<>();
            wrapper2.eq(MemberLevelRule::getPreLowMemberLevelId,memberLevel.getId());
            int count2 = this.memberLevelRuleService.count(wrapper2);
            if(count2>0){
                throw new ServiceException("会员等级已关联前置最低会员等级，停用失败", SystemCode.DATA_DELETE_FAILED.getCode());
            }
        }


        //如果是停用该会员等级则要判断在会员表里面是否有引用该等级，如果没有则允许停用。启用是都允许启用
        if((num==0 && deactivate == memberLevel.getDisable()) || enable == memberLevel.getDisable()){
            //将会员等级停用或者启用
            MemberLevel memberLvelWto=new MemberLevel();
            memberLvelWto.setId(memberLevel.getId());
            memberLvelWto.setDisable(memberLevel.getDisable());
            fl= memberLevelService.updateById(memberLvelWto);
        }
        String str="当前存在该等级的会员，无法停用！";
        boolean success=false;
        Map<String, Object> map=new HashMap<String, Object>(2);
        if(fl && enable == memberLevel.getDisable()){
            str="该等级会员启用成功！";
            success=true;
        }else if(!fl && enable == memberLevel.getDisable()){
            str="该等级会员启用失败！";
        }else if(fl && deactivate == memberLevel.getDisable()){
            str="该等级会员停用成功！";
            success=true;
        }
        map.put("success",success);
        map.put("str",str);
        return map;
    }
    /**
     * 添加或者修改会员等级
     * <AUTHOR>
     * @date 2022/3/8 10:33
     * @param memberLeveList
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addOrUpdate(List<MemberLevelVo> memberLeveList) {
        //标识被勾选的权益
        int choice=1;
        Set<String> memberLevelSet = memberLeveList.stream().map(MemberLevelVo -> MemberLevelVo.getMemberLevel()).collect(Collectors.toSet());
        if(memberLevelSet.size() != memberLeveList.size()){
            throw new ServiceException("等级名称重复，请检查", SystemCode.DATA_EXISTED.getCode());
        }
        for(MemberLevelVo memberLevelVo:memberLeveList){
            //会员类型id
            Long memberTypeId = memberLevelVo.getMemberTypeId();

            //区域类型
            Integer regionType = memberLevelVo.getRegionType();
            //会员体系会员
            Integer memberFlag = memberLevelVo.getMemberFlag();

            if(memberTypeId == null || memberTypeId.equals("")){
                throw new ServiceException("会员类型id不能为空");
            }
            MemberType memberType = memberTypeService.getById(memberTypeId);
            if(memberType == null || memberType.equals("")){
                throw new ServiceException("会员类型不存在");
            }


            if(StrUtil.isNotEmpty(memberLevelVo.getId())){

                if(memberType.getRegionFlag() == RegionFlagEnum.YES.getStatus()
                        &&regionType!=null&&!regionType.equals("")){
                    if(regionType != RegionTypeEnum.DISTRICT.getStatus()
                            &&regionType != RegionTypeEnum.CITY.getStatus()
                            &&regionType != RegionTypeEnum.PROVINCE.getStatus()){
                        throw new ServiceException("区域类型必须1-区/县，2-市级，3-省级");
                    }
                    Integer count = this.baseMapper.getRegionCountByRegionType(regionType,memberLevelVo.getId());
                    if(count>0){
                        if(regionType == RegionTypeEnum.DISTRICT.getStatus()){
                            throw new ServiceException("区域类型标识为是的会员类型，其区域类型为区/县，状态为启用的会员等级只能有一个");
                        }
                        if(regionType == RegionTypeEnum.CITY.getStatus()){
                            throw new ServiceException("区域类型标识为是的会员类型，其区域类型为市级，状态为启用的会员等级只能有一个");
                        }
                        if(regionType == RegionTypeEnum.PROVINCE.getStatus()){
                            throw new ServiceException("区域类型标识为是的会员类型，其区域类型为省级，状态为启用的会员等级只能有一个");
                        }
                    }
                }

                //修改会员等级信息
                MemberLevel memberLevel=new MemberLevel();
                BeanUtils.copyProperties(memberLevelVo, memberLevel);

                if(memberFlag == null || memberFlag.equals("")){
                    memberLevel.setMemberFlag(MemberFlagEnum.NO.getStatus());
                }
                /*LambdaQueryWrapper<MemberLevel> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MemberLevel::getMemberLevel, memberLevel.getMemberLevel()).ne(MemberLevel::getId, memberLevel.getId());
                List<MemberLevel> memberLevelList = this.baseMapper.selectList(queryWrapper);
                if (CollectionUtil.isNotEmpty(memberLevelList)) {
                    throw new ServiceException(memberLevel.getMemberLevel() + "等级名称已存在", SystemCode.DATA_EXISTED.getCode());
                }*/

                this.baseMapper.update(memberLevel);
                //修改会员等级表关联的权益
                //删除原先关联的权益
                memberLevelRightsRelationService.deleteMemberRights(memberLevel.getId());
                //添加会员等级表关联的会员权益
                if(CollectionUtil.isNotEmpty(memberLevelVo.getRightsAndInterests())){
                    //获取被勾选的权益
                    List<MemberLevelRightsVo> rightsList = memberLevelVo.getRightsAndInterests().stream().filter(rights -> rights.getIsSelected() == choice ).collect(Collectors.toList());
                    //将权益的id、权益的值、会员Id赋值到会员等级权益关联表
                    List<MemberLevelRightsRelation> rightsRelationList=rightsList.stream().map(rights -> new MemberLevelRightsRelation(rights.getId(), rights.getValue(),memberLevel.getId())).collect(Collectors.toList());
                    //批量增加
                    memberLevelRightsRelationService.saveBatch(rightsRelationList);
                }
            }else{
                if(memberType.getRegionFlag() == RegionFlagEnum.YES.getStatus()
                        &&regionType!=null&&!regionType.equals("")){

                    if(regionType != RegionTypeEnum.DISTRICT.getStatus()
                            &&regionType != RegionTypeEnum.CITY.getStatus()
                            &&regionType != RegionTypeEnum.PROVINCE.getStatus()){
                        throw new ServiceException("区域类型必须1-区/县，2-市级，3-省级");
                    }

                    Integer count = this.baseMapper.getRegionCountByRegionType(regionType,null);

                    if(count>0){
                        if(regionType == RegionTypeEnum.DISTRICT.getStatus()){
                            throw new ServiceException("区域类型标识为是的会员类型，其区域类型为区/县，状态为启用的会员等级只能有一个");
                        }
                        if(regionType == RegionTypeEnum.CITY.getStatus()){
                            throw new ServiceException("区域类型标识为是的会员类型，其区域类型为市级，状态为启用的会员等级只能有一个");
                        }
                        if(regionType == RegionTypeEnum.PROVINCE.getStatus()){
                            throw new ServiceException("区域类型标识为是的会员类型，其区域类型为省级，状态为启用的会员等级只能有一个");
                        }
                    }
                }

                //添加会员等级信息
                MemberLevel membershipLevel=new MemberLevel();
                BeanUtils.copyProperties(memberLevelVo, membershipLevel);

                if(memberFlag == null || memberFlag.equals("")){
                    membershipLevel.setMemberFlag(MemberFlagEnum.NO.getStatus());
                }
                /*LambdaQueryWrapper<MemberLevel> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MemberLevel::getMemberLevel, membershipLevel.getMemberLevel());
                List<MemberLevel> memberLevelList = this.baseMapper.selectList(queryWrapper);
                if (CollectionUtil.isNotEmpty(memberLevelList)) {
                    throw new ServiceException(membershipLevel.getMemberLevel() + "等级名称已存在", SystemCode.DATA_EXISTED.getCode());
                }*/

                Snowflake snowflake = IdUtil.createSnowflake(snowflakeProperty.getWorkerId(), snowflakeProperty.getDatacenterId());
                String memberLevelId = snowflake.nextId() + "";
                membershipLevel.setId(memberLevelId);
                memberLevelService.save(membershipLevel);
                //添加会员等级表关联的会员权益
                if(CollectionUtil.isNotEmpty(memberLevelVo.getRightsAndInterests())){
                    //获取被勾选的权益
                    List<MemberLevelRightsVo> rightsList = memberLevelVo.getRightsAndInterests().stream().filter(rights -> rights.getIsSelected() == choice ).collect(Collectors.toList());
                    //将权益的id、权益的值、会员Id赋值到会员等级权益关联表
                    List<MemberLevelRightsRelation> rightsRelationList=rightsList.stream().map(rights -> new MemberLevelRightsRelation(rights.getId(), rights.getValue(),memberLevelId)).collect(Collectors.toList());
                    //批量增加
                    memberLevelRightsRelationService.saveBatch(rightsRelationList);
                }
            }
        }
    }

    /**
     * 删除会员卡
     * @param memberLevel
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(MemberLevel memberLevel){
        //查询等级是否被使用过，使用过不允许删除
        Integer levelCount = this.remoteGoodsService.getCountByMemberLevelId(memberLevel.getId());
        if(null != levelCount && levelCount.intValue() > 0){
            throw new ServiceException("会员卡已关联商品会员价格，删除失败", SystemCode.DATA_DELETE_FAILED.getCode());
        }

        LambdaQueryWrapper<MemberLevelRule>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberLevelRule::getDirectLowMemberLevelId,memberLevel.getId());
        int count = this.memberLevelRuleService.count(wrapper);
        if(count>0){
            throw new ServiceException("会员等级已关联直推最大会员等级，删除失败", SystemCode.DATA_DELETE_FAILED.getCode());
        }

        LambdaQueryWrapper<MemberLevelRule>wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(MemberLevelRule::getPreLowMemberLevelId,memberLevel.getId());
        int count2 = this.memberLevelRuleService.count(wrapper2);
        if(count2>0){
            throw new ServiceException("会员等级已关联前置最低会员等级，删除失败", SystemCode.DATA_DELETE_FAILED.getCode());
        }

        //删除原先关联的权益
        memberLevelRightsRelationService.deleteMemberRights(memberLevel.getId());
        memberLevel.setDeleted(true);
        memberLevel.setUpdateTime(DateUtils.timestampCoverLocalDateTime(System.currentTimeMillis()));
        this.baseMapper.deleteMemberLevel(memberLevel);
    }

    @Override
    public void updateLevel(MiniAccountMemberInformation miniAccountMember) {
         this.baseMapper.updateLevel(miniAccountMember);
    }

    @Override
    public void renew(MemberLevel memberLevel) {
    }

    @Override
    public void deactivate(String memberId) {

    }

    @Override
    public void enable(String memberId) {

    }

    @Override
    public List<MemberLevel> getByMemberLeveIdList(List<String> memberLeveIdList) {
      return this.getBaseMapper().selectList(new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getDisable,0).in(MemberLevel::getId, memberLeveIdList));

    }

    @Override
    public List<MemberLevelVo> selectList(MemberLevelParam param) {

        Long memberTypeId = param.getMemberTypeId();
        if(memberTypeId != null && !memberTypeId.equals("")){
            //查询所有的会员等级
            List<MemberLevel> memberLevel =this.baseMapper.selectMemberLevelList(memberTypeId);
            if(memberLevel!=null&&memberLevel.size()>0){
                List<MemberLevelVo> vos = new LinkedList<>();
                List<String> memberLevelIdList = memberLevel.stream().map(MemberLevel::getId).collect(Collectors.toList());
                //查询用户权益
                Map<String, List<MemberLevelRightsVo>> userPower = MapUtil.newHashMap(memberLevelIdList.size());
                if(CollectionUtil.isNotEmpty(memberLevelIdList)){
                    List<MemberLevelRightsRelation> powers= memberLevelRightsRelationService.selectMemberPowerList(memberLevelIdList);
                    if (CollectionUtil.isNotEmpty(powers)) {
                        setAccountGroupRights(userPower, powers);
                    }
                    //封装用户数据
                    setPcAccountListVos(memberLevel, vos, userPower);
                }
                return  vos;
            }else{
                return new ArrayList<>();
            }
        }else{
            return new ArrayList<>();
        }
    }

    /**
     * 查询全部会员等级信息
     * @return
     */
    @Override
    public List<MemberLevelVo> selectAllList() {
        return this.baseMapper.selectMemberLevelAllList();
    }

    /**
     *  查询全部会员等级信息-根据会员类型分组
     * @return
     */
    @Override
    public  List<MemberLevelGroupByMemberTypeVo> selectAllListGroupByMemberTypeId() {
        List<MemberLevelVo> list = this.baseMapper.selectMemberLevelAllList();

        Map<String, List<MemberLevelVo>> dataList = list.stream().collect(Collectors.groupingBy(e -> e.getMemberTypeId() + "@@" + e.getMemberTypeName()));
        List<MemberLevelGroupByMemberTypeVo>resultList = new ArrayList<>();
        for (Map.Entry<String, List<MemberLevelVo>> entry : dataList.entrySet()) {
            String key = entry.getKey();
            List<MemberLevelVo> memberLevelVos = entry.getValue();
            String[] split = key.split("@@");
            MemberLevelGroupByMemberTypeVo memberLevelGroupByMemberTypeVo = new MemberLevelGroupByMemberTypeVo();
            memberLevelGroupByMemberTypeVo.setMemberTypeId(Long.valueOf(split[0]));
            memberLevelGroupByMemberTypeVo.setMemberTypeName(split[1]);
            memberLevelGroupByMemberTypeVo.setMemberLevelVos(memberLevelVos);
            resultList.add(memberLevelGroupByMemberTypeVo);
        }
        return resultList;
    }

    /**
     * 根据会员类型id获取启用会员等级
     * @param memberTypeId
     * @return
     */
    @Override
    public List<MemberLevelVo> getMemberLevelByMemberTypeId(Long memberTypeId) {

        return this.baseMapper.getMemberLevelByMemberTypeId(memberTypeId);
    }
    /**
     * @param records 会员等级信息
     * @param vos     会员等级，包含权益信息（要返回给前端）
     * @param userPower 会员权益
     */
    /**封装会员等级数据*/
    private void setPcAccountListVos(List<MemberLevel> records, List<MemberLevelVo> vos, Map<String, List<MemberLevelRightsVo>> userPower) {
        //查询所有的权益信息
        List<MemberLevelRightsVo> rightsVoList= memberLevelRightsService.selectRightsVoAllList();
        for (MemberLevel record : records) {
            MemberLevelVo vo = new MemberLevelVo();
            BeanUtils.copyProperties(record, vo);
            List<MemberLevelRightsVo> userRightsVos= userPower.get(record.getId());
            //获取该会员有的权益与所有权益的差集
            //如果该会员等级没有权益则将查出来的所有未勾选的权益赋值上
            if(userRightsVos!=null && userRightsVos.size()>0){
                List<MemberLevelRightsVo> memberRightsDifferenceSet = rightsVoList.stream()
                        .filter(rightsVo -> !userRightsVos.stream().map(all -> all.getId()).collect(Collectors.toList()).contains(rightsVo.getId()))
                        .collect(Collectors.toList());
                userRightsVos.addAll(memberRightsDifferenceSet);
                vo.setRightsAndInterests(userRightsVos);
            }else{
                vo.setRightsAndInterests(rightsVoList);
            }
            vos.add(vo);
        }
    }


    /**
     * 设置权益
     *
     * @param userPower   会员等级的权益
     * @param powers 会员等级权益关联表
     */
    private void setAccountGroupRights(Map<String, List<MemberLevelRightsVo>> userPower, List<MemberLevelRightsRelation> powers) {
        //获取权益id
        List<String> rightsIdList = powers.stream().map(MemberLevelRightsRelation::getRightsId).distinct().collect(Collectors.toList());
        //根据权益Id查询权益
        List<MemberLevelRights> miniAccountRights = memberLevelRightsService.selectRightsList(rightsIdList);
        //将权益封装称map id为key
        Map<String, MemberLevelRights> rightsMap = miniAccountRights.stream().collect(Collectors.toMap(MemberLevelRights::getId, v -> v));
        //将会员等级权益关联表也封装称map  id为key
        Map<String, MemberLevelRightsRelation> powersMap = powers.stream().collect(Collectors.toMap(MemberLevelRightsRelation::getId, v -> v));
        //获取会员等级封装称map  id为key，并且权益的id、名字、值都添加进去
        powers.stream().collect(Collectors.groupingBy(MemberLevelRightsRelation::getMemberLevelId)).forEach((k, v) -> {
            List<MemberLevelRightsVo> userRightsVos = new ArrayList<>(v.size());
            for (MemberLevelRightsRelation memberLevelRightsRelation : v) {
                MemberLevelRights miniAccountRight = rightsMap.get(memberLevelRightsRelation.getRightsId());
                MemberLevelRightsRelation power=powersMap.get(memberLevelRightsRelation.getId());
                if (miniAccountRight != null) {
                    MemberLevelRightsVo userRightsVo = new MemberLevelRightsVo();
                    userRightsVo.setId(miniAccountRight.getId());
                    userRightsVo.setName(miniAccountRight.getName());
                    userRightsVo.setValue(power.getValue());
                    userRightsVo.setIsSelected(1);
                    userRightsVos.add(userRightsVo);
                }
            }
            userPower.put(k, userRightsVos);
        });
    }

    @Override
    public List<MemberLevelRightsRelation> selectMemberPower(List<String> userIdList){
        return memberLevelRightsRelationService.selectMemberPowerList(userIdList);
    }

    @Override
    public List<MemberLevelRights> selectRights() {
        return this.baseMapper.selectRights();
    }

    @Override
    public List<MemberLevePriceVo> selectPrice(Long productId) {
        return this.baseMapper.selectMemberLevePrice(productId);
    }


    /**
     * 设置默认会员等级
     * @param id
     */
    @Override
    public void defaultLevel(Long id){
        LambdaQueryWrapper<MemberLevel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberLevel::getDefaultLevel, CommonConstants.NUMBER_ONE);
        List<MemberLevel> list = this.baseMapper.selectList(wrapper);
        //全部改为不是默认等级
        for(MemberLevel sm:list){
            sm.setDefaultLevel(CommonConstants.NUMBER_ZERO);
        }
        this.updateBatchById(list);
        //改为默认专区
        MemberLevel memberLevel=this.getById(id);
        memberLevel.setDefaultLevel(CommonConstants.NUMBER_ONE);
        this.updateById(memberLevel);
    }

    /**
     * 外部系统添加或者修改会员卡信息
     * @param memberLevelDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public MemberLevel outSave(MemberLevelDto memberLevelDto) {
        MemberLevel memberLevel =null;
        if(StrUtil.isNotEmpty(memberLevelDto.getId())){
            //修改会员等级信息
            memberLevel= this.baseMapper.selectById(memberLevelDto.getId());
            BeanUtils.copyProperties(memberLevelDto, memberLevel);

            this.baseMapper.update(memberLevel);

        }else{
            //添加会员等级信息
            memberLevel = new MemberLevel();
            BeanUtils.copyProperties(memberLevelDto, memberLevel);

                /*LambdaQueryWrapper<MemberLevel> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(MemberLevel::getMemberLevel, memberLevel.getMemberLevel());
                List<MemberLevel> memberLevelList = this.baseMapper.selectList(queryWrapper);
                if (CollectionUtil.isNotEmpty(memberLevelList)) {
                    throw new ServiceException(memberLevel.getMemberLevel() + "等级名称已存在", SystemCode.DATA_EXISTED.getCode());
                }*/

            //Snowflake snowflake = IdUtil.createSnowflake(snowflakeProperty.getWorkerId(), snowflakeProperty.getDatacenterId());
            //String memberLevelId = snowflake.nextId() + "";
            //memberLevel.setId(memberLevelId);
            //默认启用
            memberLevel.setDisable(CommonConstants.NUMBER_ZERO);
            //默认仓库
            memberLevel.setStockFlag(CommonConstants.NUMBER_ZERO);
            //设置排序level
            LambdaQueryWrapper<MemberLevel>wrapper = new LambdaQueryWrapper<>();
            wrapper.orderByDesc(MemberLevel::getLevel);
            wrapper.last("limit 1");
            MemberLevel one = this.getOne(wrapper);
            if(one!=null){
                memberLevel.setLevel(one.getLevel()+1);
            }else{
                memberLevel.setLevel(1);
            }
            memberLevelService.save(memberLevel);
            String memberLevelId = memberLevel.getId();
            //添加会员等级表关联的会员权益
            if(CollectionUtil.isNotEmpty(memberLevelDto.getRightsAndInterests())){
                int choice = 1;
                //获取被勾选的权益
                List<MemberLevelRightsDto> rightsList = memberLevelDto.getRightsAndInterests().stream().filter(rights -> rights.getIsSelected() == choice ).collect(Collectors.toList());
                //将权益的id、权益的值、会员Id赋值到会员等级权益关联表
                List<MemberLevelRightsRelation> rightsRelationList=rightsList.stream().map(rights -> new MemberLevelRightsRelation(rights.getId(), rights.getValue(),memberLevelId)).collect(Collectors.toList());
                //批量增加
                memberLevelRightsRelationService.saveBatch(rightsRelationList);
            }
        }
        return memberLevel;
    }


    @Override
    public PageUtils<List<MemberLevelVo>> externalMemberList(Integer page, Integer size) {
        Map<String, Object> paramMap = new HashMap<>();
        IPage<MemberLevelDto> iPage = this.baseMapper.selectExternalByMemberLeverList(new Page<>(page, size), paramMap);
        List<MemberLevelDto> records = iPage.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return new PageUtils(new ArrayList(0), (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
        }
        List<MemberLevelVo> vos = new LinkedList<>();
        List<String> legalRightList = records.stream().map(MemberLevelDto::getLegalRight).collect(Collectors.toList());
        //查询会员权益
        Map<String, List<MemberLevelRightsVo>> memberLeverPrice = MapUtil.newHashMap(legalRightList.size());
        List<MemberLevelRights> memberLevelRights = memberLevelRightsService.selectRightsListByEnable(legalRightList);
        if (CollectionUtil.isNotEmpty(memberLevelRights)) {
            setMemberLevelRights(memberLeverPrice, memberLevelRights);
        }
        //封装用户数据
        setPcMemberLevelListVos(records, vos, memberLeverPrice);
        //将发送的数据状态改为已发送
        List<String> idList=records.stream().map(MemberLevelDto::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(idList)){
            updateSendStatus(idList, ExternalAccountEnum.ISSUED.getStatus()+"");
        }
        return new PageUtils(vos, (int) iPage.getTotal(), (int) iPage.getSize(), (int) iPage.getCurrent());
    }

    @Override
    public void updateSendStatus(List<String> accountIds, String sendStatus) {
        this.baseMapper.updateSendStatus(accountIds,sendStatus);
    }

    @Override
    public MemberLevel getMemberLevel(Long memberTypeId) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (ObjectUtil.isNull(curUserDto)) {
            throw new ServiceException(SystemCode.UNAUTHORIZED);
        }
        log.info("当前用户信息:" + curUserDto.toString());


        //1.如果会员类型id不存在，取默认会员类型id
        if(memberTypeId == null){
            MemberType memberType = memberTypeService.getDefaultMemberType();
            memberTypeId = Long.valueOf(memberType.getId());
        }

        //查询用户持有的积分、收货地址
        AccountInfoDto accountInfoDto = miniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1,2,
                3, 5));

        MemberLevelRelationParam param = new MemberLevelRelationParam();
        param.setUserId(accountInfoDto.getMiniAccountunt().getUserId());
        param.setMemberTypeId(memberTypeId);
        List<MemberLevelRelation> memberLevelRelationList = memberLevelRelationService.getMemberLevelRelationNotMemberLevelId(param);

        if(memberLevelRelationList.size() == 0){
            throw new ServiceException("会员等级不存在，请联系管理员！");
        }
        //会员id
        String memberId = memberLevelRelationList.get(0).getMemberLevelId();
        MemberLevel memberLevel = this.getById(memberId);
        return memberLevel;
    }

    @Override
    public ApiMemberLevelVo getApiMemberLevel(Long memberTypeId) {
        CurUserDto curUserDto = CurUserUtil.getHttpCurUser();
        if (ObjectUtil.isNull(curUserDto)) {
            throw new ServiceException(SystemCode.UNAUTHORIZED);
        }
        log.info("当前用户信息:" + curUserDto.toString());


        //1.如果会员类型id不存在，取默认会员类型id
        if(memberTypeId == null){
            MemberType memberType = memberTypeService.getDefaultMemberType();
            memberTypeId = Long.valueOf(memberType.getId());
        }

        //查询用户持有的积分、收货地址
        AccountInfoDto accountInfoDto = miniAccountService.accountInfo(curUserDto.getUserId(), Arrays.asList(1,2,
                3, 5));

        MemberLevelRelationParam param = new MemberLevelRelationParam();
        param.setUserId(accountInfoDto.getMiniAccountunt().getUserId());
        param.setMemberTypeId(memberTypeId);
        List<MemberLevelRelation> memberLevelRelationList = memberLevelRelationService.getMemberLevelRelationNotMemberLevelId(param);

        if(memberLevelRelationList.size() == 0){
            throw new ServiceException("会员等级不存在，请联系管理员！");
        }

        //会员id
        String memberLevelId = memberLevelRelationList.get(0).getMemberLevelId();
        LocalDateTime upLevelTime = memberLevelRelationList.get(0).getUpLevelTime();
        MemberLevel memberLevel = this.getById(memberLevelId);
        ApiMemberLevelVo apiMemberLevelVo = new ApiMemberLevelVo();
        apiMemberLevelVo.setMemberLevel(memberLevel.getMemberLevel());
        apiMemberLevelVo.setMemberFlag(memberLevel.getMemberFlag());

        MemberActiveSettingVo memberActiveSetting = memberActiveSettingService.getMemberActiveSetting();
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String endTime = "";
        String endDays = "";

        if(memberActiveSetting!=null&&memberActiveSetting.getStatus() == MemberActiveSettingStatusEnum.YES.getStatus()){
            Integer activeDays = memberActiveSetting.getActiveDays();
            if(activeDays!=null){

                LocalDateTime lastDealTime = accountInfoDto.getMiniAccountExtends().getLastDealTime();
                LambdaQueryWrapper<MemberLevelRelation>wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(MemberLevelRelation::getUserId,accountInfoDto.getMiniAccountExtends().getUserId());
                wrapper.isNotNull(MemberLevelRelation::getUpLevelTime);
                wrapper.orderByDesc(MemberLevelRelation::getUpLevelTime);
                if(lastDealTime == null){
                    lastDealTime = accountInfoDto.getMiniAccountunt().getRegisterTime();
                }
                if(lastDealTime!=null){
                    LocalDateTime endLocalDateTime = lastDealTime.plusDays(activeDays);
                    endTime = endLocalDateTime.format(formatter);
                    long days = Duration.between(now, endLocalDateTime).toDays();
                    if(days>=0){
                        endDays = days + "天";
                    }else{
                        endDays = "已到期";
                    }
                }
            }
        }

        apiMemberLevelVo.setEndTime("无");
        apiMemberLevelVo.setEndDays("无");
        apiMemberLevelVo.setJoinTime("无");
        apiMemberLevelVo.setJoinDays("无");

        if (StrUtil.isNotBlank(accountInfoDto.getMiniAccountunt().getUserName()) || accountInfoDto.getMiniAccountunt()!=null ){
            if(memberLevel.getMemberFlag() == MemberFlagEnum.YES.getStatus()){
                apiMemberLevelVo.setEndTime(endTime);
                apiMemberLevelVo.setEndDays(endDays);
                apiMemberLevelVo.setJoinTime(upLevelTime.format(formatter));
                long joinDays = Duration.between(upLevelTime, now).toDays();
                apiMemberLevelVo.setJoinDays(joinDays+"天");
            }
        }
        apiMemberLevelVo.setAvatarUrl(accountInfoDto.getMiniAccountunt().getAvatarUrl());
        //会员类型及消费
        MiniAccountMemberUpParam miniAccountMemberUpParam = new MiniAccountMemberUpParam();
        miniAccountMemberUpParam.setMemberTypeId(memberTypeId);
        miniAccountMemberUpParam.setUserId(accountInfoDto.getMiniAccountExtends().getShopUserId());

        BigDecimal memberAmount = BigDecimal.ZERO;

        //查询会员的升级消费记录
        MiniAccountMemberUp miniAccountMemberUp = miniAccountMemberUpService.getMiniAccountMemberUp(miniAccountMemberUpParam);
        if(miniAccountMemberUp != null&&miniAccountMemberUp.getAmount()!=null){
            memberAmount = miniAccountMemberUp.getAmount();
        }
        apiMemberLevelVo.setMemberAmount(memberAmount);
        //查询待发货数量
        Integer unDeliveryQuantity = remoteOrderService.getUnDeliveryQuantity(curUserDto.getUserId(),memberTypeId);
        apiMemberLevelVo.setUnDeliveryQuantity(unDeliveryQuantity);


        //获取升级规则
        MemberLevelRuleMessageParam memberLevelRuleMessageParam = new MemberLevelRuleMessageParam();
        memberLevelRuleMessageParam.setMemberTypeId(Long.valueOf(memberTypeId));
        MemberLevelRuleMessageVo memberLevelRuleMessage = memberLevelRuleMessageService.getMemberLevelRuleMessage(memberLevelRuleMessageParam);
        if(memberLevelRuleMessage!=null){
            apiMemberLevelVo.setDescription(memberLevelRuleMessage.getDescription());
        }
        //获取当前会员规则
        MemberLevelRuleVo memberLevelRuleVo = memberLevelRuleService.getByMemberLevelId(Long.valueOf(memberLevelId));

        //是否可以入驻
        apiMemberLevelVo.setApplyShopsFlag(CommonConstants.NUMBER_ZERO);
        //判断是否入驻
        Boolean shopsFlag = remoteShopsService.getShopsFlag(curUserDto.getUserId());
        if(!shopsFlag){
            apiMemberLevelVo.setApplyShopsFlag(memberLevelRuleVo.getApplyShopPartner());
        }


        MemberLevelRuleVo nextMemberLevelRuleVo = null;
        Integer sort = memberLevelRuleVo.getSort();
        if(memberLevelRuleMessage!=null&&memberLevelRuleMessage.getRules()!=null&&memberLevelRuleMessage.getRules().size()>0){
            for (MemberLevelRuleVo rule : memberLevelRuleMessage.getRules()) {
                if(rule.getSort() == sort+1){
                    nextMemberLevelRuleVo = rule;
                    break;
                }
            }
        }
        apiMemberLevelVo.setNextUpgradeAmount(BigDecimal.ZERO);
        if(nextMemberLevelRuleVo!=null){
            apiMemberLevelVo.setNextMemberLevel(nextMemberLevelRuleVo.getMemberLevel());
            BigDecimal memberAmountStart = nextMemberLevelRuleVo.getMemberAmountStart();
            if(memberAmountStart == null){
                memberAmountStart = BigDecimal.ZERO;
            }
            apiMemberLevelVo.setNextAmount(memberAmountStart);

            if(memberAmountStart.compareTo(apiMemberLevelVo.getMemberAmount())>0){
                apiMemberLevelVo.setNextUpgradeAmount(memberAmountStart.subtract(apiMemberLevelVo.getMemberAmount()));
            }
            List<String>memberLevelIds = new ArrayList<>();
            if(StringUtils.isNotEmpty(nextMemberLevelRuleVo.getDirectLowMemberLevelId())){
                //直推最低会员等级对应的规则
                MemberLevelRuleVo directLowMemberLevelRuleVo = memberLevelRuleService.getByMemberLevelId(Long.valueOf(nextMemberLevelRuleVo.getDirectLowMemberLevelId()));
                if(directLowMemberLevelRuleVo!=null){
                    memberLevelIds = memberLevelRuleService.getMemberLevelIds(directLowMemberLevelRuleVo.getSort(),directLowMemberLevelRuleVo.getMainId());
                }
            }

            Integer userDirectMemberQty = miniAccountExtendsService.getDirectMemberQty(Long.valueOf(accountInfoDto.getMiniAccountunt().getUserId()),memberLevelIds);
            Integer directMemberQty = nextMemberLevelRuleVo.getDirectMemberQty() == null ? 0 :nextMemberLevelRuleVo.getDirectMemberQty();;
            apiMemberLevelVo.setAlreadyMemberNumber(userDirectMemberQty);
            if(userDirectMemberQty>directMemberQty){
                apiMemberLevelVo.setMissMemberNumber(0);
            }else{
                apiMemberLevelVo.setMissMemberNumber(directMemberQty-userDirectMemberQty);
            }

            List<String>memberLevelIds2 = new ArrayList<>();
            if(StringUtils.isNotEmpty(nextMemberLevelRuleVo.getDirectLowMemberLevelId2())){
                //直推最低会员等级对应的规则
                MemberLevelRuleVo directLowMemberLevelRuleVo2 = memberLevelRuleService.getByMemberLevelId(Long.valueOf(nextMemberLevelRuleVo.getDirectLowMemberLevelId2()));
                if(directLowMemberLevelRuleVo2!=null){
                    memberLevelIds2 = memberLevelRuleService.getMemberLevelIds(directLowMemberLevelRuleVo2.getSort(),directLowMemberLevelRuleVo2.getMainId());
                }
            }

            Integer userDirectMemberQty2 = miniAccountExtendsService.getDirectMemberQty(Long.valueOf(accountInfoDto.getMiniAccountunt().getUserId()),memberLevelIds2);
            Integer directMemberQty2 = nextMemberLevelRuleVo.getDirectMemberQty2() == null ? 0 :nextMemberLevelRuleVo.getDirectMemberQty2();;
            apiMemberLevelVo.setAlreadyMemberNumber2(userDirectMemberQty2);
            if(userDirectMemberQty2>directMemberQty2){
                apiMemberLevelVo.setMissMemberNumber2(0);
            }else{
                apiMemberLevelVo.setMissMemberNumber2(directMemberQty2-userDirectMemberQty2);
            }

        }

        return apiMemberLevelVo;
    }

    @Override
    public ApiMemberLevelVo getApiMemberLevelByUserId(String userId) {

        //查询用户持有的积分、收货地址
        AccountInfoDto accountInfoDto = miniAccountService.accountInfo(userId, Arrays.asList(1,2,
                3, 5));
        //会员id
        String memberId = accountInfoDto.getMiniAccountunt().getMemberLevelId();
        BigDecimal memberMoney = accountInfoDto.getMiniAccountExtends().getMemberMoney();
        if(memberMoney == null){
            memberMoney = BigDecimal.ZERO;
        }
        MemberLevel memberLevel = this.getById(memberId);
        ApiMemberLevelVo apiMemberLevelVo = new ApiMemberLevelVo();

        LambdaQueryWrapper<MemberLevelRuleMessage> messageWrapper = new LambdaQueryWrapper<>();
        messageWrapper.eq(MemberLevelRuleMessage::getDeleted,CommonConstants.NUMBER_ZERO);
        MemberLevelRuleMessage memberLevelRuleMessage = memberLevelRuleMessageService.getOne(messageWrapper);

        if(memberLevelRuleMessage!=null){
            apiMemberLevelVo.setDescription(memberLevelRuleMessage.getDescription());
        }

        apiMemberLevelVo.setMemberLevel(memberLevel.getMemberLevel());
        apiMemberLevelVo.setMemberId(memberLevel.getId());
        apiMemberLevelVo.setMemberAmount(memberMoney);
        LambdaQueryWrapper<MemberLevelRule>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberLevelRule::getMemberLevelId,memberId);
        wrapper.eq(MemberLevelRule::getDeleted,CommonConstants.NUMBER_ZERO);
        MemberLevelRule memberLevelRule = memberLevelRuleService.getOne(wrapper);
        if(memberLevelRule!=null){
            Integer sort = memberLevelRule.getSort();
            LambdaQueryWrapper<MemberLevelRule>queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MemberLevelRule::getDeleted,CommonConstants.NUMBER_ZERO);
            queryWrapper.eq(MemberLevelRule::getSort,sort+1);
            MemberLevelRule nextMemberLevelRule = memberLevelRuleService.getOne(queryWrapper);
            if(nextMemberLevelRule!=null){
                MemberLevel nextMemberLevel = this.getById(nextMemberLevelRule.getMemberLevelId());
                if(nextMemberLevel!=null){
                    BigDecimal memberAmountStart = nextMemberLevelRule.getMemberAmountStart();
                    if(memberAmountStart == null){
                        memberAmountStart = BigDecimal.ZERO;
                    }
                    apiMemberLevelVo.setNextMemberLevel(nextMemberLevel.getMemberLevel());
                    apiMemberLevelVo.setNextAmount(memberAmountStart);
                }
            }
        }
        return apiMemberLevelVo;
    }

    private void setPcMemberLevelListVos(List<MemberLevelDto> records, List<MemberLevelVo> vos, Map<String, List<MemberLevelRightsVo>> memberLeverPrice) {
        for (MemberLevelDto record : records) {
            MemberLevelVo vo = new MemberLevelVo();
            BeanUtils.copyProperties(record, vo);
            vo.setRightsAndInterests(memberLeverPrice.get(record.getLegalRight()));
            vos.add(vo);
        }
    }

    private void setMemberLevelRights(Map<String, List<MemberLevelRightsVo>> memberLeverPrice, List<MemberLevelRights> memberLevelRights) {

        Map<String, MemberLevelRights> memberLevelRightsMap = memberLevelRights.stream().collect(Collectors.toMap(MemberLevelRights::getId, v -> v));
        memberLevelRights.stream().collect(Collectors.groupingBy(MemberLevelRights::getId)).forEach((k, v) -> {
                List<MemberLevelRightsVo> memberLevelRightsVoList = new ArrayList<>(v.size());
            for (MemberLevelRights levelRights : v) {
                MemberLevelRights memberLevelRight= memberLevelRightsMap.get(levelRights.getId());
                if (memberLevelRight != null) {
                    MemberLevelRightsVo memberLevelRightsVo = new MemberLevelRightsVo();
                    BeanUtils.copyProperties(memberLevelRight,memberLevelRightsVo);
                    memberLevelRightsVoList.add(memberLevelRightsVo);
                }
            }
            memberLeverPrice.put(k,memberLevelRightsVoList);
        });

    }


}
