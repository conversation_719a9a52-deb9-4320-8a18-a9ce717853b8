package com.medusa.gruul.account.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MemberLevelRelation;
import com.medusa.gruul.account.api.entity.MemberLevelRule;
import com.medusa.gruul.account.api.entity.MemberLevelRuleMessage;
import com.medusa.gruul.account.mapper.MemberLevelRuleMapper;
import com.medusa.gruul.account.model.dto.MemberLevelRuleDto;
import com.medusa.gruul.account.model.param.MemberLevelRuleMessageParam;
import com.medusa.gruul.account.model.vo.MemberLevelRuleVo;
import com.medusa.gruul.account.service.IMemberLevelRelationService;
import com.medusa.gruul.account.service.IMemberLevelRuleMessageService;
import com.medusa.gruul.account.service.IMemberLevelRuleService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.SpringContextHolder;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:49 2024/11/5
 */
@Service
public class MemberLevelRuleServiceImpl extends ServiceImpl<MemberLevelRuleMapper, MemberLevelRule>implements IMemberLevelRuleService {


    @Autowired
    private IMemberLevelRelationService memberLevelRelationService;
    @Autowired
    private IMemberLevelRuleMessageService memberLevelRuleMessageService;


    @Override
    public List<MemberLevelRuleVo> getMemberLevelRule(MemberLevelRuleMessageParam param) {
        return this.baseMapper.getMemberLevelRule(param.getMemberTypeId());
    }

    @Override
    public Boolean checkApplyShopByLevelId(List<String> levelIds) {
        if (levelIds==null) {
           throw new ServiceException("会员等级id不能为空");
        }
        LambdaQueryWrapper<MemberLevelRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MemberLevelRule::getMemberLevelId,levelIds).eq(MemberLevelRule::getApplyShopPartner,1);
        int count = SpringContextHolder.getBean(IMemberLevelRuleService.class).count(queryWrapper);
        return   count > 0 ;
    }

    @Override
    public Boolean checkApplyShopByUserId(String userId) {
        if (StringUtils.isEmpty(userId)){
            throw new ServiceException("用户id不能为空");
        }
        List<MemberLevelRelation> levelRelations = memberLevelRelationService.getMemberLevelsByUserId(userId);

        if (CollectionUtil.isNotEmpty(levelRelations)) {
            List<String> levels = levelRelations.stream().map(MemberLevelRelation::getMemberLevelId).collect(Collectors.toList());
           return SpringContextHolder.getBean(IMemberLevelRuleService.class).checkApplyShopByLevelId(levels);
        }
        return false;
    }

    @Override
    public MemberLevelRuleVo getByMemberLevelId(Long memberLevelId) {

        MemberLevelRuleVo memberLevelRuleVo = new MemberLevelRuleVo();

        LambdaQueryWrapper<MemberLevelRule>wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberLevelRule::getDeleted,CommonConstants.NUMBER_ZERO);
        wrapper.eq(MemberLevelRule::getMemberLevelId,memberLevelId);
        MemberLevelRule memberLevelRule = this.getOne(wrapper);
        BeanUtils.copyProperties(memberLevelRule,memberLevelRuleVo);
        return memberLevelRuleVo;
    }

    @Override
    public List<String> getMemberLevelIds(Integer sort, String mainId) {
        return this.baseMapper.getMemberLevelIds(sort,mainId);
    }


}
