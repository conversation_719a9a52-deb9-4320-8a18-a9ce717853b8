package com.medusa.gruul.account.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.*;
import com.medusa.gruul.account.api.enums.CommissionTypeEnum;
import com.medusa.gruul.account.api.enums.DirectConditionRelationEnum;
import com.medusa.gruul.account.api.enums.GoldenOrderTypeEnum;
import com.medusa.gruul.account.api.enums.UpgradeTypeEnum;
import com.medusa.gruul.account.api.model.message.AccountReturnBalanceMessage;
import com.medusa.gruul.account.api.model.message.UpgradeMemberLevelMessage;
import com.medusa.gruul.account.mapper.MiniAccountMapper;
import com.medusa.gruul.account.model.dto.MiniAccountCommissionDto;
import com.medusa.gruul.account.model.dto.MiniAccountGoldenDto;
import com.medusa.gruul.account.model.param.MemberLevelRelationParam;
import com.medusa.gruul.account.model.param.MemberLevelRuleMessageParam;
import com.medusa.gruul.account.model.param.MemberTypeParam;
import com.medusa.gruul.account.model.param.MiniAccountMemberUpParam;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.mq.Sender;
import com.medusa.gruul.account.service.*;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.*;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.LocalDateTimeUtils;
import com.medusa.gruul.common.data.tenant.TenantContextHolder;
import com.medusa.gruul.goods.api.entity.MemberLevelGoodsAgainPrice;
import com.medusa.gruul.goods.api.entity.MemberLevelGoodsPrice;
import com.medusa.gruul.goods.api.entity.RewardSchemeDet;
import com.medusa.gruul.goods.api.entity.SkuStock;
import com.medusa.gruul.goods.api.enums.*;
import com.medusa.gruul.goods.api.feign.RemoteGoodsService;
import com.medusa.gruul.goods.api.model.param.manager.RewardSchemeDetParam;
import com.medusa.gruul.goods.api.model.vo.manager.*;
import com.medusa.gruul.order.api.enums.BuyTypeEnum;
import com.medusa.gruul.order.api.enums.MemberPriceTypeEnum;
import com.medusa.gruul.order.api.feign.RemoteOrderService;
import com.medusa.gruul.order.api.model.OrderItemVo;
import com.medusa.gruul.order.api.model.OrderVo;
import com.medusa.gruul.shops.api.entity.ShopCommissionRule;
import com.medusa.gruul.shops.api.enums.CommissionRuleTypeEnum;
import com.medusa.gruul.shops.api.feign.RemoteShopsService;
import com.medusa.gruul.shops.api.model.PrizeMemberMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Member;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: plh
 * @Description: 服务间调用service
 * @Date: Created in 21:15 2025/6/18
 */
@Service
@Slf4j
public class RemoteMiniAccountServiceImpl extends ServiceImpl<MiniAccountMapper, MiniAccount>implements IRemoteMiniAccountService {

    @Autowired
    private RemoteOrderService remoteOrderService;
    @Autowired
    private RemoteGoodsService remoteGoodsService;
    @Autowired
    private IMiniAccountExtendsService miniAccountExtendsService;
    @Autowired
    private IMiniAccountService miniAccountService;
    @Autowired
    private IMiniAccountCommissionService miniAccountCommissionService;
    @Autowired
    private IMiniAccountGoldenService miniAccountGoldenService;
    @Autowired
    private IMiniAccountMemberUpService miniAccountMemberUpService;
    @Autowired
    private IMemberLevelRelationService memberLevelRelationService;
    @Autowired
    private IMemberLevelRuleMessageService memberLevelRuleMessageService;
    @Autowired
    private IMemberLevelRuleProductService memberLevelRuleProductService;
    @Autowired
    private IMemberLevelService memberLevelService;
    @Autowired
    private IMemberLevelRuleService memberLevelRuleService;
    @Autowired
    private IMemberTypeService memberTypeService;
    @Autowired
    private RemoteShopsService remoteShopsService;
    @Autowired
    private Sender sender;
    @Autowired
    private IMiniAccountIntegralService miniAccountIntegralService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handCompletedOrder(UpgradeMemberLevelMessage message) {
        //1.处理奖励方案内容
        handleReward(message);
        //2.处理积分内容
        handleIntegral(message);
        //3.处理升级内容
        upgradeMemberLevel(message);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleIntegral(UpgradeMemberLevelMessage message) {
        if(message.getOrderId()!=null){
            log.info("开始处理订单积分内容");
            OrderVo orderVo = remoteOrderService.orderInfo(message.getOrderId());
            miniAccountIntegralService.handleIntegral(orderVo);
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleReward(UpgradeMemberLevelMessage message) {
        if(message.getOrderId()!=null){
            log.info("开始处理订单奖励内容");
            OrderVo orderVo = remoteOrderService.orderInfo(message.getOrderId());
            log.info("订单信息，orderVo: {}", JSONObject.toJSONString(orderVo));
            List<OrderItemVo> orderItemList = orderVo.getOrderItemList();
            log.info("订单明细信息，orderItemList: {}", JSONObject.toJSONString(orderItemList));
            MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(orderVo.getUserId());
            log.info("用户扩展信息，miniAccountExtends: {}", JSONObject.toJSONString(miniAccountExtends));
            MiniAccount miniAccount = miniAccountService.getByShopUserId(orderVo.getUserId());
            log.info("用户信息，miniAccount: {}", JSONObject.toJSONString(miniAccount));
            ShopCommissionRule commissionRule = remoteShopsService.getCommissionRule();
            if(commissionRule == null || (commissionRule!=null&&commissionRule.getRuleType() == null)){
                throw new ServiceException("请先设置佣金划转类型！");
            }
            if(commissionRule == null || (commissionRule!=null&&commissionRule.getCommissionTransferRate() == null)){
                throw new ServiceException("请先设置佣金划转比例/金额！");
            }
            Integer ruleType = commissionRule.getRuleType();
            BigDecimal commissionTransferRate = BigDecimal.ONE;
            BigDecimal goldenTransferRate = BigDecimal.ZERO;
            //
            BigDecimal commissionTransferAmount = BigDecimal.ZERO;
            if(ruleType == CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){
                commissionTransferAmount = commissionRule.getCommissionTransferRate();
            }else if(ruleType == CommissionRuleTypeEnum.PERCENTAGE.getType()){
                if(commissionRule!=null&&commissionRule.getCommissionTransferRate()!=null){
                    commissionTransferRate = commissionRule.getCommissionTransferRate().divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP);
                    goldenTransferRate = BigDecimal.ONE.subtract(commissionTransferRate);
                }
            }else{
                throw new ServiceException("佣金划转类型设置错误，必须为固定金额或百分比！");
            }




            //获取区域会员类型
            MemberType regionMemberType = memberTypeService.getRegionMemberType();
            log.info("区域会员类型，regionMemberType: {}", JSONObject.toJSONString(regionMemberType));
            Long regionMemberTypeId = null;
            if(regionMemberType!=null){
                regionMemberTypeId = Long.valueOf(regionMemberType.getId());
            }


            //1.循环订单明细
            for (OrderItemVo orderItemVo : orderItemList) {
                //订单会员类型id
                Long orderMemberTypeId = orderItemVo.getMemberTypeId();
                //获取符合条件奖励方案明细规则
                List<RewardSchemeDetVo> rewardSchemeDetList = getRewardSchemeDetVo(orderVo,orderItemVo,CommissionAmountSourceEnum.ORDER_PAYMENT_AMOUNT.getStatus(),orderMemberTypeId,regionMemberTypeId,0);

                log.info("获取符合条件奖励方案明细规则rewardSchemeDetList：{}",JSONObject.toJSONString(rewardSchemeDetList));
                if(rewardSchemeDetList!=null&&rewardSchemeDetList.size()>0){
                    List<MiniAccountRewardVo>miniAccountRewardList = new ArrayList<>();
                    //1.分佣类型为分佣
                    handleCommission(rewardSchemeDetList,orderVo.getOrderUserId(),orderVo.getId(),orderItemVo.getRealAmount(),
                            orderItemVo.getMemberTypeId(),miniAccountRewardList);
                    //2.分佣类型为平级
                    handleSameLevel(rewardSchemeDetList,orderVo.getOrderUserId(),orderVo.getId(),
                            orderItemVo.getRealAmount(),orderItemVo.getMemberTypeId(),miniAccountRewardList);
                    //3.分佣类型为循环分佣
                    handleCycleCommission(rewardSchemeDetList,orderVo,orderItemVo,miniAccountRewardList);
                    //4.分佣类型为级差
                    if(orderVo.getMallOrderType()!=ProductTypeEnum.UPGRADE_PRODUCT.getStatus()){
                        handleDifferCommission(rewardSchemeDetList,orderVo.getOrderUserId(),orderVo.getId(),orderItemVo.getRealAmount(),
                                orderItemVo.getMemberTypeId(),orderItemVo.getProductSkuId(),orderItemVo.getProductId(),
                                orderItemVo.getBuyType(),orderItemVo.getProductQuantity(),
                                miniAccountRewardList);
                    }
                    //5.分佣类型为团队
                    handleTeamCommission(rewardSchemeDetList,orderVo.getOrderUserId(),
                            orderVo.getId(),orderItemVo.getRealAmount(),
                            orderItemVo.getMemberTypeId(),miniAccountRewardList);
                    if(miniAccountRewardList!=null&&miniAccountRewardList.size()>0){
                        for (MiniAccountRewardVo miniAccountRewardVo : miniAccountRewardList) {

                            BigDecimal amount = miniAccountRewardVo.getAmount();
                            if(ruleType == CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){
                                BigDecimal commission = BigDecimal.ZERO;
                                BigDecimal golden = BigDecimal.ZERO;
                                if(amount.compareTo(commissionTransferAmount)>=0){
                                    commission = commissionTransferAmount;
                                    golden = amount.subtract(commissionTransferAmount);
                                }else{
                                    commission = amount;
                                    golden = BigDecimal.ZERO;
                                }
                                if(commission.compareTo(BigDecimal.ZERO)>0){
                                    MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                                    dto.setUserId(miniAccountExtendsService.findByUserId(miniAccountRewardVo.getUserId()).getShopUserId());
                                    dto.setOrderId(orderVo.getId()+"");
                                    dto.setCommissionType(miniAccountRewardVo.getCommissionType());
                                    dto.setRewardId(miniAccountRewardVo.getRewardId());
                                    dto.setRewardDetId(miniAccountRewardVo.getRewardDetId());
                                    dto.setAmount(commission);
                                    dto.setSourceShopUserId(miniAccountExtends.getShopUserId());
                                    dto.setRemark(miniAccountRewardVo.getRemark());
                                    dto.setSource(CommonConstants.NUMBER_ZERO);
                                    dto.setMemberTypeId(orderMemberTypeId);
                                    miniAccountCommissionService.addMiniAccountCommission(dto);
                                }
                                if(goldenTransferRate.compareTo(BigDecimal.ZERO)>0){
                                    MiniAccountGoldenDto dto = new MiniAccountGoldenDto();
                                    dto.setUserId(miniAccountExtendsService.findByUserId(miniAccountRewardVo.getUserId()).getShopUserId());
                                    dto.setCommissionType(miniAccountRewardVo.getCommissionType());
                                    dto.setOrderId(orderVo.getId());
                                    dto.setRewardId(miniAccountRewardVo.getRewardId());
                                    dto.setRewardDetId(miniAccountRewardVo.getRewardDetId());
                                    dto.setAmount(golden);
                                    dto.setOrderType(CommonConstants.NUMBER_ONE);
                                    dto.setSourceShopUserId(miniAccountExtends.getShopUserId());
                                    dto.setRemark(miniAccountRewardVo.getRemark());
                                    dto.setSource(CommonConstants.NUMBER_ZERO);
                                    dto.setMemberTypeId(orderMemberTypeId);
                                    miniAccountGoldenService.addMiniAccountGolden(dto);
                                }
                            }else{
                                BigDecimal commission = amount.multiply(commissionTransferRate);
                                BigDecimal golden = amount.subtract(commission);
                                if(commission.compareTo(BigDecimal.ZERO)>0){
                                    MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                                    dto.setUserId(miniAccountExtendsService.findByUserId(miniAccountRewardVo.getUserId()).getShopUserId());
                                    dto.setOrderId(orderVo.getId()+"");
                                    dto.setCommissionType(miniAccountRewardVo.getCommissionType());
                                    dto.setRewardId(miniAccountRewardVo.getRewardId());
                                    dto.setRewardDetId(miniAccountRewardVo.getRewardDetId());
                                    dto.setAmount(commission);
                                    dto.setSourceShopUserId(miniAccountExtends.getShopUserId());
                                    dto.setRemark(miniAccountRewardVo.getRemark());
                                    dto.setSource(CommonConstants.NUMBER_ZERO);
                                    dto.setMemberTypeId(orderMemberTypeId);
                                    miniAccountCommissionService.addMiniAccountCommission(dto);
                                }
                                if(goldenTransferRate.compareTo(BigDecimal.ZERO)>0){
                                    MiniAccountGoldenDto dto = new MiniAccountGoldenDto();
                                    dto.setUserId(miniAccountExtendsService.findByUserId(miniAccountRewardVo.getUserId()).getShopUserId());
                                    dto.setCommissionType(miniAccountRewardVo.getCommissionType());
                                    dto.setOrderId(orderVo.getId());
                                    dto.setRewardId(miniAccountRewardVo.getRewardId());
                                    dto.setRewardDetId(miniAccountRewardVo.getRewardDetId());
                                    dto.setAmount(golden);
                                    dto.setOrderType(CommonConstants.NUMBER_ONE);
                                    dto.setSourceShopUserId(miniAccountExtends.getShopUserId());
                                    dto.setRemark(miniAccountRewardVo.getRemark());
                                    dto.setSource(CommonConstants.NUMBER_ZERO);
                                    dto.setMemberTypeId(orderMemberTypeId);
                                    miniAccountGoldenService.addMiniAccountGolden(dto);
                                }
                            }
                        }
                    }

                    List<RewardSchemeDetVo> rewardSchemeDetList2 = getRewardSchemeDetVo(orderVo,orderItemVo,CommissionAmountSourceEnum.COMMISSION_AMOUNT.getStatus(),orderItemVo.getMemberTypeId(),regionMemberTypeId,0);
                    log.info("获取符合条件奖励方案明细规则rewardSchemeDetList2：{}",JSONObject.toJSONString(rewardSchemeDetList));
                    if(rewardSchemeDetList!=null&&rewardSchemeDetList.size()>0){
                        for (MiniAccountRewardVo miniAccountRewardVo : miniAccountRewardList) {
                            String shopUserId = miniAccountExtendsService.findByUserId(miniAccountRewardVo.getUserId()).getShopUserId();
                            List<MiniAccountRewardVo>miniAccountRewardList2 = new ArrayList<>();
                            //1.分佣类型为分佣
                            handleCommission(rewardSchemeDetList2,shopUserId,null,miniAccountRewardVo.getAmount(),
                                    orderItemVo.getMemberTypeId(),miniAccountRewardList2);
                            //2.分佣类型为平级
                            handleSameLevel(rewardSchemeDetList2,shopUserId,null,
                                    miniAccountRewardVo.getAmount(),orderItemVo.getMemberTypeId(),miniAccountRewardList2);
                            //3.级差
                            handleDifferCommission(rewardSchemeDetList2,shopUserId,null,miniAccountRewardVo.getAmount(),
                                    orderItemVo.getMemberTypeId(),null,null,
                                    null,null,
                                    miniAccountRewardList2);
                            //4.分佣类型为团队
                            handleTeamCommission(rewardSchemeDetList2,shopUserId,
                                    null,miniAccountRewardVo.getAmount(),
                                    orderItemVo.getMemberTypeId(),miniAccountRewardList2);
                            if(miniAccountRewardList2!=null&&miniAccountRewardList2.size()>0){
                                for (MiniAccountRewardVo miniAccountRewardVo2 : miniAccountRewardList2) {
                                    BigDecimal amount = miniAccountRewardVo2.getAmount();
                                    if(ruleType == CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){
                                        BigDecimal commission = BigDecimal.ZERO;
                                        BigDecimal golden = BigDecimal.ZERO;
                                        if(amount.compareTo(commissionTransferAmount)>=0){
                                            commission = commissionTransferAmount;
                                            golden = amount.subtract(commissionTransferAmount);
                                        }else{
                                            commission = amount;
                                            golden = BigDecimal.ZERO;
                                        }
                                        if(commission.compareTo(BigDecimal.ZERO)>0){
                                            MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                                            dto.setUserId(miniAccountExtendsService.findByUserId(miniAccountRewardVo.getUserId()).getShopUserId());
                                            dto.setOrderId(orderVo.getId()+"");
                                            dto.setCommissionType(miniAccountRewardVo.getCommissionType());
                                            dto.setRewardId(miniAccountRewardVo.getRewardId());
                                            dto.setRewardDetId(miniAccountRewardVo.getRewardDetId());
                                            dto.setAmount(commission);
                                            dto.setSourceShopUserId(miniAccountExtends.getShopUserId());
                                            dto.setRemark(miniAccountRewardVo.getRemark());
                                            dto.setSource(CommonConstants.NUMBER_ZERO);
                                            dto.setMemberTypeId(orderMemberTypeId);
                                            miniAccountCommissionService.addMiniAccountCommission(dto);
                                        }
                                        if(goldenTransferRate.compareTo(BigDecimal.ZERO)>0){
                                            MiniAccountGoldenDto dto = new MiniAccountGoldenDto();
                                            dto.setUserId(miniAccountExtendsService.findByUserId(miniAccountRewardVo.getUserId()).getShopUserId());
                                            dto.setCommissionType(miniAccountRewardVo.getCommissionType());
                                            dto.setOrderId(orderVo.getId());
                                            dto.setRewardId(miniAccountRewardVo.getRewardId());
                                            dto.setRewardDetId(miniAccountRewardVo.getRewardDetId());
                                            dto.setAmount(golden);
                                            dto.setOrderType(CommonConstants.NUMBER_ONE);
                                            dto.setSourceShopUserId(miniAccountExtends.getShopUserId());
                                            dto.setRemark(miniAccountRewardVo.getRemark());
                                            dto.setSource(CommonConstants.NUMBER_ZERO);
                                            dto.setMemberTypeId(orderMemberTypeId);
                                            miniAccountGoldenService.addMiniAccountGolden(dto);
                                        }
                                    }else{
                                        BigDecimal commission = amount.multiply(commissionTransferRate);
                                        BigDecimal golden = amount.subtract(commission);
                                        if(commission.compareTo(BigDecimal.ZERO)>0){
                                            MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                                            dto.setUserId(miniAccountExtendsService.findByUserId(miniAccountRewardVo.getUserId()).getShopUserId());
                                            dto.setOrderId(orderVo.getId()+"");
                                            dto.setCommissionType(miniAccountRewardVo.getCommissionType());
                                            dto.setRewardId(miniAccountRewardVo.getRewardId());
                                            dto.setRewardDetId(miniAccountRewardVo.getRewardDetId());
                                            dto.setAmount(commission);
                                            dto.setSourceShopUserId(miniAccountExtends.getShopUserId());
                                            dto.setRemark(miniAccountRewardVo.getRemark());
                                            dto.setSource(CommonConstants.NUMBER_ZERO);
                                            dto.setMemberTypeId(orderMemberTypeId);
                                            miniAccountCommissionService.addMiniAccountCommission(dto);
                                        }
                                        if(goldenTransferRate.compareTo(BigDecimal.ZERO)>0){
                                            MiniAccountGoldenDto dto = new MiniAccountGoldenDto();
                                            dto.setUserId(miniAccountExtendsService.findByUserId(miniAccountRewardVo.getUserId()).getShopUserId());
                                            dto.setCommissionType(miniAccountRewardVo.getCommissionType());
                                            dto.setOrderId(orderVo.getId());
                                            dto.setRewardId(miniAccountRewardVo.getRewardId());
                                            dto.setRewardDetId(miniAccountRewardVo.getRewardDetId());
                                            dto.setAmount(golden);
                                            dto.setOrderType(CommonConstants.NUMBER_ONE);
                                            dto.setSourceShopUserId(miniAccountExtends.getShopUserId());
                                            dto.setRemark(miniAccountRewardVo.getRemark());
                                            dto.setSource(CommonConstants.NUMBER_ZERO);
                                            dto.setMemberTypeId(orderMemberTypeId);
                                            miniAccountGoldenService.addMiniAccountGolden(dto);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                //区域会员
                if(regionMemberType!=null){
                    List<RewardSchemeDetVo> rewardSchemeDetList3 = getRewardSchemeDetVo(orderVo,orderItemVo,null,orderMemberTypeId,regionMemberTypeId,1);
                    if(rewardSchemeDetList3!=null){
                        List<MemberLevelRelation>E = new ArrayList<>();
                        String countyCode = orderVo.getCountyCode();
                        MemberLevelRelation E1 = memberLevelRelationService.getMemberLevelRelationByCode(countyCode, regionMemberType.getId());
                        if(E1!=null){
                            E.add(E1);
                        }
                        String cityCode = orderVo.getCityCode();
                        MemberLevelRelation E2 = memberLevelRelationService.getMemberLevelRelationByCode(cityCode, regionMemberType.getId());
                        if(E2!=null){
                            E.add(E2);
                        }
                        String provinceCode = orderVo.getProvinceCode();
                        MemberLevelRelation E3 = memberLevelRelationService.getMemberLevelRelationByCode(provinceCode, regionMemberType.getId());
                        if(E3!=null){
                            E.add(E3);
                        }
                        if(E!=null&&E.size()>0){
                            List<MiniAccountRewardVo>regionMiniAccountRewardList = new ArrayList<>();
                            for (MemberLevelRelation memberLevelRelation : E) {
                                //过滤收益等级
                                List<RewardSchemeDetVo> memberRewardSchemeDetList = rewardSchemeDetList3.stream().filter(e -> StringUtils.isEmpty(e.getMemberLevelId()) || (StringUtils.isNotEmpty(e.getMemberLevelId()) &&
                                        e.getMemberLevelId().indexOf(memberLevelRelation.getMemberLevelId()) != -1)).collect(Collectors.toList());
                                log.info("过滤会员类型等级奖励方案明细规则memberRewardSchemeDetList：{}",JSONObject.toJSONString(memberRewardSchemeDetList));
                                if(memberRewardSchemeDetList!=null&&memberRewardSchemeDetList.size()>0){
                                    //过滤直推会员等级
                                    //查询直推属于区域类型会员体系的会员数量
//                                    Integer count = miniAccountService.getRegionMemberCount(regionMemberType.getId(),memberLevelRelation.getUserId());
//                                    //查询业务员直推会员数
//                                    List<RewardSchemeDetVo> directMemberRewardSchemeDetList = memberRewardSchemeDetList.stream().filter(e -> e.getDirectMemberCount() == null
//                                            || (e.getDirectMemberCount() != null && e.getDirectMemberCount() > 0 &&
//                                            count >= e.getDirectMemberCount())).collect(Collectors.toList());
//                                    log.info("过滤直推会员数奖励规则directMemberRewardSchemeDetList：{}",JSONObject.toJSONString(directMemberRewardSchemeDetList));

                                    List<RewardSchemeDetVo>directMemberRewardSchemeDetList = new ArrayList<>();
                                    for (RewardSchemeDetVo rewardSchemeDetVo : memberRewardSchemeDetList) {
                                        Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), memberLevelRelation.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                        if(b){
                                            directMemberRewardSchemeDetList.add(rewardSchemeDetVo);
                                        }
                                    }

                                    if(directMemberRewardSchemeDetList!=null&&directMemberRewardSchemeDetList.size()>0){

                                        //1.分佣类型为分佣
                                            handleRegionCommission(directMemberRewardSchemeDetList,orderItemVo.getRealAmount(),
                                                memberLevelRelation.getUserId(),orderVo.getId(),regionMiniAccountRewardList);
                                        //2.分佣类型为平级
                                        handleRegionSameLevel(directMemberRewardSchemeDetList,memberLevelRelation.getUserId(),orderVo.getOrderUserId(),
                                                Long.valueOf(regionMemberType.getId()),orderItemVo.getRealAmount(),
                                                orderVo.getId(),regionMiniAccountRewardList);
                                        //3.分佣类型为循环分佣
                                        handleRegionCycleCommission(directMemberRewardSchemeDetList,memberLevelRelation,orderItemVo.getRealAmount(),
                                                orderVo.getId(),regionMiniAccountRewardList);
                                        //4.分佣类型为级差
                                        handleRegionDifferCommission(directMemberRewardSchemeDetList,
                                                rewardSchemeDetList3,
                                                memberLevelRelation,
                                                orderVo,orderItemVo.getRealAmount(),orderVo.getId(),
                                                E,regionMiniAccountRewardList);

                                    }
                                }
                            }

                            if(regionMiniAccountRewardList!=null&&regionMiniAccountRewardList.size()>0){
                                for (MiniAccountRewardVo miniAccountRewardVo : regionMiniAccountRewardList) {
                                    BigDecimal amount = miniAccountRewardVo.getAmount();
                                    if(ruleType == CommissionRuleTypeEnum.REGULAR_AMOUNT.getType()){
                                        BigDecimal commission = BigDecimal.ZERO;
                                        BigDecimal golden = BigDecimal.ZERO;
                                        if(amount.compareTo(commissionTransferAmount)>=0){
                                            commission = commissionTransferAmount;
                                            golden = amount.subtract(commissionTransferAmount);
                                        }else{
                                            commission = amount;
                                            golden = BigDecimal.ZERO;
                                        }
                                        if(commission.compareTo(BigDecimal.ZERO)>0){
                                            MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                                            dto.setUserId(miniAccountExtendsService.findByUserId(miniAccountRewardVo.getUserId()).getShopUserId());
                                            dto.setOrderId(orderVo.getId()+"");
                                            dto.setCommissionType(miniAccountRewardVo.getCommissionType());
                                            dto.setRewardId(miniAccountRewardVo.getRewardId());
                                            dto.setRewardDetId(miniAccountRewardVo.getRewardDetId());
                                            dto.setAmount(commission);
                                            dto.setSourceShopUserId(miniAccountExtends.getShopUserId());
                                            dto.setRemark(miniAccountRewardVo.getRemark());
                                            dto.setSource(CommonConstants.NUMBER_ZERO);
                                            dto.setMemberTypeId(regionMemberTypeId);
                                            miniAccountCommissionService.addMiniAccountCommission(dto);
                                        }
                                        if(goldenTransferRate.compareTo(BigDecimal.ZERO)>0){
                                            MiniAccountGoldenDto dto = new MiniAccountGoldenDto();
                                            dto.setUserId(miniAccountExtendsService.findByUserId(miniAccountRewardVo.getUserId()).getShopUserId());
                                            dto.setCommissionType(miniAccountRewardVo.getCommissionType());
                                            dto.setOrderId(orderVo.getId());
                                            dto.setRewardId(miniAccountRewardVo.getRewardId());
                                            dto.setRewardDetId(miniAccountRewardVo.getRewardDetId());
                                            dto.setAmount(golden);
                                            dto.setOrderType(CommonConstants.NUMBER_ONE);
                                            dto.setSourceShopUserId(miniAccountExtends.getShopUserId());
                                            dto.setRemark(miniAccountRewardVo.getRemark());
                                            dto.setSource(CommonConstants.NUMBER_ZERO);
                                            dto.setMemberTypeId(regionMemberTypeId);
                                            miniAccountGoldenService.addMiniAccountGolden(dto);
                                        }
                                    }else{
                                        BigDecimal commission = amount.multiply(commissionTransferRate);
                                        BigDecimal golden = amount.subtract(commission);
                                        if(commission.compareTo(BigDecimal.ZERO)>0){
                                            MiniAccountCommissionDto dto = new MiniAccountCommissionDto();
                                            dto.setUserId(miniAccountExtendsService.findByUserId(miniAccountRewardVo.getUserId()).getShopUserId());
                                            dto.setOrderId(orderVo.getId()+"");
                                            dto.setCommissionType(miniAccountRewardVo.getCommissionType());
                                            dto.setRewardId(miniAccountRewardVo.getRewardId());
                                            dto.setRewardDetId(miniAccountRewardVo.getRewardDetId());
                                            dto.setAmount(commission);
                                            dto.setSourceShopUserId(miniAccountExtends.getShopUserId());
                                            dto.setRemark(miniAccountRewardVo.getRemark());
                                            dto.setSource(CommonConstants.NUMBER_ZERO);
                                            dto.setMemberTypeId(regionMemberTypeId);
                                            miniAccountCommissionService.addMiniAccountCommission(dto);
                                        }
                                        if(goldenTransferRate.compareTo(BigDecimal.ZERO)>0){
                                            MiniAccountGoldenDto dto = new MiniAccountGoldenDto();
                                            dto.setUserId(miniAccountExtendsService.findByUserId(miniAccountRewardVo.getUserId()).getShopUserId());
                                            dto.setCommissionType(miniAccountRewardVo.getCommissionType());
                                            dto.setOrderId(orderVo.getId());
                                            dto.setRewardId(miniAccountRewardVo.getRewardId());
                                            dto.setRewardDetId(miniAccountRewardVo.getRewardDetId());
                                            dto.setAmount(golden);
                                            dto.setOrderType(CommonConstants.NUMBER_ONE);
                                            dto.setSourceShopUserId(miniAccountExtends.getShopUserId());
                                            dto.setRemark(miniAccountRewardVo.getRemark());
                                            dto.setSource(CommonConstants.NUMBER_ZERO);
                                            dto.setMemberTypeId(regionMemberTypeId);
                                            miniAccountGoldenService.addMiniAccountGolden(dto);
                                        }
                                    }
                                }
                            }

                        }
                    }
                }
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upgradeMemberLevel(UpgradeMemberLevelMessage message) {
        log.info("开始处理订单升级内容");
        //订单升级
        if(message.getMessageType().equals(UpgradeTypeEnum.ORDER.getType())){


            OrderVo orderVo = remoteOrderService.orderInfo(message.getOrderId());
            log.info("订单信息，orderVo: {}", JSONObject.toJSONString(orderVo));

            MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByShopUserId(orderVo.getUserId());
            log.info("用户扩展信息，miniAccountExtends: {}", JSONObject.toJSONString(miniAccountExtends));

            MiniAccount miniAccount = miniAccountService.getByShopUserId(orderVo.getUserId());
            log.info("用户信息，miniAccount: {}", JSONObject.toJSONString(miniAccount));

            //订单明细的实际支付金额
            BigDecimal payAmount = BigDecimal.ZERO;

            for (OrderItemVo orderItemVo : orderVo.getOrderItemList()) {
                payAmount = payAmount.add(orderItemVo.getRealAmount());
            }

            //商城订单类型如果是升级订单
            if(orderVo.getMallOrderType() == ProductTypeEnum.UPGRADE_PRODUCT.getStatus()){
                //订单升级会员类型id
                Long upgradeMemberTypeId = orderVo.getUpgradeMemberTypeId();
                //订单升级会员等级id
                Long upgradeMemberLeverId = orderVo.getUpgradeMemberLeverId();
                MiniAccountMemberUpParam param = new MiniAccountMemberUpParam();
                param.setMemberTypeId(upgradeMemberTypeId);
                param.setUserId(orderVo.getUserId());
                //查询会员的升级消费记录
                MiniAccountMemberUp miniAccountMemberUp = miniAccountMemberUpService.getMiniAccountMemberUp(param);
                BigDecimal amount = BigDecimal.ZERO;
                //判断会员升级类型下的会员升级消费额记录是否存在
                if(miniAccountMemberUp!=null){
                    //存在-更新会员升级类型下的会员升级消费额记录
                    amount = amount.add(payAmount).add(miniAccountMemberUp.getAmount());
                    miniAccountMemberUp.setAmount(amount);
                    log.info("更新会员升级消费额，miniAccountMemberUp: {}", JSONObject.toJSONString(miniAccountMemberUp));
                    miniAccountMemberUpService.updateById(miniAccountMemberUp);
                }else{
                    //不存在-新增会员升级类型下的会员升级消费额记录
                    amount = amount.add(payAmount);
                    miniAccountMemberUp = new MiniAccountMemberUp();
                    miniAccountMemberUp.setMemberTypeId(upgradeMemberTypeId);
                    miniAccountMemberUp.setUserId(orderVo.getUserId());
                    miniAccountMemberUp.setAmount(amount);
                    log.info("新增会员升级消费额，miniAccountMemberUp: {}", JSONObject.toJSONString(miniAccountMemberUp));
                    miniAccountMemberUpService.save(miniAccountMemberUp);
                }

                MemberLevelRuleMessageParam memberLevelRuleMessageParam = new MemberLevelRuleMessageParam();
                memberLevelRuleMessageParam.setMemberTypeId(upgradeMemberTypeId);
                //获取订单升级会员类型下的会员等级规则信息
                MemberLevelRuleMessageVo memberLevelRuleMessage = memberLevelRuleMessageService.getMemberLevelRuleMessage(memberLevelRuleMessageParam);
                log.info("会员类型{}对应会员规则信息，memberLevelRuleMessage: {}", upgradeMemberTypeId,JSONObject.toJSONString(memberLevelRuleMessage));
                if(memberLevelRuleMessage == null || memberLevelRuleMessage.equals("")){
                    log.info("会员类型{}对应会员规则信息不存在",upgradeMemberTypeId);
                    return;
                }
                //判断订单会员升级等级id是否存在
                if(upgradeMemberLeverId!=null){
                    //指定商品数升级方式
                    handleAppointGoods(memberLevelRuleMessage,miniAccount,upgradeMemberLeverId);
                }else{

                    List<MemberLevelRuleMessageVo> memberLevelRuleMessageList = memberLevelRuleMessageService.getMemberLevelRuleMessageList();
                    if(memberLevelRuleMessageList!=null&&memberLevelRuleMessageList.size()>0){
                        for (MemberLevelRuleMessageVo memberLevelRuleMessageVo : memberLevelRuleMessageList) {
                            Long memberTypeId = memberLevelRuleMessageVo.getMemberTypeId();

                            String orderMemberTypeIds = memberLevelRuleMessageVo.getOrderMemberTypeIds();
                            if(StringUtils.isNotEmpty(orderMemberTypeIds)){
                                if(orderMemberTypeIds.contains(upgradeMemberTypeId+"")){

                                    MemberLevelRuleMessageParam upgradeParam = new MemberLevelRuleMessageParam();
                                    upgradeParam.setMemberTypeId(memberTypeId);
                                    MemberLevelRuleMessageVo upgradeMessage = memberLevelRuleMessageService.getMemberLevelRuleMessage(upgradeParam);
                                    for (String upgradeType : upgradeMessage.getType().split(",")) {
                                        //升级方式
                                        if(upgradeType.equals(MemberLevelRuleTypeEnum.AMOUNT.getStatus())){
                                            //消费额升级
                                            handleAmountUpgrade(upgradeMessage,miniAccount,amount);
                                        }
                                        if(upgradeType.equals(MemberLevelRuleTypeEnum.INTEGRAL.getStatus())){
                                            //积分升级
                                            handleIntegralUpgrade(upgradeMessage,miniAccount);
                                        }
                                        if(upgradeType.equals(MemberLevelRuleTypeEnum.MEMBER_AMOUNT.getStatus())){
                                            //商品消费额升级
                                            handleMemberAmountUpgrade(upgradeMessage,miniAccount,amount);
                                        }
                                        if(upgradeType.equals(MemberLevelRuleTypeEnum.TEAM_PERFORMANCE.getStatus())){
                                            //团队业绩升级
                                            handleTeamPerformance(upgradeMessage,miniAccount);
                                        }
                                    }
                                }
                            }else{
                                if(memberTypeId.equals(upgradeMemberTypeId)){
                                    //循环升级方式
                                    if(StringUtils.isNotEmpty(memberLevelRuleMessageVo.getType())){
                                        for (String upgradeType : memberLevelRuleMessageVo.getType().split(",")) {
                                            //升级方式
                                            if(upgradeType.equals(MemberLevelRuleTypeEnum.AMOUNT.getStatus())){
                                                //消费额升级
                                                handleAmountUpgrade(memberLevelRuleMessageVo,miniAccount,amount);
                                            }
                                            if(upgradeType.equals(MemberLevelRuleTypeEnum.INTEGRAL.getStatus())){
                                                //积分升级
                                                handleIntegralUpgrade(memberLevelRuleMessageVo,miniAccount);
                                            }
                                            if(upgradeType.equals(MemberLevelRuleTypeEnum.MEMBER_AMOUNT.getStatus())){
                                                //商品消费额升级
                                                handleMemberAmountUpgrade(memberLevelRuleMessageVo,miniAccount,amount);
                                            }
                                            if(upgradeType.equals(MemberLevelRuleTypeEnum.TEAM_PERFORMANCE.getStatus())){
                                                //团队业绩升级
                                                handleTeamPerformance(memberLevelRuleMessageVo,miniAccount);
                                            }
                                        }
                                    }else{
                                        log.info("会员类型{}对应升级方式不存在",memberLevelRuleMessage.getMemberTypeId());
                                    }
                                }
                            }
                        }
                    }
                }
            }else{
                //普通订单升级
                List<OrderItemVo> orderItemList = orderVo.getOrderItemList();
                if(orderItemList!=null&&orderItemList.size()>0){
                    Map<Long,BigDecimal>map = new HashMap<>();
                    for (OrderItemVo orderItemVo : orderItemList) {
                        Long upgradeMemberTypeId = orderItemVo.getMemberTypeId();
                        LambdaQueryWrapper<MemberLevelRuleProduct>wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(MemberLevelRuleProduct::getMemberTypeId,upgradeMemberTypeId);
                        List<MemberLevelRuleProduct> list = memberLevelRuleProductService.list(wrapper);
                        if(list!=null&&list.size()>0){
                            for (MemberLevelRuleProduct memberLevelRuleProduct : list) {
                                if(memberLevelRuleProduct.getSkuId().equals(orderItemVo.getProductSkuId()+"")
                                        &&memberLevelRuleProduct.getProductId().equals(orderItemVo.getProductId()+"")){
                                    BigDecimal realAmount = orderItemVo.getRealAmount();
                                    BigDecimal upgradeAmount = map.get(upgradeMemberTypeId);
                                    if(upgradeAmount!=null){
                                        map.put(upgradeMemberTypeId,realAmount.add(upgradeAmount));
                                    }else{
                                        map.put(upgradeMemberTypeId,realAmount);
                                    }
                                }
                            }
                        }else{
                            BigDecimal realAmount = orderItemVo.getRealAmount();
                            BigDecimal upgradeAmount = map.get(upgradeMemberTypeId);
                            if(upgradeAmount!=null){
                                map.put(upgradeMemberTypeId,realAmount.add(upgradeAmount));
                            }else{
                                map.put(upgradeMemberTypeId,realAmount);
                            }
                        }
                    }
                    if(map.size()>0){
                        for (Map.Entry<Long, BigDecimal> entry : map.entrySet()) {
                            //升级类型id
                            Long upgradeMemberTypeId = entry.getKey();
                            //升级金额
                            BigDecimal upgradeAmount = entry.getValue();
                            MiniAccountMemberUpParam param = new MiniAccountMemberUpParam();
                            param.setMemberTypeId(upgradeMemberTypeId);
                            param.setUserId(orderVo.getUserId());
                            //查询会员的升级消费记录
                            MiniAccountMemberUp miniAccountMemberUp = miniAccountMemberUpService.getMiniAccountMemberUp(param);
                            BigDecimal amount = BigDecimal.ZERO;
                            //判断会员升级类型下的会员升级消费额记录是否存在
                            if(miniAccountMemberUp!=null){
                                //存在-更新会员升级类型下的会员升级消费额记录
                                amount = amount.add(upgradeAmount).add(miniAccountMemberUp.getAmount());
                                miniAccountMemberUp.setAmount(amount);
                                log.info("更新会员升级消费额，miniAccountMemberUp: {}", JSONObject.toJSONString(miniAccountMemberUp));
                                miniAccountMemberUpService.updateById(miniAccountMemberUp);
                            }else{
                                //不存在-新增会员升级类型下的会员升级消费额记录
                                amount = amount.add(upgradeAmount);
                                miniAccountMemberUp = new MiniAccountMemberUp();
                                miniAccountMemberUp.setMemberTypeId(upgradeMemberTypeId);
                                miniAccountMemberUp.setUserId(orderVo.getUserId());
                                miniAccountMemberUp.setAmount(amount);
                                log.info("新增会员升级消费额，miniAccountMemberUp: {}", JSONObject.toJSONString(miniAccountMemberUp));
                                miniAccountMemberUpService.save(miniAccountMemberUp);
                            }

                            MemberLevelRuleMessageParam memberLevelRuleMessageParam = new MemberLevelRuleMessageParam();
                            memberLevelRuleMessageParam.setMemberTypeId(upgradeMemberTypeId);
                            //获取订单升级会员类型下的会员等级规则信息
                            MemberLevelRuleMessageVo memberLevelRuleMessage = memberLevelRuleMessageService.getMemberLevelRuleMessage(memberLevelRuleMessageParam);
                            log.info("会员类型{}对应会员规则信息，memberLevelRuleMessage: {}", upgradeMemberTypeId,JSONObject.toJSONString(memberLevelRuleMessage));

                            List<MemberLevelRuleMessageVo> memberLevelRuleMessageList = memberLevelRuleMessageService.getMemberLevelRuleMessageList();
                            if(memberLevelRuleMessageList!=null&&memberLevelRuleMessageList.size()>0){
                                for (MemberLevelRuleMessageVo memberLevelRuleMessageVo : memberLevelRuleMessageList) {
                                    Long memberTypeId = memberLevelRuleMessageVo.getMemberTypeId();

                                    String orderMemberTypeIds = memberLevelRuleMessageVo.getOrderMemberTypeIds();
                                    if(StringUtils.isNotEmpty(orderMemberTypeIds)){
                                        if(orderMemberTypeIds.contains(upgradeMemberTypeId+"")){
                                            MemberLevelRuleMessageParam upgradeParam = new MemberLevelRuleMessageParam();
                                            upgradeParam.setMemberTypeId(memberTypeId);
                                            MemberLevelRuleMessageVo upgradeMessage = memberLevelRuleMessageService.getMemberLevelRuleMessage(upgradeParam);
                                            for (String upgradeType : upgradeMessage.getType().split(",")) {
                                                //升级方式
                                                if(upgradeType.equals(MemberLevelRuleTypeEnum.AMOUNT.getStatus())){
                                                    //消费额升级
                                                    handleAmountUpgrade(upgradeMessage,miniAccount,amount);
                                                }
                                                if(upgradeType.equals(MemberLevelRuleTypeEnum.INTEGRAL.getStatus())){
                                                    //积分升级
                                                    handleIntegralUpgrade(upgradeMessage,miniAccount);
                                                }
                                                if(upgradeType.equals(MemberLevelRuleTypeEnum.MEMBER_AMOUNT.getStatus())){
                                                    //商品消费额升级
                                                    handleMemberAmountUpgrade(upgradeMessage,miniAccount,amount);
                                                }
                                                if(upgradeType.equals(MemberLevelRuleTypeEnum.TEAM_PERFORMANCE.getStatus())){
                                                    //团队业绩升级
                                                    handleTeamPerformance(upgradeMessage,miniAccount);
                                                }
                                            }
                                        }
                                    }else{
                                        if(memberTypeId.equals(upgradeMemberTypeId)){
                                            //循环升级方式
                                            if(StringUtils.isNotEmpty(memberLevelRuleMessageVo.getType())){
                                                for (String upgradeType : memberLevelRuleMessageVo.getType().split(",")) {
                                                    //升级方式
                                                    if(upgradeType.equals(MemberLevelRuleTypeEnum.AMOUNT.getStatus())){
                                                        //消费额升级
                                                        handleAmountUpgrade(memberLevelRuleMessageVo,miniAccount,amount);
                                                    }
                                                    if(upgradeType.equals(MemberLevelRuleTypeEnum.INTEGRAL.getStatus())){
                                                        //积分升级
                                                        handleIntegralUpgrade(memberLevelRuleMessageVo,miniAccount);
                                                    }
                                                    if(upgradeType.equals(MemberLevelRuleTypeEnum.MEMBER_AMOUNT.getStatus())){
                                                        //商品消费额升级
                                                        handleMemberAmountUpgrade(memberLevelRuleMessageVo,miniAccount,amount);
                                                    }
                                                    if(upgradeType.equals(MemberLevelRuleTypeEnum.TEAM_PERFORMANCE.getStatus())){
                                                        //团队业绩升级
                                                        handleTeamPerformance(memberLevelRuleMessageVo,miniAccount);
                                                    }
                                                }
                                            }else{
                                                log.info("会员类型{}对应升级方式不存在",memberLevelRuleMessage.getMemberTypeId());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }else if(message.getMessageType().equals(UpgradeTypeEnum.MEMBERUP.getType())||
                 message.getMessageType().equals(UpgradeTypeEnum.MEMBERREGISTER.getType())){
            log.info("会员升级消息类型为会员升级或会员注册");
            handleMemberUpOrRegister(message);
        }
    }

    /**
     * 处理积分升级
     * @param memberLevelRuleMessage
     * @param miniAccount
     */
    private void handleIntegralUpgrade(MemberLevelRuleMessageVo memberLevelRuleMessage, MiniAccount miniAccount) {

        BigDecimal integral = miniAccount.getIntegral();

        List<MemberLevelRuleVo> rules = memberLevelRuleMessage.getRules();
        //判断会员类型
        MemberType memberType = memberTypeService.getById(memberLevelRuleMessage.getMemberTypeId());

        if(rules!=null&&rules.size()>0){
            for (MemberLevelRuleVo upgradeMemberLevelRule : rules) {
                //订单升级会员等级id
                Long upgradeMemberLeverId = upgradeMemberLevelRule.getMemberLevelId();
                log.info("订单升级会员等级{}对应会员规则信息，upgradeMemberLevelRule: {}", upgradeMemberLeverId,JSONObject.toJSONString(upgradeMemberLevelRule));
                if(upgradeMemberLevelRule!=null){
                    //判断是否存在前置最低会员等级
                    //是否满足升级
                    Boolean upgradeFlag = false;
                    //判断是否存在前置最低会员等级
                    if(StringUtils.isNotEmpty(upgradeMemberLevelRule.getPreLowMemberLevelId())){
                        upgradeFlag = checkUpgradePreLow(upgradeMemberLevelRule, miniAccount);
                    }else{
                        upgradeFlag = true;
                    }
                    if(upgradeFlag){
                        if(integral.compareTo(upgradeMemberLevelRule.getIntegralStart())>=0){
                            MemberLevelRelationParam memberLevelRelationParam = new MemberLevelRelationParam();
                            memberLevelRelationParam.setUserId(miniAccount.getUserId());
                            memberLevelRelationParam.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                            List<MemberLevelRelation> upgradeMemberLevelRelationList = memberLevelRelationService.getMemberLevelRelation(memberLevelRelationParam);
                            if(upgradeMemberLevelRelationList!=null&&upgradeMemberLevelRelationList.size()>0){
                                MemberLevelRelation upgradeMemberLevelRelation = upgradeMemberLevelRelationList.get(0);
                                MemberLevelRuleVo memberLevelRule =
                                        memberLevelRuleService.getByMemberLevelId(Long.valueOf(upgradeMemberLevelRelation.getMemberLevelId()));
                                if(memberLevelRule!=null&&upgradeMemberLevelRule.getSort()>memberLevelRule.getSort()){
                                    if(memberType.getRegionFlag() == RegionFlagEnum.NO.getStatus()){
                                        //非区域类型直接升级并且发送升级会员等级消息
                                        if(StringUtils.isNotEmpty(upgradeMemberLevelRelation.getMemberLevelId())){
                                            MemberLevel oldMemberLevel = memberLevelService.getById(upgradeMemberLevelRelation.getMemberLevelId());
                                            MemberLevel upgradeMemberLevel = memberLevelService.getById(upgradeMemberLeverId);
                                            if(oldMemberLevel.getMemberFlag() == MemberFlagEnum.NO.getStatus()&&
                                                    upgradeMemberLevel.getMemberFlag() == MemberFlagEnum.YES.getStatus()){
                                                upgradeMemberLevelRelation.setUpLevelTime(LocalDateTime.now());
                                            }
                                        }


                                        upgradeMemberLevelRelation.setMemberLevelId(upgradeMemberLeverId+"");
                                        log.info("更新会员等级关系，memberLevelRelation: {}", JSONObject.toJSONString(upgradeMemberLevelRelation));
                                        memberLevelRelationService.updateById(upgradeMemberLevelRelation);

                                        UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                                        upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                                        upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                                        upgradeMemberLevelMessage.setUserId(Long.valueOf(miniAccount.getUserId()));
                                        //发送升级会员等级消息
                                        sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                                        // 赠送抽奖机会
                                        PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                                        BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                                        prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                                        sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                                    }else{
                                        //区域类型需要审核
                                        upgradeMemberLevelRelation.setApplyMemberLevelId(upgradeMemberLeverId+"");
                                        memberLevelRelationService.updateById(upgradeMemberLevelRelation);
                                    }
                                    break;
                                }
                            }else{
                                MemberLevelRelation newMemberLevelRelation = new MemberLevelRelation();
                                newMemberLevelRelation.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                                newMemberLevelRelation.setUserId(miniAccount.getUserId());
                                if(memberType.getRegionFlag() == RegionFlagEnum.NO.getStatus()){
                                    //非区域类型直接升级并且发送升级会员等级消息
                                    newMemberLevelRelation.setMemberLevelId(upgradeMemberLeverId+"");
                                    newMemberLevelRelation.setUpLevelTime(LocalDateTime.now());

                                    log.info("新增会员等级关系，upgradeMemberLevelRelation: {}", JSONObject.toJSONString(newMemberLevelRelation));
                                    memberLevelRelationService.save(newMemberLevelRelation);

                                    UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                                    upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                                    upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                                    upgradeMemberLevelMessage.setUserId(Long.valueOf(miniAccount.getUserId()));
                                    //发送升级会员等级消息
                                    sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                                    // 赠送抽奖机会
                                    PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                                    BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                                    prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                                    sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                                }else{
                                    //区域类型需要审核
                                    newMemberLevelRelation.setApplyMemberLevelId(upgradeMemberLeverId+"");
                                    log.info("新增会员等级关系，upgradeMemberLevelRelation: {}", JSONObject.toJSONString(newMemberLevelRelation));
                                    memberLevelRelationService.save(newMemberLevelRelation);
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }



    }

    //处理会员升级消息类型为会员注册，会员升级消息
    private void handleMemberUpOrRegister(UpgradeMemberLevelMessage message) {

        Long userId = message.getUserId();

        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(userId+"");
        log.info("用户扩展信息，miniAccountExtends: {}", JSONObject.toJSONString(miniAccountExtends));

        MiniAccount miniAccount = miniAccountService.getByShopUserId(miniAccountExtends.getShopUserId());
        log.info("用户信息，miniAccount: {}", JSONObject.toJSONString(miniAccount));

        //获取会员方式包含直推会员的会员类型
        List<MemberLevelRuleMessage> list = memberLevelRuleMessageService.list();
        List<Long>memberTypeIds = new ArrayList<>();
        if(list!=null&&list.size()>0){
            for (MemberLevelRuleMessage memberLevelRuleMessage : list) {
                if(StringUtils.isNotEmpty(memberLevelRuleMessage.getType())&&
                        memberLevelRuleMessage.getType().contains(MemberLevelRuleTypeEnum.DIRECT_PUSH_MEMBER.getStatus())){
                    memberTypeIds.add(memberLevelRuleMessage.getMemberTypeId());
                }
            }
        }

        if(memberTypeIds!=null&&memberTypeIds.size()>0){
            LambdaQueryWrapper<MemberType>wrapper = new LambdaQueryWrapper<>();
            //启用
            wrapper.eq(MemberType::getStatus, MemberTypeStatusEnum.YES.getStatus());
            wrapper.in(MemberType::getId,memberTypeIds);
            List<MemberType> memberTypeList = memberTypeService.list(wrapper);

            String parentId = miniAccount.getParentId();

            //判断上级用户id是否存在
            if(StringUtils.isNotEmpty(parentId)){

                MiniAccountExtends parentMiniAccountExtends = miniAccountExtendsService.findByUserId(parentId+"");
                log.info("用户扩展信息，parentMiniAccountExtends: {}", JSONObject.toJSONString(parentMiniAccountExtends));

                MiniAccount parentMiniAccount = miniAccountService.getByShopUserId(parentMiniAccountExtends.getShopUserId());
                log.info("用户信息，parentMiniAccount: {}", JSONObject.toJSONString(parentMiniAccount));

                if(memberTypeList!=null&&memberTypeList.size()>0){
                    for (MemberType memberType : memberTypeList) {

                        MemberLevelRuleMessageParam memberLevelRuleMessageParam = new MemberLevelRuleMessageParam();
                        memberLevelRuleMessageParam.setMemberTypeId(Long.valueOf(memberType.getId()));
                        MemberLevelRuleMessageVo memberLevelRuleMessage = memberLevelRuleMessageService.getMemberLevelRuleMessage(memberLevelRuleMessageParam);
                        if(memberLevelRuleMessage == null || memberLevelRuleMessage.equals("")){
                            log.info("会员类型{}对应会员规则信息不存在",Long.valueOf(memberType.getId()));
                            return;
                        }
                        List<MemberLevelRuleVo> rules = memberLevelRuleMessage.getRules();
                        if(rules!=null&&rules.size()>0){
                            for (MemberLevelRuleVo upgradeMemberLevelRule : rules) {
                                //订单升级会员等级id
                                Long upgradeMemberLeverId = upgradeMemberLevelRule.getMemberLevelId();
                                log.info("升级会员等级{}对应会员规则信息，upgradeMemberLevelRule: {}", upgradeMemberLeverId,JSONObject.toJSONString(upgradeMemberLevelRule));
                                if(upgradeMemberLevelRule!=null){
                                    //是否满足升级
                                    Boolean upgradeFlag = false;
                                    //判断是否存在前置最低会员等级
                                    if(StringUtils.isNotEmpty(upgradeMemberLevelRule.getPreLowMemberLevelId())){
                                        upgradeFlag = checkUpgradePreLow(upgradeMemberLevelRule, parentMiniAccount);
                                    }else{
                                        upgradeFlag = true;
                                    }
                                    if(upgradeFlag){
                                        //直推会员数
                                        Integer directMemberQty = upgradeMemberLevelRule.getDirectMemberQty();
                                        //最低会员等级ID
                                        String directLowMemberLevelId = upgradeMemberLevelRule.getDirectLowMemberLevelId();
                                        //直推会员关系
                                        String directConditionRelation = upgradeMemberLevelRule.getDirectConditionRelation();
                                        //直推会员数2
                                        Integer directMemberQty2 = upgradeMemberLevelRule.getDirectMemberQty2();
                                        //最低会员等级ID
                                        String directLowMemberLevelId2 = upgradeMemberLevelRule.getDirectLowMemberLevelId2();

                                        Boolean success = false;

                                        if(directMemberQty!=null&&directMemberQty>0){
                                            Integer userDirectMemberQty = 0;
                                            List<String>memberLevelIds = new ArrayList<>();
                                            if(StringUtils.isNotEmpty(directLowMemberLevelId)){
                                                //直推最低会员等级对应的规则
                                                MemberLevelRuleVo directLowMemberLevelRuleVo = memberLevelRuleService.getByMemberLevelId(Long.valueOf(directLowMemberLevelId));
                                                if(directLowMemberLevelRuleVo!=null){
                                                    Integer sort = directLowMemberLevelRuleVo.getSort();
                                                    String mainId = directLowMemberLevelRuleVo.getMainId();
                                                    memberLevelIds = memberLevelRuleService.getMemberLevelIds(sort,mainId);
                                                }
                                            }
                                            userDirectMemberQty = miniAccountExtendsService.getDirectMemberQty(Long.valueOf(parentId),memberLevelIds);
                                            if(StringUtils.isNotEmpty(directConditionRelation)&&directMemberQty2!=null&&directMemberQty2>0){
                                                Integer userDirectMemberQty2 = 0;
                                                List<String>memberLevelIds2 = new ArrayList<>();
                                                if(StringUtils.isNotEmpty(directLowMemberLevelId2)){
                                                    //直推最低会员等级对应的规则
                                                    MemberLevelRuleVo directLowMemberLevelRuleVo2 = memberLevelRuleService.getByMemberLevelId(Long.valueOf(directLowMemberLevelId2));
                                                    if(directLowMemberLevelRuleVo2!=null){
                                                        Integer sort = directLowMemberLevelRuleVo2.getSort();
                                                        String mainId = directLowMemberLevelRuleVo2.getMainId();
                                                        memberLevelIds2 = memberLevelRuleService.getMemberLevelIds(sort,mainId);
                                                    }
                                                }
                                                userDirectMemberQty2 = miniAccountExtendsService.getDirectMemberQty(Long.valueOf(parentId),memberLevelIds2);
                                                //且
                                                if(directConditionRelation.equals(DirectConditionRelationEnum.AND.getType())){
                                                    if(userDirectMemberQty>=directMemberQty&&
                                                            userDirectMemberQty2>=directMemberQty2){
                                                        success = true;
                                                    }
                                                }
                                                //或
                                                if(directConditionRelation.equals(DirectConditionRelationEnum.OR.getType())){
                                                    if(userDirectMemberQty>=directMemberQty||
                                                            userDirectMemberQty2>=directMemberQty2){
                                                        success = true;
                                                    }
                                                }
                                            }else{
                                                if(userDirectMemberQty>=directMemberQty){
                                                    success = true;
                                                }
                                            }
                                        }
                                        if(success){
                                            MemberLevelRelationParam memberLevelRelationParam = new MemberLevelRelationParam();
                                            memberLevelRelationParam.setUserId(parentMiniAccount.getUserId());
                                            memberLevelRelationParam.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                                            List<MemberLevelRelation> upgradeMemberLevelRelationList = memberLevelRelationService.getMemberLevelRelation(memberLevelRelationParam);

                                            if(upgradeMemberLevelRelationList!=null&&upgradeMemberLevelRelationList.size()>0){
                                                MemberLevelRelation upgradeMemberLevelRelation = upgradeMemberLevelRelationList.get(0);
                                                MemberLevelRuleVo memberLevelRule =
                                                        memberLevelRuleService.getByMemberLevelId(Long.valueOf(upgradeMemberLevelRelation.getMemberLevelId()));

                                                if(memberLevelRule!=null&&upgradeMemberLevelRule.getSort()>memberLevelRule.getSort()){


                                                    if(memberType.getRegionFlag() == RegionFlagEnum.NO.getStatus()){
                                                        //非区域类型直接升级并且发送升级会员等级消息

                                                        if(StringUtils.isNotEmpty(upgradeMemberLevelRelation.getMemberLevelId())){
                                                            MemberLevel oldMemberLevel = memberLevelService.getById(upgradeMemberLevelRelation.getMemberLevelId());
                                                            MemberLevel upgradeMemberLevel = memberLevelService.getById(upgradeMemberLeverId);
                                                            if(oldMemberLevel.getMemberFlag() == MemberFlagEnum.NO.getStatus()&&
                                                                    upgradeMemberLevel.getMemberFlag() == MemberFlagEnum.YES.getStatus()){
                                                                upgradeMemberLevelRelation.setUpLevelTime(LocalDateTime.now());
                                                            }
                                                        }


                                                        upgradeMemberLevelRelation.setMemberLevelId(upgradeMemberLeverId+"");
                                                        log.info("更新会员等级关系，memberLevelRelation: {}", JSONObject.toJSONString(upgradeMemberLevelRelation));
                                                        memberLevelRelationService.updateById(upgradeMemberLevelRelation);

                                                        UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                                                        upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                                                        upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                                                        upgradeMemberLevelMessage.setUserId(Long.valueOf(parentMiniAccount.getUserId()));
                                                        //发送升级会员等级消息
                                                        sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                                                        // 赠送抽奖机会
                                                        PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                                                        BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                                                        prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                                                        sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                                                    }else{
                                                        //区域类型需要审核
                                                        upgradeMemberLevelRelation.setApplyMemberLevelId(upgradeMemberLeverId+"");
                                                        memberLevelRelationService.updateById(upgradeMemberLevelRelation);
                                                    }
                                                    break;
                                                }
                                            }else{
                                                MemberLevelRelation newMemberLevelRelation = new MemberLevelRelation();
                                                newMemberLevelRelation.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                                                newMemberLevelRelation.setUserId(parentMiniAccount.getUserId());
                                                if(memberType.getRegionFlag() == RegionFlagEnum.NO.getStatus()){
                                                    //非区域类型直接升级并且发送升级会员等级消息
                                                    newMemberLevelRelation.setMemberLevelId(upgradeMemberLeverId+"");
                                                    newMemberLevelRelation.setUpLevelTime(LocalDateTime.now());
                                                    log.info("新增会员等级关系，upgradeMemberLevelRelation: {}", JSONObject.toJSONString(newMemberLevelRelation));
                                                    memberLevelRelationService.save(newMemberLevelRelation);

                                                    UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                                                    upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                                                    upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                                                    upgradeMemberLevelMessage.setUserId(Long.valueOf(parentMiniAccount.getUserId()));
                                                    //发送升级会员等级消息
                                                    sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                                                    // 赠送抽奖机会
                                                    PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                                                    BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                                                    prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                                                    sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                                                }else{
                                                    //区域类型需要审核
                                                    newMemberLevelRelation.setApplyMemberLevelId(upgradeMemberLeverId+"");
                                                    log.info("新增会员等级关系，upgradeMemberLevelRelation: {}", JSONObject.toJSONString(newMemberLevelRelation));
                                                    memberLevelRelationService.save(newMemberLevelRelation);
                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    /**
     * 验证前置最低会员等级
     * @param upgradeMemberLevelRule
     * @param miniAccount
     * @return
     */
    public Boolean checkUpgradePreLow(MemberLevelRuleVo upgradeMemberLevelRule,MiniAccount miniAccount){
        Boolean result = false;
        //获取前置最低会员等级对应规则
        MemberLevelRuleVo preLowMemberLevelRule =
                memberLevelRuleService.getByMemberLevelId(Long.valueOf(upgradeMemberLevelRule.getPreLowMemberLevelId()));
        log.info("前置最低会员等级{}对应会员规则信息，preLowMemberLevelRule: {}", upgradeMemberLevelRule.getPreLowMemberLevelId(),JSONObject.toJSONString(preLowMemberLevelRule));
        //获取前置最低会员等级信息
        MemberLevel preLowMemberLevel = memberLevelService.getById(upgradeMemberLevelRule.getPreLowMemberLevelId());
        log.info("前置最低会员等级{}对应会员等级信息，preLowMemberLevel: {}", upgradeMemberLevelRule.getPreLowMemberLevelId(),JSONObject.toJSONString(preLowMemberLevel));
        MemberLevelRelationParam memberLevelRelationParam = new MemberLevelRelationParam();
        memberLevelRelationParam.setUserId(miniAccount.getUserId());
        memberLevelRelationParam.setMemberTypeId(preLowMemberLevel.getMemberTypeId());
        List<MemberLevelRelation> userMemberLevelRelationList = memberLevelRelationService.getMemberLevelRelation(memberLevelRelationParam);
        if(userMemberLevelRelationList!=null&&userMemberLevelRelationList.size()>0){
            MemberLevelRelation userMemberLevelRelation = userMemberLevelRelationList.get(0);
            MemberLevelRuleVo userMemberLevelRule = memberLevelRuleService.getByMemberLevelId(Long.valueOf(userMemberLevelRelation.getMemberLevelId()));
            log.info("用户最低会员等级会员等级{}对应会员规则信息，userMemberLevelRule: {}", upgradeMemberLevelRule.getPreLowMemberLevelId(),JSONObject.toJSONString(userMemberLevelRule));
            if(userMemberLevelRule!=null){
                if(userMemberLevelRule.getSort()>=preLowMemberLevelRule.getSort()){
                    log.info("会员类型{}下，用户会员等级大于当前规则前置最低会员等级");
                    result =true;
                }else{
                    log.info("会员类型{}下，用户会员等级小于当前规则前置最低会员等级");
                    result = false;
                }
            }else{
                log.info("会员等级{}对应的会员等级升级规则不存在",userMemberLevelRule.getMemberLevelId());
                result = false;
            }
        }else{
            log.info("会员类型{}下，用户会员等级小于当前规则前置最低会员等级");
            result = false;
        }
        return result;
    }

    @Override
    public List<RewardSchemeDetVo> getRewardSchemeDetVo(OrderVo orderVo, OrderItemVo orderItemVo,
                                                        Integer commissionAmountSource,Long orderMemberTypeId,Long regionMemberTypeId,Integer regionFlag) {

        String shopId = orderItemVo.getShopId();

        //根据订单明细会员类型，商家id获取奖励方案明细
        String completeTime = LocalDateTimeUtils.formatTime(orderVo.getCompleteTime(), "yyyy-MM-dd HH:mm:ss");

        //1.根据店铺id，订单完成时间，会员类型id，分佣来源获取奖励方案规则
        RewardSchemeDetParam param = new RewardSchemeDetParam();
        param.setCompleteTime(completeTime);
        param.setShopId(shopId);
        param.setOrderMemberTypeId(orderMemberTypeId);
        param.setRegionMemberTypeId(regionMemberTypeId);
        param.setRegionFlag(regionFlag);

        if(commissionAmountSource!=null){
            param.setCommissionAmountSource(commissionAmountSource);
        }
        List<RewardSchemeDetVo> rewardSchemeDetList = remoteGoodsService.getRewardSchemeDetVo(param);
        log.info("根据订单会员类型id{}、商家id{}查出分佣金额来源为订单实际支付金额{}的奖励方案明细记录rewardSchemeDetList: {}",
                orderMemberTypeId,shopId,commissionAmountSource,JSONObject.toJSONString(rewardSchemeDetList));

        List<RewardSchemeDetVo>newList = new ArrayList<>();
        if(rewardSchemeDetList!=null&&rewardSchemeDetList.size()>0){
            for (RewardSchemeDetVo rewardSchemeDetVo : rewardSchemeDetList) {
                //购买类型判断
                Boolean buyTypeFlag = false;
                if(rewardSchemeDetVo.getBuyType() == null ){
                    buyTypeFlag = true;
                }else{
                    if(rewardSchemeDetVo.getBuyType() == orderItemVo.getBuyType()){
                        buyTypeFlag = true;
                    }
                }
                //分佣商品判断
                Boolean productFlag = false;
                //非分佣商品
                List<RewardSchemeDetNonProductVo> rewardSchemeDetNonProductList = rewardSchemeDetVo.getRewardSchemeDetNonProductList();
                //分佣商品
                List<RewardSchemeDetProductVo> rewardSchemeDetProductList = rewardSchemeDetVo.getRewardSchemeDetProductList();
                if(rewardSchemeDetNonProductList.size()>0&&rewardSchemeDetProductList.size()>0){
                    //分佣商品，非分佣商品不为空
                    List<RewardSchemeDetProductVo> dataList = rewardSchemeDetProductList.stream().filter(e -> e.getProductId().equals(orderItemVo.getProductId()) && e.getSkuId().equals(orderItemVo.getProductSkuId()))
                            .collect(Collectors.toList());
                    List<RewardSchemeDetNonProductVo> dataNoList = rewardSchemeDetNonProductList.stream().filter(e -> e.getProductId().equals(orderItemVo.getProductId()) && e.getSkuId().equals(orderItemVo.getProductSkuId()))
                            .collect(Collectors.toList());
                    if(dataList.size()>0&&dataNoList.size() == 0){
                        productFlag = true;
                    }
                }else if(rewardSchemeDetNonProductList.size()>0&&rewardSchemeDetProductList.size() == 0){
                    //分佣商品为空，非分佣商品不为空
                    List<RewardSchemeDetNonProductVo> dataNoList = rewardSchemeDetNonProductList.stream().filter(e -> e.getProductId().equals(orderItemVo.getProductId()) && e.getSkuId().equals(orderItemVo.getProductSkuId()))
                            .collect(Collectors.toList());
                    if(dataNoList.size() == 0){
                        productFlag = true;
                    }
                }else if(rewardSchemeDetNonProductList.size() == 0&&rewardSchemeDetProductList.size() > 0){
                    //分佣商品不为空，非分佣商品为空
                    List<RewardSchemeDetProductVo> dataList = rewardSchemeDetProductList.stream().filter(e -> e.getProductId().equals(orderItemVo.getProductId()))
                            .collect(Collectors.toList());
                    if(dataList.size()>0){
                        productFlag = true;
                    }
                }else{
                    //分佣商品，非分佣商品都为空
                    productFlag = true;
                }

                //条件升级判断
                Boolean upMemberLevelFlag = false;
                if(StringUtils.isNotEmpty(rewardSchemeDetVo.getUpMemberLevelId())){
                    //条件升级等级包含订单的升级会员等级ID
                    if(orderVo.getUpgradeMemberLeverId()!=null
                            &&rewardSchemeDetVo.getUpMemberLevelId().indexOf(orderVo.getUpgradeMemberLeverId()+"")!=-1){
                        upMemberLevelFlag = true;
                    }
                }else{
                    upMemberLevelFlag = true;
                }
                log.info("判断是否满足分佣条件，购买类型判断：{}；分佣商品判断：{}；条件升级判断：{}",buyTypeFlag,productFlag,upMemberLevelFlag);
                if(buyTypeFlag&&productFlag&&upMemberLevelFlag){
                    newList.add(rewardSchemeDetVo);
                }
            }
        }
        return newList;
    }
    @Override
    public void handleRegionCommission(List<RewardSchemeDetVo> rewardSchemeDetList,BigDecimal money,String userId,
                                       Long orderId,List<MiniAccountRewardVo> miniAccountRewardList) {
        //1.分佣类型为佣金
        List<RewardSchemeDetVo> commissionList = rewardSchemeDetList.stream().
                filter(e -> e.getRewardType() == RewardTypeEnum.COMMISSION.getStatus()).collect(Collectors.toList());
        log.info("获取区域类型分佣类型为佣金的奖励方案明细规则commissionList：{}",JSONObject.toJSONString(commissionList));
        if(commissionList!=null&&commissionList.size()>0){
            for (RewardSchemeDetVo rewardSchemeDetVo : commissionList) {
                MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                BigDecimal amount = BigDecimal.ZERO;
                if(rewardSchemeDetVo.getOneCommissionAmount()!=null&&!rewardSchemeDetVo.getOneCommissionAmount().equals("")){
                    amount = new BigDecimal(rewardSchemeDetVo.getOneCommissionAmount());
                }
                if(rewardSchemeDetVo.getOneCommissionRate()!=null&&!rewardSchemeDetVo.getOneCommissionRate().equals("")){
                    //佣金金额 = 佣金比例* 订单明细金额
                    amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getOneCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                }
                miniAccountRewardVo.setUserId(userId);
                miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                miniAccountRewardVo.setAmount(amount);
                miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                miniAccountRewardVo.setOrderId(orderId+"");
                miniAccountRewardVo.setCommissionType(CommissionTypeEnum.COMMISSION.getType());
                miniAccountRewardList.add(miniAccountRewardVo);
            }

        }

    }
    @Override
    public void handleCommission(List<RewardSchemeDetVo> rewardSchemeDetList,String shopUserId,
                                 Long orderId,BigDecimal money,Long orderMemberTypeId,
                                 List<MiniAccountRewardVo> miniAccountRewardList) {
        //1.分佣类型为佣金
        List<RewardSchemeDetVo> commissionList = rewardSchemeDetList.stream().
                filter(e -> e.getRewardType() == RewardTypeEnum.COMMISSION.getStatus()).collect(Collectors.toList());
        log.info("获取分佣类型为佣金的奖励方案明细规则commissionList：{}",JSONObject.toJSONString(commissionList));
        if(commissionList!=null&&commissionList.size()>0){
            MiniAccount orderMiniAccount = miniAccountService.getByShopUserId(shopUserId);
            log.info("下单人会员，orderMiniAccount: {}", JSONObject.toJSONString(orderMiniAccount));
            MiniAccount C1 = null;
            if(StringUtils.isNotEmpty(orderMiniAccount.getParentId())){
                LambdaQueryWrapper<MiniAccount>parentWrapper = new LambdaQueryWrapper<>();
                parentWrapper.eq(MiniAccount::getUserId,orderMiniAccount.getParentId());
                C1 = miniAccountService.getOne(parentWrapper);
                log.info("上级会员信息，C1: {}", JSONObject.toJSONString(C1));
            }
            MiniAccount C2 = null;
            if(StringUtils.isNotEmpty(orderMiniAccount.getAboveParentId())){
                LambdaQueryWrapper<MiniAccount>aboveParentWrapper = new LambdaQueryWrapper<>();
                aboveParentWrapper.eq(MiniAccount::getUserId,orderMiniAccount.getAboveParentId());
                C2 = miniAccountService.getOne(aboveParentWrapper);
                log.info("上上级会员信息，C2: {}", JSONObject.toJSONString(C2));
            }
            MiniAccount C3 = orderMiniAccount;
            log.info("自身会员信息，C3: {}", JSONObject.toJSONString(C3));
            MiniAccount C4 = null;
            if(StringUtils.isNotEmpty(orderMiniAccount.getSaleUserId())){
                LambdaQueryWrapper<MiniAccount>saleWrapper = new LambdaQueryWrapper<>();
                saleWrapper.eq(MiniAccount::getUserId,orderMiniAccount.getSaleUserId());
                C4 = miniAccountService.getOne(saleWrapper);
                log.info("业务员会员信息，C4: {}", JSONObject.toJSONString(C4));
            }
            //上级会员
            if(C1!=null){
                //过滤业务员分佣为是记录
                List<RewardSchemeDetVo> saleYesCommissionList = commissionList.stream().filter(e -> e.getSalesmanFlag() != null && e.getSalesmanFlag() == SalesmanFlagEnum.YES.getStatus())
                        .collect(Collectors.toList());
                log.info("获取业务员分佣为是的奖励方案明细规则saleYesCommissionList：{}",JSONObject.toJSONString(saleYesCommissionList));
                if(saleYesCommissionList!=null&&saleYesCommissionList.size()>0){
                    if(C4!=null&&C4.getUserId().equals(C1.getUserId())){
                        if(saleYesCommissionList!=null&&saleYesCommissionList.size()>0){
                            for (RewardSchemeDetVo rewardSchemeDetVo : saleYesCommissionList) {
                                Long memberTypeId = rewardSchemeDetVo.getMemberTypeId();
                                String memberLevelId = rewardSchemeDetVo.getMemberLevelId();
                                MemberLevelRelationParam C1Param = new MemberLevelRelationParam();
                                C1Param.setMemberTypeId(memberTypeId);
                                C1Param.setUserId(C1.getUserId());
                                //查询上级会员当前会员类型对应会员等级id
                                String C1MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C1Param);
                                log.info("上级会员C1会员类型{},对应会员等级id：{}",memberTypeId,C1MemberLevelId);
                                if(StringUtils.isNotEmpty(C1MemberLevelId)){
                                    //过滤收益等级
                                    if(StringUtils.isEmpty(memberLevelId)||(StringUtils.isNotEmpty(memberLevelId)&&
                                            memberLevelId.indexOf(C1MemberLevelId) != -1)){
                                        //判断直推会员数
                                        Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C1.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                        if(b){
                                            BigDecimal amount = BigDecimal.ZERO;
                                            if(rewardSchemeDetVo.getOneCommissionAmount()!=null&&!rewardSchemeDetVo.getOneCommissionAmount().equals("")){
                                                amount = new BigDecimal(rewardSchemeDetVo.getOneCommissionAmount());
                                            }
                                            if(rewardSchemeDetVo.getOneCommissionRate()!=null&&!rewardSchemeDetVo.getOneCommissionRate().equals("")){
                                                //佣金金额 = 佣金比例* 订单明细金额
                                                amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getOneCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                            }
                                            if(amount.compareTo(BigDecimal.ZERO)>0){
                                                MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                                miniAccountRewardVo.setUserId(C1.getUserId());
                                                miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                                miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                                miniAccountRewardVo.setAmount(amount);
                                                miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                                miniAccountRewardVo.setOrderId(orderId+"");
                                                miniAccountRewardVo.setCommissionType(CommissionTypeEnum.COMMISSION.getType());
                                                miniAccountRewardList.add(miniAccountRewardVo);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                //过滤业务员分佣为否记录
                List<RewardSchemeDetVo> saleNoCommissionList = commissionList.stream().filter(e -> e.getSalesmanFlag() == null || (e.getSalesmanFlag() != null && e.getSalesmanFlag() == SalesmanFlagEnum.NO.getStatus()))
                        .collect(Collectors.toList());
                log.info("获取业务员分佣为否的奖励方案明细规则saleNoCommissionList：{}",JSONObject.toJSONString(saleNoCommissionList));
                if(saleNoCommissionList!=null&&saleNoCommissionList.size()>0){
                    for (RewardSchemeDetVo rewardSchemeDetVo : saleNoCommissionList) {
                        Long memberTypeId = rewardSchemeDetVo.getMemberTypeId();
                        String memberLevelId = rewardSchemeDetVo.getMemberLevelId();
                        MemberLevelRelationParam C1Param = new MemberLevelRelationParam();
                        C1Param.setMemberTypeId(memberTypeId);
                        C1Param.setUserId(C1.getUserId());
                        //查询上级会员当前会员类型对应会员等级id
                        String C1MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C1Param);
                        log.info("上级会员C1当前会员类型{},对应会员等级id：{}",memberTypeId,C1MemberLevelId);
                        //过滤会员收益等级
                        if(StringUtils.isNotEmpty(C1MemberLevelId)){
                            if(StringUtils.isEmpty(memberLevelId)||(StringUtils.isNotEmpty(memberLevelId)&&
                                    memberLevelId.indexOf(C1MemberLevelId) != -1)){
                                Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C1.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                if(b){
                                    BigDecimal amount = BigDecimal.ZERO;
                                    if(rewardSchemeDetVo.getOneCommissionAmount()!=null&&!rewardSchemeDetVo.getOneCommissionAmount().equals("")){
                                        amount = new BigDecimal(rewardSchemeDetVo.getOneCommissionAmount());
                                    }
                                    if(rewardSchemeDetVo.getOneCommissionRate()!=null&&!rewardSchemeDetVo.getOneCommissionRate().equals("")){
                                        //佣金金额 = 佣金比例* 订单明细金额

                                        amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getOneCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                    }
                                    if(amount.compareTo(BigDecimal.ZERO)>0){
                                        MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                        miniAccountRewardVo.setUserId(C1.getUserId());
                                        miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                        miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                        miniAccountRewardVo.setAmount(amount);
                                        miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                        miniAccountRewardVo.setOrderId(orderId+"");
                                        miniAccountRewardVo.setCommissionType(CommissionTypeEnum.COMMISSION.getType());
                                        miniAccountRewardList.add(miniAccountRewardVo);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            //上上级会员
            if(C2!=null){
                //过滤业务员分佣为是记录
                List<RewardSchemeDetVo> saleYesCommissionList = commissionList.stream().filter(e -> e.getSalesmanFlag() != null && e.getSalesmanFlag() == SalesmanFlagEnum.YES.getStatus())
                        .collect(Collectors.toList());
                log.info("获取业务员分佣为是的奖励方案明细规则saleYesCommissionList：{}",JSONObject.toJSONString(saleYesCommissionList));
                if(saleYesCommissionList!=null&&saleYesCommissionList.size()>0){
                    if(C4!=null&&C4.getUserId().equals(C2.getUserId())){
                        for (RewardSchemeDetVo rewardSchemeDetVo : saleYesCommissionList) {
                            Long memberTypeId = rewardSchemeDetVo.getMemberTypeId();
                            String memberLevelId = rewardSchemeDetVo.getMemberLevelId();
                            MemberLevelRelationParam C2Param = new MemberLevelRelationParam();
                            C2Param.setMemberTypeId(memberTypeId);
                            C2Param.setUserId(C2.getUserId());
                            //查询上上级会员当前会员类型对应会员等级id
                            String C2MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C2Param);
                            log.info("上上级会员C2当前会员类型{},对应会员等级id：{}",memberLevelId,C2MemberLevelId);
                            if(StringUtils.isNotEmpty(C2MemberLevelId)){
                                if(StringUtils.isEmpty(memberLevelId)||(StringUtils.isNotEmpty(memberLevelId)&&
                                        memberLevelId.indexOf(C2MemberLevelId) != -1)){
                                    Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C2.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                    if(b){
                                        BigDecimal amount = BigDecimal.ZERO;
                                        if(rewardSchemeDetVo.getTwoCommissionAmount()!=null&&!rewardSchemeDetVo.getTwoCommissionAmount().equals("")){
                                            amount = new BigDecimal(rewardSchemeDetVo.getTwoCommissionAmount());
                                        }
                                        if(rewardSchemeDetVo.getTwoCommissionRate()!=null&&!rewardSchemeDetVo.getTwoCommissionRate().equals("")){
                                            //佣金金额 = 佣金比例* 订单明细金额
                                            amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getTwoCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                        }
                                        if(amount.compareTo(BigDecimal.ZERO)>0){
                                            MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                            miniAccountRewardVo.setUserId(C2.getUserId());
                                            miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                            miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                            miniAccountRewardVo.setAmount(amount);
                                            miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                            miniAccountRewardVo.setOrderId(orderId+"");
                                            miniAccountRewardVo.setCommissionType(CommissionTypeEnum.COMMISSION.getType());
                                            miniAccountRewardList.add(miniAccountRewardVo);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                //过滤业务员分佣为否记录
                List<RewardSchemeDetVo> saleNoCommissionList = commissionList.stream().filter(e -> e.getSalesmanFlag() == null || (e.getSalesmanFlag() != null && e.getSalesmanFlag() == SalesmanFlagEnum.NO.getStatus()))
                        .collect(Collectors.toList());
                log.info("获取业务员分佣为否的奖励方案明细规则saleNoCommissionList：{}",JSONObject.toJSONString(saleNoCommissionList));
                if(saleNoCommissionList!=null&&saleNoCommissionList.size()>0){
                    for (RewardSchemeDetVo rewardSchemeDetVo : saleNoCommissionList) {
                        Long memberTypeId = rewardSchemeDetVo.getMemberTypeId();
                        String memberLevelId = rewardSchemeDetVo.getMemberLevelId();
                        MemberLevelRelationParam C2Param = new MemberLevelRelationParam();
                        C2Param.setMemberTypeId(memberTypeId);
                        C2Param.setUserId(C2.getUserId());
                        //查询上上级会员当前会员类型对应会员等级id
                        String C2MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C2Param);
                        log.info("上上级会员C2当前会员类型{},对应会员等级id：{}",memberLevelId,C2MemberLevelId);
                        if(StringUtils.isNotEmpty(C2MemberLevelId)){
                            if(StringUtils.isEmpty(memberLevelId)||(StringUtils.isNotEmpty(memberLevelId)&&
                                    memberLevelId.indexOf(C2MemberLevelId) != -1)){
                                Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C2.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                if(b){
                                    BigDecimal amount = BigDecimal.ZERO;
                                    if(rewardSchemeDetVo.getTwoCommissionAmount()!=null&&!rewardSchemeDetVo.getTwoCommissionAmount().equals("")){
                                        amount = new BigDecimal(rewardSchemeDetVo.getTwoCommissionAmount());
                                    }
                                    if(rewardSchemeDetVo.getTwoCommissionRate()!=null&&!rewardSchemeDetVo.getTwoCommissionRate().equals("")){
                                        //佣金金额 = 佣金比例* 订单明细金额
                                        amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getTwoCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                    }
                                    if(amount.compareTo(BigDecimal.ZERO)>0){
                                        MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                        miniAccountRewardVo.setUserId(C2.getUserId());
                                        miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                        miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                        miniAccountRewardVo.setAmount(amount);
                                        miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                        miniAccountRewardVo.setOrderId(orderId+"");
                                        miniAccountRewardVo.setCommissionType(CommissionTypeEnum.COMMISSION.getType());
                                        miniAccountRewardList.add(miniAccountRewardVo);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            //自身会员
            if(C3!=null){
                //过滤自身分佣为是记录
                List<RewardSchemeDetVo> selfYesCommissionList = commissionList.stream().filter(e -> e.getSelfCommission() != null && e.getSelfCommission() == SelfCommissionEnum.YES.getStatus())
                        .collect(Collectors.toList());
                log.info("获取自身分佣为是的奖励方案明细规则selfYesCommissionList：{}",JSONObject.toJSONString(selfYesCommissionList));
                if(selfYesCommissionList!=null&&selfYesCommissionList.size()>0){
                    for (RewardSchemeDetVo rewardSchemeDetVo : selfYesCommissionList) {
                        Long memberTypeId = rewardSchemeDetVo.getMemberTypeId();
                        String memberLevelId = rewardSchemeDetVo.getMemberLevelId();
                        MemberLevelRelationParam C3Param = new MemberLevelRelationParam();
                        C3Param.setMemberTypeId(memberTypeId);
                        C3Param.setUserId(C3.getUserId());
                        //查询自身会员当前会员类型对应会员等级id
                        String C3MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C3Param);
                        log.info("自身会员C3当前会员类型{},对应会员等级id：{}",memberTypeId,C3MemberLevelId);
                        if(StringUtils.isNotEmpty(C3MemberLevelId)){
                            if(StringUtils.isEmpty(memberLevelId) || (StringUtils.isNotEmpty(memberLevelId) &&
                                    memberLevelId.indexOf(C3MemberLevelId) != -1)){
                                Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C3.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                if(b){
                                    BigDecimal amount = BigDecimal.ZERO;
                                    if(rewardSchemeDetVo.getOneCommissionAmount()!=null&&!rewardSchemeDetVo.getOneCommissionAmount().equals("")){
                                        amount = new BigDecimal(rewardSchemeDetVo.getOneCommissionAmount());
                                    }
                                    if(rewardSchemeDetVo.getOneCommissionRate()!=null&&!rewardSchemeDetVo.getOneCommissionRate().equals("")){
                                        //佣金金额 = 佣金比例* 订单明细金额
                                        amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getOneCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                    }
                                    if(amount.compareTo(BigDecimal.ZERO)>0){
                                        MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                        miniAccountRewardVo.setUserId(C3.getUserId());
                                        miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                        miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                        miniAccountRewardVo.setAmount(amount);
                                        miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                        miniAccountRewardVo.setOrderId(orderId+"");
                                        miniAccountRewardVo.setCommissionType(CommissionTypeEnum.COMMISSION.getType());
                                        miniAccountRewardList.add(miniAccountRewardVo);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    @Override
    public void handleRegionSameLevel(List<RewardSchemeDetVo> rewardSchemeDetList,String userId,
                                      String orderUserId,Long memberTypeId,BigDecimal money,Long orderId,
                                      List<MiniAccountRewardVo> miniAccountRewardList) {
        //1.分佣类型为平级
        List<RewardSchemeDetVo> sameLevelList = rewardSchemeDetList.stream().
                filter(e -> e.getRewardType() == RewardTypeEnum.SAME_LEVEL.getStatus()).collect(Collectors.toList());
        log.info("获取区域类型分佣类型为平级的奖励方案明细规则saleLevelList：{}",JSONObject.toJSONString(sameLevelList));
        if(sameLevelList!=null&&sameLevelList.size()>0){
            //获取下单人区域会员等级
            MemberLevelRelationParam orderParam = new MemberLevelRelationParam();
            orderParam.setMemberTypeId(memberTypeId);
            orderParam.setUserId(orderUserId);
            String orderMemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(orderParam);
            //获取当前用户区域会员等级
            MemberLevelRelationParam userParam = new MemberLevelRelationParam();
            userParam.setMemberTypeId(memberTypeId);
            orderParam.setUserId(userId);
            String userMemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(userParam);
            if(StringUtils.isEmpty(orderMemberLevelId)||
                    (StringUtils.isNotEmpty(orderMemberLevelId)&&orderMemberLevelId.equals(userMemberLevelId))){
                if(sameLevelList!=null&&sameLevelList.size()>0){
                    for (RewardSchemeDetVo rewardSchemeDetVo : sameLevelList) {
                        MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                        BigDecimal amount = BigDecimal.ZERO;
                        if(rewardSchemeDetVo.getOneCommissionAmount()!=null&&!rewardSchemeDetVo.getOneCommissionAmount().equals("")){
                            amount = new BigDecimal(rewardSchemeDetVo.getOneCommissionAmount());
                        }
                        if(rewardSchemeDetVo.getOneCommissionRate()!=null&&!rewardSchemeDetVo.getOneCommissionRate().equals("")){
                            //佣金金额 = 佣金比例* 订单明细金额
                            amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getOneCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                        }
                        miniAccountRewardVo.setUserId(userId);
                        miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                        miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                        miniAccountRewardVo.setAmount(amount);
                        miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                        miniAccountRewardVo.setOrderId(orderId+"");
                        miniAccountRewardVo.setCommissionType(CommissionTypeEnum.COMMISSION.getType());
                        miniAccountRewardList.add(miniAccountRewardVo);
                    }
                }
            }
        }

    }
    @Override
    public void handleSameLevel(List<RewardSchemeDetVo> rewardSchemeDetList,String shopUserId,Long orderId,
                                BigDecimal money,Long orderMemberTypeId,
                                List<MiniAccountRewardVo> miniAccountRewardList) {
        //1.分佣类型为平级
        List<RewardSchemeDetVo> sameLevelList = rewardSchemeDetList.stream().
                filter(e -> e.getRewardType() == RewardTypeEnum.SAME_LEVEL.getStatus()).collect(Collectors.toList());
        log.info("获取分佣类型为平级的奖励方案明细规则saleLevelList：{}",JSONObject.toJSONString(sameLevelList));
        if(sameLevelList!=null&&sameLevelList.size()>0){
            MiniAccount orderMiniAccount = miniAccountService.getByShopUserId(shopUserId);
            log.info("下单人会员，orderMiniAccount: {}", JSONObject.toJSONString(orderMiniAccount));
            MiniAccount C1 = null;
            if(StringUtils.isNotEmpty(orderMiniAccount.getParentId())){
                LambdaQueryWrapper<MiniAccount>parentWrapper = new LambdaQueryWrapper<>();
                parentWrapper.eq(MiniAccount::getUserId,orderMiniAccount.getParentId());
                C1 = miniAccountService.getOne(parentWrapper);
                log.info("上级会员信息，C1: {}", JSONObject.toJSONString(C1));
            }
            MiniAccount C2 = null;
            if(StringUtils.isNotEmpty(orderMiniAccount.getAboveParentId())){
                LambdaQueryWrapper<MiniAccount>aboveParentWrapper = new LambdaQueryWrapper<>();
                aboveParentWrapper.eq(MiniAccount::getUserId,orderMiniAccount.getAboveParentId());
                C2 = miniAccountService.getOne(aboveParentWrapper);
                log.info("上上级会员信息，C2: {}", JSONObject.toJSONString(C2));
            }
            if(sameLevelList!=null&&sameLevelList.size()>0){
                for (RewardSchemeDetVo rewardSchemeDetVo : sameLevelList) {
                    Long memberTypeId = rewardSchemeDetVo.getMemberTypeId();
                    String memberLevelId = rewardSchemeDetVo.getMemberLevelId();
                    //下单人
                    MemberLevelRelationParam orderParam = new MemberLevelRelationParam();
                    orderParam.setMemberTypeId(memberTypeId);
                    orderParam.setUserId(orderMiniAccount.getUserId());
                    //查询下单人会员当前会员类型对应会员等级id
                    String orderMemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(orderParam);
                    log.info("下单人会员C1当前会员类型{},对应会员等级id：{}",memberTypeId,orderMemberLevelId);

                    if(C1!=null){
                        //上级会员
                        MemberLevelRelationParam C1Param = new MemberLevelRelationParam();
                        C1Param.setMemberTypeId(memberTypeId);
                        C1Param.setUserId(C1.getUserId());
                        //查询上级会员当前会员类型对应会员等级id
                        String C1MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C1Param);
                        log.info("上级会员C1当前会员类型{}.对应会员等级id：{}",memberTypeId,C1MemberLevelId);
                        if(StringUtils.isNotEmpty(C1MemberLevelId)&&StringUtils.isNotEmpty(orderMemberLevelId)&&C1MemberLevelId.equals(orderMemberLevelId)){
                            //过滤收益等级
                            if(StringUtils.isEmpty(memberLevelId) || (StringUtils.isNotEmpty(memberLevelId) &&
                                    memberLevelId.indexOf(C1MemberLevelId) != -1)){
                                Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C1.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                if(b){
                                    BigDecimal amount = BigDecimal.ZERO;
                                    if(rewardSchemeDetVo.getOneCommissionAmount()!=null&&!rewardSchemeDetVo.getOneCommissionAmount().equals("")){
                                        amount = new BigDecimal(rewardSchemeDetVo.getOneCommissionAmount());
                                    }
                                    if(rewardSchemeDetVo.getOneCommissionRate()!=null&&!rewardSchemeDetVo.getOneCommissionRate().equals("")){
                                        //佣金金额 = 佣金比例* 订单明细金额
                                        amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getOneCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                    }
                                    if(amount.compareTo(BigDecimal.ZERO)>0){
                                        MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                        miniAccountRewardVo.setUserId(C1.getUserId());
                                        miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                        miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                        miniAccountRewardVo.setAmount(amount);
                                        miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                        miniAccountRewardVo.setOrderId(orderId+"");
                                        miniAccountRewardVo.setCommissionType(CommissionTypeEnum.SAME_LEVEL.getType());
                                        miniAccountRewardList.add(miniAccountRewardVo);
                                    }
                                }
                            }
                        }
                    }
                    if(C2!=null){
                        //上上级会员
                        MemberLevelRelationParam C2Param = new MemberLevelRelationParam();
                        C2Param.setMemberTypeId(memberTypeId);
                        C2Param.setUserId(C2.getUserId());
                        //查询上上级会员当前会员类型对应会员等级id
                        String C2MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C2Param);
                        log.info("上上级会员C1当前会员类型{},对应会员等级id：{}",memberTypeId,C2MemberLevelId);
                        if(StringUtils.isNotEmpty(C2MemberLevelId)&&StringUtils.isNotEmpty(orderMemberLevelId)&&C2MemberLevelId.equals(orderMemberLevelId)){
                            //过滤收益等级
                            if(StringUtils.isEmpty(memberLevelId) || (StringUtils.isNotEmpty(memberLevelId) &&
                                    memberLevelId.indexOf(C2MemberLevelId) != -1)){
                                Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C2.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                if(b){
                                    BigDecimal amount = BigDecimal.ZERO;
                                    if(rewardSchemeDetVo.getTwoCommissionAmount()!=null&&!rewardSchemeDetVo.getTwoCommissionAmount().equals("")){
                                        amount = new BigDecimal(rewardSchemeDetVo.getTwoCommissionAmount());
                                    }
                                    if(rewardSchemeDetVo.getTwoCommissionRate()!=null&&!rewardSchemeDetVo.getTwoCommissionRate().equals("")){
                                        //佣金金额 = 佣金比例* 订单明细金额
                                        amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getTwoCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                    }
                                    if(amount.compareTo(BigDecimal.ZERO)>0){
                                        MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                        miniAccountRewardVo.setUserId(C2.getUserId());
                                        miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                        miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                        miniAccountRewardVo.setAmount(amount);
                                        miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                        miniAccountRewardVo.setOrderId(orderId+"");
                                        miniAccountRewardVo.setCommissionType(CommissionTypeEnum.SAME_LEVEL.getType());
                                        miniAccountRewardList.add(miniAccountRewardVo);
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }
    }

    @Override
    public void handleRegionCycleCommission(List<RewardSchemeDetVo> rewardSchemeDetList,MemberLevelRelation memberLevelRelation,
                                            BigDecimal money,Long orderId,List<MiniAccountRewardVo> miniAccountRewardList) {
        //1.分佣类型为循环分佣
        List<RewardSchemeDetVo> cycleCommissionList = rewardSchemeDetList.stream().
                filter(e -> e.getRewardType() == RewardTypeEnum.CYCLE_COMMISSION.getStatus()).collect(Collectors.toList());
        log.info("获取区域类型分佣类型为循环分佣的奖励方案明细规则cycleCommissionList：{}",JSONObject.toJSONString(cycleCommissionList));
        if(cycleCommissionList!=null&&cycleCommissionList.size()>0){
            //查询出E会员在成为对应的区域等级时间后，对应的区域编码的订单数量为D1
            String agentRegionCode = memberLevelRelation.getAgentRegionCode();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String upLevelTime = memberLevelRelation.getUpLevelTime().format(formatter);
            Integer regionType = memberLevelRelation.getRegionType();
            Integer D1 = remoteOrderService.getOrderCountByRegionCode(agentRegionCode,upLevelTime,regionType);
            for (RewardSchemeDetVo rewardSchemeDetVo : cycleCommissionList) {
                if(rewardSchemeDetVo.getOneCommissionAmount()!=null&&!rewardSchemeDetVo.getOneCommissionAmount().equals("")){
                    //将一级分销固定金额值按照英文逗号分割成数组E1,
                    // 将D1对E1的长度取余得到值为F1，则E1[F1]的值为佣金计算的基值，
                    // 即是固定金额算佣金的话，该基值就是佣金
                    String[] E1 = rewardSchemeDetVo.getOneCommissionAmount().split(",");
                    log.info("将一级分销固定金额值按照英文逗号分割成数组E1：{}",JSONObject.toJSONString(E1));
                    int F1 = D1%E1.length;
                    log.info("将D1{}对E1的长度{}取余得到值为F1{}",D1,E1.length,F1);
                    String result = E1[F1];
                    BigDecimal amount = new BigDecimal(result);
                    MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                    miniAccountRewardVo.setUserId(memberLevelRelation.getUserId());
                    miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                    miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                    miniAccountRewardVo.setAmount(amount);
                    miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                    miniAccountRewardVo.setOrderId(orderId+"");
                    miniAccountRewardVo.setCommissionType(CommissionTypeEnum.CYCLE_COMMISSION.getType());

                    miniAccountRewardList.add(miniAccountRewardVo);
                }
                //将一级分销固定金额值按照英文逗号分割成数组E1,
                // 将D1对E1的长度取余得到值为F1，则E1[F1]的值为佣金计算的基值，
                // 若是比例计算佣金的话，则该值就是计算佣金的比率，再用订单实际支付金额乘以该比率得到佣金
                if(rewardSchemeDetVo.getOneCommissionRate()!=null&&!rewardSchemeDetVo.getOneCommissionRate().equals("")){
                    String[] E1 = rewardSchemeDetVo.getOneCommissionRate().split(",");
                    log.info("将一级分销固定金额值按照英文逗号分割成数组E1：{}",JSONObject.toJSONString(E1));
                    int F1 = D1%E1.length;
                    log.info("将D1{}对E1的长度{}取余得到值为F1{}",D1,E1.length,F1);
                    String result = E1[F1];

                    BigDecimal amount = new BigDecimal(result).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP).multiply(money);
                    MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                    miniAccountRewardVo.setUserId(memberLevelRelation.getUserId());
                    miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                    miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                    miniAccountRewardVo.setAmount(amount);
                    miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                    miniAccountRewardVo.setOrderId(orderId+"");
                    miniAccountRewardVo.setCommissionType(CommissionTypeEnum.CYCLE_COMMISSION.getType());
                    miniAccountRewardList.add(miniAccountRewardVo);
                }
            }
        }
    }

    @Override
    public void handleCycleCommission(List<RewardSchemeDetVo> rewardSchemeDetList,OrderVo orderVo,OrderItemVo orderItemVo,List<MiniAccountRewardVo> miniAccountRewardList) {
        //1.分佣类型为循环分佣
        List<RewardSchemeDetVo> cycleCommissionList = rewardSchemeDetList.stream().
                filter(e -> e.getRewardType() == RewardTypeEnum.CYCLE_COMMISSION.getStatus()).collect(Collectors.toList());
        log.info("获取分佣类型为循环分佣的奖励方案明细规则cycleCommissionList：{}",JSONObject.toJSONString(cycleCommissionList));
        if(cycleCommissionList!=null&&cycleCommissionList.size()>0){
            MiniAccount orderMiniAccount = miniAccountService.getByShopUserId(orderVo.getOrderUserId());
            log.info("下单人会员，orderMiniAccount: {}", JSONObject.toJSONString(orderMiniAccount));
            MiniAccount C1 = null;
            if(StringUtils.isNotEmpty(orderMiniAccount.getParentId())){
                LambdaQueryWrapper<MiniAccount>parentWrapper = new LambdaQueryWrapper<>();
                parentWrapper.eq(MiniAccount::getUserId,orderMiniAccount.getParentId());
                C1 = miniAccountService.getOne(parentWrapper);
                log.info("上级会员信息，C1: {}", JSONObject.toJSONString(C1));
            }
            MiniAccount C2 = null;
            if(StringUtils.isNotEmpty(orderMiniAccount.getAboveParentId())){
                LambdaQueryWrapper<MiniAccount>aboveParentWrapper = new LambdaQueryWrapper<>();
                aboveParentWrapper.eq(MiniAccount::getUserId,orderMiniAccount.getAboveParentId());
                C2 = miniAccountService.getOne(aboveParentWrapper);
                log.info("上上级会员信息，C2: {}", JSONObject.toJSONString(C2));
            }

            if(cycleCommissionList!=null&&cycleCommissionList.size()>0){
                for (RewardSchemeDetVo rewardSchemeDetVo : cycleCommissionList) {
                    Long memberTypeId = rewardSchemeDetVo.getMemberTypeId();
                    String memberLevelId = rewardSchemeDetVo.getMemberLevelId();
                    if(C1!=null){
                        //上级会员
                        MemberLevelRelationParam C1Param = new MemberLevelRelationParam();
                        C1Param.setMemberTypeId(memberTypeId);
                        C1Param.setUserId(C1.getUserId());
                        //查询上级会员当前会员类型对应会员等级id
                        String C1MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C1Param);
                        log.info("上级会员C1当前会员类型{},对应会员等级id：{}",memberTypeId,C1MemberLevelId);
                        if(StringUtils.isNotEmpty(C1MemberLevelId)){
                            if(StringUtils.isEmpty(memberLevelId) || (StringUtils.isNotEmpty(memberLevelId) &&
                                    memberLevelId.indexOf(C1MemberLevelId) != -1)){
                                Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C1.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                if(b){
                                    //查询出C1直推该会员类型下会员等级属于会员体系的会员数量为D1
                                    Integer D1 = miniAccountService.getDirectMemberCount(memberTypeId,C1.getUserId());
                                    log.info("查询出C1{}直推该会员类型{}下会员等级属于会员体系的会员数量为D1{}",C1.getUserId(),memberTypeId,D1);
                                    //将一级分销固定金额值按照英文逗号分割成数组E1,
                                    // 将D1对E1的长度取余得到值为F1，则E1[F1]的值为佣金计算的基值，
                                    // 即是固定金额算佣金的话，该基值就是佣金
                                    if(rewardSchemeDetVo.getOneCommissionAmount()!=null&&!rewardSchemeDetVo.getOneCommissionAmount().equals("")){
                                        //将一级分销固定金额值按照英文逗号分割成数组E1,
                                        // 将D1对E1的长度取余得到值为F1，则E1[F1]的值为佣金计算的基值，
                                        // 即是固定金额算佣金的话，该基值就是佣金
                                        String[] E1 = rewardSchemeDetVo.getOneCommissionAmount().split(",");
                                        log.info("将一级分销固定金额值按照英文逗号分割成数组E1：{}",JSONObject.toJSONString(E1));
                                        int F1 = D1%E1.length;
                                        log.info("将D1{}对E1的长度{}取余得到值为F1{}",D1,E1.length,F1);
                                        String result = E1[F1];
                                        BigDecimal amount = new BigDecimal(result);

                                        if(amount.compareTo(BigDecimal.ZERO)>0){
                                            MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                            miniAccountRewardVo.setUserId(C1.getUserId());
                                            miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                            miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                            miniAccountRewardVo.setAmount(amount);
                                            miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                            miniAccountRewardVo.setOrderId(orderVo.getId()+"");
                                            miniAccountRewardVo.setCommissionType(CommissionTypeEnum.CYCLE_COMMISSION.getType());
                                            miniAccountRewardList.add(miniAccountRewardVo);
                                        }
                                    }
                                    //将一级分销固定金额值按照英文逗号分割成数组E1,
                                    // 将D1对E1的长度取余得到值为F1，则E1[F1]的值为佣金计算的基值，
                                    // 若是比例计算佣金的话，则该值就是计算佣金的比率，再用订单实际支付金额乘以该比率得到佣金
                                    if(rewardSchemeDetVo.getOneCommissionRate()!=null&&!rewardSchemeDetVo.getOneCommissionRate().equals("")){
                                        String[] E1 = rewardSchemeDetVo.getOneCommissionRate().split(",");
                                        log.info("将一级分销固定金额值按照英文逗号分割成数组E1：{}",JSONObject.toJSONString(E1));
                                        int F1 = D1%E1.length;
                                        log.info("将D1{}对E1的长度{}取余得到值为F1{}",D1,E1.length,F1);
                                        String result = E1[F1];


                                        BigDecimal amount = new BigDecimal(result).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP).multiply(orderItemVo.getRealAmount());
                                        if(amount.compareTo(BigDecimal.ZERO)>0){
                                            MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                            miniAccountRewardVo.setUserId(C1.getUserId());
                                            miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                            miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                            miniAccountRewardVo.setAmount(amount);
                                            miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                            miniAccountRewardVo.setOrderId(orderVo.getId()+"");
                                            miniAccountRewardVo.setCommissionType(CommissionTypeEnum.CYCLE_COMMISSION.getType());
                                            miniAccountRewardList.add(miniAccountRewardVo);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if(C2!=null){
                        MemberLevelRelationParam C2Param = new MemberLevelRelationParam();
                        C2Param.setMemberTypeId(memberTypeId);
                        C2Param.setUserId(C2.getUserId());
                        //查询上上级会员当前会员类型对应会员等级id
                        String C2MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C2Param);
                        log.info("上上级会员C2当前会员类型{},对应会员等级id：{}",memberTypeId,C2MemberLevelId);
                        if(StringUtils.isNotEmpty(C2MemberLevelId)){
                            if(StringUtils.isEmpty(memberLevelId) || (StringUtils.isNotEmpty(memberLevelId) &&
                                    memberLevelId.indexOf(C2MemberLevelId) != -1)){

                                Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C2.getUserId(), rewardSchemeDetVo.getDirectMemberCount());

                                if(b){
                                    //查询出C2间推该会员类型下会员等级属于会员体系的会员数量为D1
                                    Integer D1 = miniAccountService.getInDirectMemberCount(memberTypeId,C2.getUserId());
                                    log.info("查询出C2{}间推该会员类型{}下会员等级属于会员体系的会员数量为D1{}",C2.getUserId(),memberTypeId,D1);
                                    //将二级分销固定金额值按照英文逗号分割成数组E1,
                                    // 将D1对E1的长度取余得到值为F1，则E1[F1]的值为佣金计算的基值，
                                    // 即是固定金额算佣金的话，该基值就是佣金
                                    if(rewardSchemeDetVo.getTwoCommissionAmount()!=null&&!rewardSchemeDetVo.getTwoCommissionAmount().equals("")){
                                        String[] E1 = rewardSchemeDetVo.getTwoCommissionAmount().split(",");
                                        log.info("将二级分销固定金额值按照英文逗号分割成数组E1：{}",JSONObject.toJSONString(E1));
                                        int F1 = D1%E1.length;
                                        log.info("将D1{}对E1的长度{}取余得到值为F1{}",D1,E1.length,F1);
                                        String result = E1[F1];
                                        BigDecimal amount = new BigDecimal(result);
                                        if(amount.compareTo(BigDecimal.ZERO)>0){
                                            MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                            miniAccountRewardVo.setUserId(C2.getUserId());
                                            miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                            miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                            miniAccountRewardVo.setAmount(amount);
                                            miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                            miniAccountRewardVo.setOrderId(orderVo.getId()+"");
                                            miniAccountRewardVo.setCommissionType(CommissionTypeEnum.CYCLE_COMMISSION.getType());
                                            miniAccountRewardList.add(miniAccountRewardVo);
                                        }
                                    }
                                    //将二级分销固定金额值按照英文逗号分割成数组E1,
                                    // 将D1对E1的长度取余得到值为F1，则E1[F1]的值为佣金计算的基值，
                                    // 若是比例计算佣金的话，则该值就是计算佣金的比率，再用订单实际支付金额乘以该比率得到佣金
                                    if(rewardSchemeDetVo.getTwoCommissionRate()!=null&&!rewardSchemeDetVo.getTwoCommissionRate().equals("")){
                                        String[] E1 = rewardSchemeDetVo.getTwoCommissionRate().split(",");
                                        log.info("将二级分销固定金额值按照英文逗号分割成数组E1：{}",JSONObject.toJSONString(E1));
                                        int F1 = D1%E1.length;
                                        log.info("将D1{}对E1的长度{}取余得到值为F1{}",D1,E1.length,F1);
                                        String result = E1[F1];
                                        BigDecimal amount = new BigDecimal(result).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP).multiply(orderItemVo.getRealAmount());
                                        if(amount.compareTo(BigDecimal.ZERO)>0){
                                            MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                            miniAccountRewardVo.setUserId(C2.getUserId());
                                            miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                            miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                            miniAccountRewardVo.setAmount(amount);
                                            miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                            miniAccountRewardVo.setOrderId(orderVo.getId()+"");
                                            miniAccountRewardVo.setCommissionType(CommissionTypeEnum.CYCLE_COMMISSION.getType());
                                            miniAccountRewardList.add(miniAccountRewardVo);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void handleRegionDifferCommission(List<RewardSchemeDetVo> rewardSchemeDetList,
                                             List<RewardSchemeDetVo> allRewardSchemeDetList,
                                             MemberLevelRelation memberLevelRelation,
                                             OrderVo orderVo,
                                             BigDecimal money,
                                             Long orderId,
                                             List<MemberLevelRelation> memberLevelRelationList,
                                             List<MiniAccountRewardVo> miniAccountRewardList) {
        //1.分佣类型为级差
        List<RewardSchemeDetVo> differList = rewardSchemeDetList.stream().filter(e -> e.getRewardType() == RewardTypeEnum.DIFFER.getStatus())
                .collect(Collectors.toList());
        log.info("获取区域类型分佣类型为级差（已过滤收益等级）的奖励方案明细规则differList：{}",JSONObject.toJSONString(differList));
        if(differList!=null&&differList.size()>0){
            for (RewardSchemeDetVo rewardSchemeDetVo : differList) {
                //订单总额级差
                if(rewardSchemeDetVo.getDifferCalType()!=null&&rewardSchemeDetVo.getDifferCalType() == DifferCalTypeEnum.ORDER_AMOUNT.getStatus()){
                    String oneCommissionAmount = rewardSchemeDetVo.getOneCommissionAmount();
                    if(StringUtils.isNotEmpty(oneCommissionAmount)){
                        //则查出在区域类型下比其会员等级低的会员等级得到的佣金之和为H1
                        BigDecimal H1 = BigDecimal.ZERO;
                        //该会员等级在一级分销规则下获得的佣金为H2
                        BigDecimal H2 = new BigDecimal(oneCommissionAmount);
                        Integer regionType = memberLevelRelation.getRegionType();
                        if(regionType == RegionTypeEnum.DISTRICT.getStatus()){
                            H1 = BigDecimal.ZERO;
                        }else if(regionType == RegionTypeEnum.CITY.getStatus()){
                            H1 = BigDecimal.ZERO;
                            LambdaQueryWrapper<MemberLevelRelation>countyWrapper = new LambdaQueryWrapper<>();
                            countyWrapper.eq(MemberLevelRelation::getAgentRegionCode,orderVo.getCountyCode());
                            //区/县会员类型
                            MemberLevelRelation countyCodeMemberLevelRelation = memberLevelRelationService.getOne(countyWrapper);
                            if(countyCodeMemberLevelRelation!=null){
                                String userId = countyCodeMemberLevelRelation.getUserId();
                                List<MiniAccountRewardVo> dataList = miniAccountRewardList.stream().filter(e -> e.getUserId().equals(userId)).collect(Collectors.toList());
                                if(dataList!=null&&dataList.size()>0){
                                    for (MiniAccountRewardVo miniAccountRewardVo : dataList) {
                                        H1 = H1.add(miniAccountRewardVo.getAmount());
                                    }
                                }
                            }
                        }else if(regionType == RegionTypeEnum.PROVINCE.getStatus()){
                            H1 = BigDecimal.ZERO;
                            LambdaQueryWrapper<MemberLevelRelation>countyWrapper = new LambdaQueryWrapper<>();
                            countyWrapper.eq(MemberLevelRelation::getAgentRegionCode,orderVo.getCountyCode());
                            //区/县会员类型
                            MemberLevelRelation countyCodeMemberLevelRelation = memberLevelRelationService.getOne(countyWrapper);
                            if(countyCodeMemberLevelRelation!=null){
                                String userId = countyCodeMemberLevelRelation.getUserId();
                                List<MiniAccountRewardVo> dataList = miniAccountRewardList.stream().filter(e -> e.getUserId().equals(userId)).collect(Collectors.toList());
                                if(dataList!=null&&dataList.size()>0){
                                    for (MiniAccountRewardVo miniAccountRewardVo : dataList) {
                                        H1 = H1.add(miniAccountRewardVo.getAmount());
                                    }
                                }
                            }
                            LambdaQueryWrapper<MemberLevelRelation>cityWrapper = new LambdaQueryWrapper<>();
                            cityWrapper.eq(MemberLevelRelation::getAgentRegionCode,orderVo.getCityCode());
                            //市类型
                            MemberLevelRelation cityMemberLevelRelation = memberLevelRelationService.getOne(cityWrapper);
                            if(cityMemberLevelRelation!=null){
                                String userId = cityMemberLevelRelation.getUserId();
                                List<MiniAccountRewardVo> dataList = miniAccountRewardList.stream().filter(e -> e.getUserId().equals(userId)).collect(Collectors.toList());
                                if(dataList!=null&&dataList.size()>0){
                                    for (MiniAccountRewardVo miniAccountRewardVo : dataList) {
                                        H1 = H1.add(miniAccountRewardVo.getAmount());
                                    }
                                }
                            }
                        }
                        //用H2减H1得到差值H3
                        BigDecimal H3 = H2.subtract(H1);
                        if(H3.compareTo(BigDecimal.ZERO)>0){
                            MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                            miniAccountRewardVo.setUserId(memberLevelRelation.getUserId());
                            miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                            miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                            miniAccountRewardVo.setAmount(H3);
                            miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                            miniAccountRewardVo.setOrderId(orderId+"");
                            miniAccountRewardVo.setCommissionType(CommissionTypeEnum.DIFFER.getType());
                            miniAccountRewardList.add(miniAccountRewardVo);
                        }
                    }
                }
                //分佣率级差
                if(rewardSchemeDetVo.getDifferCalType()!=null&&rewardSchemeDetVo.getDifferCalType() == DifferCalTypeEnum.ORDER_AMOUNT.getStatus()){
                    String oneCommissionRate = rewardSchemeDetVo.getOneCommissionRate();
                    if(StringUtils.isNotEmpty(oneCommissionRate)){
                        //则查出在区域类型下比其会员等级低的会员等级得到的佣金之和为H1
                        BigDecimal H1 = BigDecimal.ZERO;
                        //该会员等级在一级分销规则下获得的佣金为H2
                        BigDecimal H2 = new BigDecimal(oneCommissionRate).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP).subtract(money);
                        Integer regionType = memberLevelRelation.getRegionType();
                        if(regionType == RegionTypeEnum.DISTRICT.getStatus()){
                            H1 = BigDecimal.ZERO;
                        }else if(regionType == RegionTypeEnum.CITY.getStatus()){
                            H1 = BigDecimal.ZERO;
                            LambdaQueryWrapper<MemberLevelRelation>countyWrapper = new LambdaQueryWrapper<>();
                            countyWrapper.eq(MemberLevelRelation::getAgentRegionCode,orderVo.getCountyCode());
                            //区/县会员类型
                            MemberLevelRelation countyCodeMemberLevelRelation = memberLevelRelationService.getOne(countyWrapper);
                            if(countyCodeMemberLevelRelation!=null){
                                String userId = countyCodeMemberLevelRelation.getUserId();
                                List<MiniAccountRewardVo> dataList = miniAccountRewardList.stream().filter(e -> e.getUserId().equals(userId)).collect(Collectors.toList());
                                if(dataList!=null&&dataList.size()>0){
                                    for (MiniAccountRewardVo miniAccountRewardVo : dataList) {
                                        H1 = H1.add(miniAccountRewardVo.getAmount());
                                    }
                                }
                            }
                        }else if(regionType == RegionTypeEnum.PROVINCE.getStatus()){
                            H1 = BigDecimal.ZERO;
                            LambdaQueryWrapper<MemberLevelRelation>countyWrapper = new LambdaQueryWrapper<>();
                            countyWrapper.eq(MemberLevelRelation::getAgentRegionCode,orderVo.getCountyCode());
                            //区/县会员类型
                            MemberLevelRelation countyCodeMemberLevelRelation = memberLevelRelationService.getOne(countyWrapper);
                            if(countyCodeMemberLevelRelation!=null){
                                String userId = countyCodeMemberLevelRelation.getUserId();
                                List<MiniAccountRewardVo> dataList = miniAccountRewardList.stream().filter(e -> e.getUserId().equals(userId)).collect(Collectors.toList());
                                if(dataList!=null&&dataList.size()>0){
                                    for (MiniAccountRewardVo miniAccountRewardVo : dataList) {
                                        H1 = H1.add(miniAccountRewardVo.getAmount());
                                    }
                                }
                            }
                            LambdaQueryWrapper<MemberLevelRelation>cityWrapper = new LambdaQueryWrapper<>();
                            cityWrapper.eq(MemberLevelRelation::getAgentRegionCode,orderVo.getCityCode());
                            //市类型
                            MemberLevelRelation cityMemberLevelRelation = memberLevelRelationService.getOne(cityWrapper);
                            if(cityMemberLevelRelation!=null){
                                String userId = cityMemberLevelRelation.getUserId();
                                List<MiniAccountRewardVo> dataList = miniAccountRewardList.stream().filter(e -> e.getUserId().equals(userId)).collect(Collectors.toList());
                                if(dataList!=null&&dataList.size()>0){
                                    for (MiniAccountRewardVo miniAccountRewardVo : dataList) {
                                        H1 = H1.add(miniAccountRewardVo.getAmount());
                                    }
                                }
                            }
                        }
                        //用H2减H1得到差值H3
                        BigDecimal H3 = H2.subtract(H1);
                        if(H3.compareTo(BigDecimal.ZERO)>0){
                            MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                            miniAccountRewardVo.setUserId(memberLevelRelation.getUserId());
                            miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                            miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                            miniAccountRewardVo.setAmount(H3);
                            miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                            miniAccountRewardVo.setOrderId(orderId+"");
                            miniAccountRewardVo.setCommissionType(CommissionTypeEnum.DIFFER.getType());
                            miniAccountRewardList.add(miniAccountRewardVo);
                        }
                    }
                }
            }
        }
    }
    @Override
    public void handleDifferCommission(List<RewardSchemeDetVo> rewardSchemeDetList, String shopUserId,Long orderId,
                                       BigDecimal money,Long orderMemberTypeId,Long skuId,Long productId,
                                       Integer buyType,Integer productQuantity,
                                       List<MiniAccountRewardVo> miniAccountRewardList) {
        //1.分佣类型为级差
        List<RewardSchemeDetVo> differList = rewardSchemeDetList.stream().filter(e -> e.getRewardType() == RewardTypeEnum.DIFFER.getStatus())
                .collect(Collectors.toList());
        log.info("获取分佣类型为级差的奖励方案明细规则differList：{}",JSONObject.toJSONString(differList));
        if(differList!=null&&differList.size()>0){
            //过滤分佣规则业务员分佣为是
            List<RewardSchemeDetVo> saleYesDifferList = differList.stream().filter(e -> e.getSalesmanFlag() != null
                    && e.getSalesmanFlag() == SalesmanFlagEnum.YES.getStatus()).collect(Collectors.toList());
            log.info("获取业务员分佣为是的奖励方案明细规则saleYesDifferList：{}",JSONObject.toJSONString(saleYesDifferList));
            if(saleYesDifferList!=null&&saleYesDifferList.size()>0){
                //会员类型
                MemberTypeParam memberTypeParam = new MemberTypeParam();
                memberTypeParam.setStatus(CommonConstants.NUMBER_ONE);
                List<MemberTypeVo> memberTypeList = memberTypeService.getMemberType(memberTypeParam);
                if(memberTypeList!=null&&memberTypeList.size()>0){
                    for (MemberTypeVo memberTypeVo : memberTypeList) {
                        Long memberTypeId = Long.valueOf(memberTypeVo.getId());
                        //过滤会员类型
                        saleYesDifferList = saleYesDifferList.stream().filter(e->e.getMemberTypeId().equals(memberTypeId)).collect(Collectors.toList());
                        if(saleYesDifferList!=null&&saleYesDifferList.size()>0){
                            MiniAccount orderMiniAccount = miniAccountService.getByShopUserId(shopUserId);
                            log.info("下单人会员，orderMiniAccount: {}", JSONObject.toJSONString(orderMiniAccount));
                            MemberLevelRelationParam orderParam = new MemberLevelRelationParam();
                            orderParam.setMemberTypeId(memberTypeId);
                            orderParam.setUserId(orderMiniAccount.getUserId());
                            //查询下单人会员当前会员类型对应会员等级id
                            String orderMemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(orderParam);
                            MiniAccount C4 = null;
                            if(StringUtils.isNotEmpty(orderMiniAccount.getSaleUserId())){
                                LambdaQueryWrapper<MiniAccount>saleWrapper = new LambdaQueryWrapper<>();
                                saleWrapper.eq(MiniAccount::getUserId,orderMiniAccount.getSaleUserId());
                                // 则根据下单人会员表记录对应的业务员ID查出业务员会员C4，
                                C4 = miniAccountService.getOne(saleWrapper);
                                log.info("业务员会员信息，C4: {}", JSONObject.toJSONString(C4));
                            }
                            if(C4!=null){
                                MemberLevelRelationParam C4Param = new MemberLevelRelationParam();
                                C4Param.setMemberTypeId(memberTypeId);
                                C4Param.setUserId(C4.getUserId());
                                //查询上上级会员当前会员类型对应会员等级id
                                String C4MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C4Param);
                                log.info("业务员C4当前会员类型对应会员等级id：{}",C4MemberLevelId);
                                if(StringUtils.isNotEmpty(C4MemberLevelId)){
                                    //过滤收益等级
                                    List<RewardSchemeDetVo> C4SaleYesDifferList = saleYesDifferList.stream().filter(e -> StringUtils.isEmpty(e.getMemberLevelId()) || (StringUtils.isNotEmpty(e.getMemberLevelId()) &&
                                            e.getMemberLevelId().indexOf(C4MemberLevelId) != -1)).collect(Collectors.toList());
                                    log.info("过滤C4会员类型等级奖励方案明细规则C4SaleYesDifferList：{}",JSONObject.toJSONString(C4SaleYesDifferList));
                                    List<RewardSchemeDetVo> orderSaleYesDifferList = saleYesDifferList.stream().filter(e -> StringUtils.isEmpty(e.getMemberLevelId()) || (StringUtils.isNotEmpty(e.getMemberLevelId()) &&
                                            e.getMemberLevelId().indexOf(orderMemberLevelId) != -1)).collect(Collectors.toList());
                                    log.info("过滤下单人会员类型等级奖励方案明细规则orderSaleYesDifferList：{}",JSONObject.toJSONString(orderSaleYesDifferList));
                                    if(C4SaleYesDifferList!=null&&C4SaleYesDifferList.size()>0){
                                        List<RewardSchemeDetVo> C4DifferCallTypeList = C4SaleYesDifferList.stream().filter(e -> e.getDifferCalType() == DifferCalTypeEnum.ORDER_AMOUNT.getStatus())
                                                .collect(Collectors.toList());
                                        log.info("过滤级差计算方式为金额奖励规则C4DifferCallTypeList：{}",JSONObject.toJSONString(C4DifferCallTypeList));
                                        if(C4DifferCallTypeList!=null&&C4DifferCallTypeList.size()>0&&buyType!=null){
                                            for (RewardSchemeDetVo rewardSchemeDetVo : C4DifferCallTypeList) {
                                                Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C4.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                                if(b){
                                                    SkuStock skuStock = remoteGoodsService.findSkuStockById(skuId);
                                                    ProductVo product = remoteGoodsService.findProductById(productId);
                                                    //则先查询C4会员等级G1在此订单明细的购买类型下购买相同商品的价格为H1
                                                    BigDecimal H1 = skuStock.getPrice();
                                                    if(buyType == BuyTypeEnum.ONE.getCode()){
                                                        //购买类型为首单
                                                        List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(C4MemberLevelId);
                                                        if(memberLevelGoodsPriceList!=null&&memberLevelGoodsPriceList.size()>0){
                                                            List<MemberLevelGoodsPrice> dataList = memberLevelGoodsPriceList.stream().filter(e->e.getSkuId().equals(skuId)).collect(Collectors.toList());
                                                            if (dataList.get(0).getMemberLevelPrice()!=null){
                                                                if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberPriceType()){
                                                                    H1 = dataList.get(0).getMemberLevelPrice();
                                                                }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberPriceType()){
                                                                    H1 = H1.multiply(dataList.get(0).getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP);
                                                                }
                                                            }
                                                        }
                                                    }else if(buyType == BuyTypeEnum.AGAIN.getCode()){
                                                        //购买类型为复购
                                                        List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPriceList = remoteGoodsService.selectMemberGoodsAgainPrice(C4MemberLevelId);
                                                        if(memberLevelGoodsAgainPriceList!=null&&memberLevelGoodsAgainPriceList.size()>0){
                                                            List<MemberLevelGoodsAgainPrice> dataList = memberLevelGoodsAgainPriceList.stream().filter(e -> e.getSkuId().equals(skuId)).collect(Collectors.toList());
                                                            if (dataList.get(0).getMemberLevelAgainPrice()!=null){
                                                                if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberAgainPriceType()){
                                                                    H1 = dataList.get(0).getMemberLevelAgainPrice();
                                                                }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberAgainPriceType()){
                                                                    H1 = H1.multiply(dataList.get(0).getMemberLevelAgainPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP);
                                                                }
                                                            }
                                                        }
                                                    }
                                                    log.info("先查询C4会员等级G1在此订单明细的购买类型下购买相同商品的价格为H1:{}",H1);
                                                    BigDecimal K = H1.multiply(new BigDecimal(productQuantity));
                                                    //然后用订单明细数量乘以价格，得到订单金额K
                                                    log.info("订单明细数量乘以价格，得到订单金额K:{}",K);
                                                    if(money.compareTo(K)>0){
                                                        MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                                        miniAccountRewardVo.setUserId(C4.getUserId());
                                                        miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                                        miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                                        miniAccountRewardVo.setAmount(money.subtract(K));
                                                        miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                                        miniAccountRewardVo.setOrderId(orderId+"");
                                                        miniAccountRewardVo.setCommissionType(CommissionTypeEnum.DIFFER.getType());
                                                        miniAccountRewardList.add(miniAccountRewardVo);
                                                    }
                                                }
                                            }
                                        }
                                        List<RewardSchemeDetVo> C4DifferCallTypeList2 = C4SaleYesDifferList.stream().filter(e -> e.getDifferCalType() == DifferCalTypeEnum.COMMISSION_SHARING.getStatus())
                                                .collect(Collectors.toList());
                                        log.info("过滤级差计算方式为分佣率奖励规则C4DifferCallTypeList2：{}",JSONObject.toJSONString(C4DifferCallTypeList2));
                                        if(C4DifferCallTypeList2!=null&&C4DifferCallTypeList2.size()>0){
                                            for (RewardSchemeDetVo rewardSchemeDetVo : C4DifferCallTypeList2) {
                                                if(StringUtils.isNotEmpty(rewardSchemeDetVo.getOneCommissionRate())){
                                                    BigDecimal H2 = new BigDecimal(rewardSchemeDetVo.getOneCommissionRate());
                                                    BigDecimal H3 = BigDecimal.ZERO;
                                                    if(orderSaleYesDifferList!=null){
                                                        RewardSchemeDetVo orderRewardSchemeDetVo = orderSaleYesDifferList.get(0);
                                                        if(StringUtils.isNotEmpty(orderRewardSchemeDetVo.getOneCommissionRate())){
                                                            H3 = new BigDecimal(orderRewardSchemeDetVo.getOneCommissionRate());
                                                        }
                                                    }
                                                    if(H2.compareTo(H3)>0){
                                                        MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                                        miniAccountRewardVo.setUserId(C4.getUserId());
                                                        miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                                        miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                                        miniAccountRewardVo.setAmount(money.multiply((H2.subtract(H3)).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP)));
                                                        miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                                        miniAccountRewardVo.setOrderId(orderId+"");
                                                        miniAccountRewardVo.setCommissionType(CommissionTypeEnum.DIFFER.getType());
                                                        miniAccountRewardList.add(miniAccountRewardVo);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            //过滤分佣规则业务员分佣为否
            List<RewardSchemeDetVo> saleNoDifferList = differList.stream().filter(e -> (e.getSalesmanFlag() == null)||(e.getSalesmanFlag() != null
                    && e.getSalesmanFlag() == SalesmanFlagEnum.NO.getStatus())).collect(Collectors.toList());
            log.info("获取业务员分佣为否的奖励方案明细规则saleNoDifferList：{}",JSONObject.toJSONString(saleNoDifferList));
            if(saleNoDifferList!=null&&saleNoDifferList.size()>0){
                //会员类型
                MemberTypeParam memberTypeParam = new MemberTypeParam();
                memberTypeParam.setStatus(CommonConstants.NUMBER_ONE);
                List<MemberTypeVo> memberTypeList = memberTypeService.getMemberType(memberTypeParam);
                if(memberTypeList!=null&&memberTypeList.size()>0){
                    for (MemberTypeVo memberTypeVo : memberTypeList) {
                        Long memberTypeId = Long.valueOf(memberTypeVo.getId());
                        //过滤会员类型
                        saleYesDifferList = saleYesDifferList.stream().filter(e->e.getMemberTypeId().equals(memberTypeId)).collect(Collectors.toList());
                        if(saleYesDifferList!=null&&saleYesDifferList.size()>0){
                            MiniAccount orderMiniAccount = miniAccountService.getByShopUserId(shopUserId);
                            log.info("下单人会员，orderMiniAccount: {}", JSONObject.toJSONString(orderMiniAccount));
                            MemberLevelRelationParam orderParam = new MemberLevelRelationParam();
                            orderParam.setMemberTypeId(memberTypeId);
                            orderParam.setUserId(orderMiniAccount.getUserId());
                            String orderMemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(orderParam);
                            List<RewardSchemeDetVo> orderSaleNoDifferList = saleNoDifferList.stream().filter(e -> StringUtils.isEmpty(e.getMemberLevelId()) || (StringUtils.isNotEmpty(e.getMemberLevelId()) &&
                                    e.getMemberLevelId().indexOf(orderMemberLevelId) != -1)).collect(Collectors.toList());
                            log.info("过滤下单人会员类型等级奖励方案明细规则orderSaleNoDifferList：{}",JSONObject.toJSONString(orderSaleNoDifferList));
                            if(StringUtils.isNotEmpty(orderMemberLevelId)){
                                MemberLevelRuleVo orderMemberLevelRule =
                                        memberLevelRuleService.getByMemberLevelId(Long.valueOf(orderMemberLevelId));
                                if(orderMemberLevelRule!=null){
                                    MiniAccount C9 = null;
                                    String parentId = orderMiniAccount.getParentId();
                                    List<RewardSchemeDetVo>C9RewardSchemeList = null;
                                    MemberLevelRuleVo C9MemberLevelRule = null;
                                    String C9MemberLevelId = "";
                                    while (StringUtils.isNotEmpty(parentId) || C9 == null){
                                        LambdaQueryWrapper<MiniAccount>parentWrapper = new LambdaQueryWrapper<>();
                                        parentWrapper.eq(MiniAccount::getUserId,parentId);
                                        //则根据下单用户查询其上级会员C1
                                        MiniAccount C1 = miniAccountService.getOne(parentWrapper);
                                        log.info("上级用户id{}对应小程序用户: {}", parentId,JSONObject.toJSONString(C1));
                                        if(C1!=null){
                                            MemberLevelRelationParam C1Param = new MemberLevelRelationParam();
                                            C1Param.setMemberTypeId(memberTypeId);
                                            C1Param.setUserId(C1.getUserId());
                                            //查询上上级会员当前会员类型对应会员等级id
                                            String C1MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C1Param);
                                            log.info("上级会员C1当前会员类型对应会员等级id：{}",C1MemberLevelId);
                                            if(StringUtils.isNotEmpty(C1MemberLevelId)){
                                                MemberLevelRuleVo C1MemberLevelRule =
                                                        memberLevelRuleService.getByMemberLevelId(Long.valueOf(C1MemberLevelId));
                                                if(C1MemberLevelRule!=null){
                                                    //判断C1在此会员类型下的会员等级要大于等于下单人（或者上一个获得佣金的会员）的会员等级
                                                    if(orderMemberLevelRule!=null&&C1MemberLevelRule!=null&&C1MemberLevelRule.getSort()>=orderMemberLevelRule.getSort()){
                                                        //过滤收益等级
                                                        List<RewardSchemeDetVo> C1SaleNoDifferList  = saleNoDifferList.stream().filter(e -> StringUtils.isEmpty(e.getMemberLevelId()) || (StringUtils.isNotEmpty(e.getMemberLevelId()) &&
                                                                e.getMemberLevelId().indexOf(C1MemberLevelId) != -1)).collect(Collectors.toList());
                                                        log.info("过滤会员类型等级奖励方案明细规则C1SaleNoDifferList：{}",JSONObject.toJSONString(C1SaleNoDifferList));
                                                        if(C1SaleNoDifferList!=null&&C1SaleNoDifferList.size()>0){

                                                            List<RewardSchemeDetVo>returnList = new ArrayList<>();
                                                            for (RewardSchemeDetVo rewardSchemeDetVo : C1SaleNoDifferList) {
                                                                Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C1.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                                                if(b){
                                                                    returnList.add(rewardSchemeDetVo);
                                                                }
                                                            }
                                                            if(returnList!=null&&returnList.size()>0){
                                                                C9RewardSchemeList = returnList;
                                                                C9 = C1;
                                                                C9MemberLevelRule = C1MemberLevelRule;
                                                                C9MemberLevelId = C1MemberLevelId;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                            if(C9 == null){
                                                parentId = C1.getParentId();
                                            }else{
                                                parentId = "";
                                            }
                                        }else{
                                            parentId = "";
                                        }
                                    }

                                    if(C9!=null){

                                        log.info("分佣规则业务员分佣为否符合级差条件第一个会员C9:{}",JSONObject.toJSONString(C9));
                                        BigDecimal K9 = BigDecimal.ZERO;
                                        BigDecimal H92 = BigDecimal.ZERO;
                                        if(C9RewardSchemeList!=null&&C9RewardSchemeList.size()>0){
                                            log.info("C9对应的奖励明细规则:{}",JSONObject.toJSONString(C9RewardSchemeList));
                                            List<RewardSchemeDetVo> C9DifferRewardSchemeList = C9RewardSchemeList.stream().filter(e -> e.getDifferCalType() == DifferCalTypeEnum.ORDER_AMOUNT.getStatus())
                                                    .collect(Collectors.toList());
                                            log.info("过滤级差计算方式为金额奖励规则C9DifferRewardSchemeList：{}",JSONObject.toJSONString(C9DifferRewardSchemeList));
                                            if(C9DifferRewardSchemeList!=null&&C9DifferRewardSchemeList.size()>0 && buyType!=null){
                                                RewardSchemeDetVo rewardSchemeDetVo = C9DifferRewardSchemeList.get(0);
                                                SkuStock skuStock = remoteGoodsService.findSkuStockById(skuId);
                                                ProductVo product = remoteGoodsService.findProductById(productId);
                                                //则先查询C9会员等级在此订单明细的购买类型下购买相同商品的价格为H9
                                                BigDecimal H9 = skuStock.getPrice();
                                                if(buyType == BuyTypeEnum.ONE.getCode()){
                                                    //购买类型为首单
                                                    List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(C9MemberLevelId);
                                                    if(memberLevelGoodsPriceList!=null&&memberLevelGoodsPriceList.size()>0){
                                                        List<MemberLevelGoodsPrice> dataList = memberLevelGoodsPriceList.stream().filter(e->e.getSkuId().equals(skuId)).collect(Collectors.toList());
                                                        if (dataList.get(0).getMemberLevelPrice()!=null){
                                                            if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberPriceType()){
                                                                H9 = dataList.get(0).getMemberLevelPrice();
                                                            }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberPriceType()){
                                                                H9 = H9.multiply(dataList.get(0).getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP);
                                                            }
                                                        }
                                                    }
                                                }else if(buyType == BuyTypeEnum.AGAIN.getCode()){
                                                    //购买类型为复购
                                                    List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPriceList = remoteGoodsService.selectMemberGoodsAgainPrice(C9MemberLevelId);
                                                    if(memberLevelGoodsAgainPriceList!=null&&memberLevelGoodsAgainPriceList.size()>0){
                                                        List<MemberLevelGoodsAgainPrice> dataList = memberLevelGoodsAgainPriceList.stream().filter(e -> e.getSkuId().equals(skuId)).collect(Collectors.toList());
                                                        if (dataList.get(0).getMemberLevelAgainPrice()!=null){
                                                            if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberAgainPriceType()){
                                                                H9 = dataList.get(0).getMemberLevelAgainPrice();
                                                            }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberAgainPriceType()){
                                                                H9 = H9.multiply(dataList.get(0).getMemberLevelAgainPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP);
                                                            }
                                                        }
                                                    }
                                                }
                                                log.info("先查询C4会员等级G1在此订单明细的购买类型下购买相同商品的价格为H9:{}",H9);
                                                K9 = H9.multiply(new BigDecimal(productQuantity));
                                                //然后用订单明细数量乘以价格，得到订单金额K
                                                log.info("订单明细数量乘以价格，得到订单金额K:{}",K9);
                                                if(money.compareTo(K9)>0){
                                                    MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                                    miniAccountRewardVo.setUserId(C9.getUserId());
                                                    miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                                    miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                                    miniAccountRewardVo.setAmount(money.subtract(K9));
                                                    miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                                    miniAccountRewardVo.setOrderId(orderId+"");
                                                    miniAccountRewardVo.setCommissionType(CommissionTypeEnum.DIFFER.getType());
                                                    miniAccountRewardList.add(miniAccountRewardVo);
                                                }
                                            }
                                            List<RewardSchemeDetVo> C9DifferRewardSchemeList2 = C9RewardSchemeList.stream().filter(e -> e.getDifferCalType() == DifferCalTypeEnum.COMMISSION_SHARING.getStatus())
                                                    .collect(Collectors.toList());
                                            log.info("过滤级差计算方式为分佣率奖励规则C9DifferRewardSchemeList2：{}",JSONObject.toJSONString(C9DifferRewardSchemeList2));
                                            if(C9DifferRewardSchemeList2!=null&&C9DifferRewardSchemeList2.size()>0){
                                                RewardSchemeDetVo C9rewardSchemeDetVo = C9DifferRewardSchemeList2.get(0);
                                                if(StringUtils.isNotEmpty(C9rewardSchemeDetVo.getOneCommissionRate())){
                                                    H92 = new BigDecimal(C9rewardSchemeDetVo.getOneCommissionRate());
                                                    BigDecimal H3 = BigDecimal.ZERO;
                                                    if(orderSaleNoDifferList!=null){
                                                        RewardSchemeDetVo orderRewardSchemeDetVo = orderSaleNoDifferList.get(0);
                                                        if(StringUtils.isNotEmpty(orderRewardSchemeDetVo.getOneCommissionRate())){
                                                            H3 = new BigDecimal(orderRewardSchemeDetVo.getOneCommissionRate());
                                                        }
                                                    }
                                                    if(H92.compareTo(H3)>0){
                                                        MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                                        miniAccountRewardVo.setUserId(C9.getUserId());
                                                        miniAccountRewardVo.setRewardId(C9rewardSchemeDetVo.getRewardId()+"");
                                                        miniAccountRewardVo.setRewardDetId(C9rewardSchemeDetVo.getId()+"");
                                                        miniAccountRewardVo.setAmount(money.multiply((H92.subtract(H3)).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP)));
                                                        miniAccountRewardVo.setRemark(C9rewardSchemeDetVo.getCommissionTitle());
                                                        miniAccountRewardVo.setOrderId(orderId+"");
                                                        miniAccountRewardVo.setCommissionType(CommissionTypeEnum.DIFFER.getType());
                                                        miniAccountRewardList.add(miniAccountRewardVo);
                                                    }
                                                }
                                            }
                                        }
                                        MiniAccount C10 = null;
                                        String C9ParentId = orderMiniAccount.getParentId();
                                        List<RewardSchemeDetVo>C10RewardSchemeList = null;
                                        MemberLevelRuleVo C10MemberLevelRule = null;
                                        String C10MemberLevelId = "";

                                        while (StringUtils.isNotEmpty(C9ParentId) || C10 == null){
                                            LambdaQueryWrapper<MiniAccount>parentWrapper = new LambdaQueryWrapper<>();
                                            parentWrapper.eq(MiniAccount::getUserId,C9ParentId);
                                            //则根据下单用户查询其上级会员C1
                                            MiniAccount C1 = miniAccountService.getOne(parentWrapper);
                                            log.info("上级用户id{}对应小程序用户: {}", C9ParentId,JSONObject.toJSONString(C1));
                                            if(C1!=null){
                                                MemberLevelRelationParam C1Param = new MemberLevelRelationParam();
                                                C1Param.setMemberTypeId(memberTypeId);
                                                C1Param.setUserId(C1.getUserId());
                                                //查询上上级会员当前会员类型对应会员等级id
                                                String C1MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C1Param);
                                                log.info("上级会员C1当前会员类型对应会员等级id：{}",C1MemberLevelId);
                                                if(StringUtils.isNotEmpty(C1MemberLevelId)){
                                                    MemberLevelRuleVo C1MemberLevelRule =
                                                            memberLevelRuleService.getByMemberLevelId(Long.valueOf(C1MemberLevelId));
                                                    if(C1MemberLevelRule!=null){
                                                        //判断C1在此会员类型下的会员等级要大于等于下单人（或者上一个获得佣金的会员）的会员等级
                                                        if(C9MemberLevelRule!=null&&C1MemberLevelRule!=null&&C1MemberLevelRule.getSort()>=C9MemberLevelRule.getSort()){
                                                            //过滤收益等级
                                                            List<RewardSchemeDetVo> C1SaleNoDifferList  = saleNoDifferList.stream().filter(e -> StringUtils.isEmpty(e.getMemberLevelId()) || (StringUtils.isNotEmpty(e.getMemberLevelId()) &&
                                                                    e.getMemberLevelId().indexOf(C1MemberLevelId) != -1)).collect(Collectors.toList());
                                                            log.info("过滤会员类型等级奖励方案明细规则C1SaleNoDifferList：{}",JSONObject.toJSONString(C1SaleNoDifferList));
                                                            if(C1SaleNoDifferList!=null&&C1SaleNoDifferList.size()>0){

                                                                List<RewardSchemeDetVo>returnList = new ArrayList<>();
                                                                for (RewardSchemeDetVo rewardSchemeDetVo : C1SaleNoDifferList) {
                                                                    Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C1.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                                                    if(b){
                                                                        returnList.add(rewardSchemeDetVo);
                                                                    }
                                                                }
                                                                if(returnList!=null&&returnList.size()>0){
                                                                    C10RewardSchemeList = returnList;
                                                                    C10 = C1;
                                                                    C10MemberLevelRule = C1MemberLevelRule;
                                                                    C10MemberLevelId = C1MemberLevelId;
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                                if(C10 == null){
                                                    C9ParentId = C1.getParentId();
                                                }else{
                                                    C9ParentId = "";
                                                }
                                            }else{
                                                C9ParentId = "";
                                            }
                                        }
                                        if(C10!=null){
                                            log.info("分佣规则业务员分佣为否符合级差条件第二个会员C10:{}",JSONObject.toJSONString(C10));
                                            if(C10RewardSchemeList!=null&&C10RewardSchemeList.size()>0){
                                                log.info("C10对应的奖励明细规则:{}",JSONObject.toJSONString(C10RewardSchemeList));
                                                List<RewardSchemeDetVo> C10DifferRewardSchemeList = C10RewardSchemeList.stream().filter(e -> e.getDifferCalType() == DifferCalTypeEnum.ORDER_AMOUNT.getStatus())
                                                        .collect(Collectors.toList());
                                                log.info("过滤级差计算方式为金额奖励规则C10DifferRewardSchemeList：{}",JSONObject.toJSONString(C10DifferRewardSchemeList));
                                                if(C10DifferRewardSchemeList!=null&&C10DifferRewardSchemeList.size()>0 &&buyType!=null){
                                                    RewardSchemeDetVo rewardSchemeDetVo = C10DifferRewardSchemeList.get(0);
                                                    SkuStock skuStock = remoteGoodsService.findSkuStockById(skuId);
                                                    ProductVo product = remoteGoodsService.findProductById(productId);
                                                    //则先查询C10会员等级在此订单明细的购买类型下购买相同商品的价格为H10
                                                    BigDecimal H10 = skuStock.getPrice();
                                                    if(buyType == BuyTypeEnum.ONE.getCode()){
                                                        //购买类型为首单
                                                        List<MemberLevelGoodsPrice> memberLevelGoodsPriceList = remoteGoodsService.selectMemberGoodsPrice(C10MemberLevelId);
                                                        if(memberLevelGoodsPriceList!=null&&memberLevelGoodsPriceList.size()>0){
                                                            List<MemberLevelGoodsPrice> dataList = memberLevelGoodsPriceList.stream().filter(e->e.getSkuId().equals(skuId)).collect(Collectors.toList());
                                                            if (dataList.get(0).getMemberLevelPrice()!=null){
                                                                if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberPriceType()){
                                                                    H10 = dataList.get(0).getMemberLevelPrice();
                                                                }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberPriceType()){
                                                                    H10 = H10.multiply(dataList.get(0).getMemberLevelPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP);
                                                                }
                                                            }
                                                        }
                                                    }else if(buyType == BuyTypeEnum.AGAIN.getCode()){
                                                        //购买类型为复购
                                                        List<MemberLevelGoodsAgainPrice> memberLevelGoodsAgainPriceList = remoteGoodsService.selectMemberGoodsAgainPrice(C9MemberLevelId);
                                                        if(memberLevelGoodsAgainPriceList!=null&&memberLevelGoodsAgainPriceList.size()>0){
                                                            List<MemberLevelGoodsAgainPrice> dataList = memberLevelGoodsAgainPriceList.stream().filter(e -> e.getSkuId().equals(skuId)).collect(Collectors.toList());
                                                            if (dataList.get(0).getMemberLevelAgainPrice()!=null){
                                                                if(MemberPriceTypeEnum.MEMBER_PRICE_FIXED_AMOUNT.getStatus()==product.getMemberAgainPriceType()){
                                                                    H10 = dataList.get(0).getMemberLevelAgainPrice();
                                                                }else if(MemberPriceTypeEnum.MEMBER_PRICE_PERCENTAGE.getStatus()==product.getMemberAgainPriceType()){
                                                                    H10 = H10.multiply(dataList.get(0).getMemberLevelAgainPrice()).divide(new BigDecimal(100),4, BigDecimal.ROUND_HALF_UP);
                                                                }
                                                            }
                                                        }
                                                    }
                                                    log.info("先查询C4会员等级G1在此订单明细的购买类型下购买相同商品的价格为H9:{}",H10);
                                                    BigDecimal K10 = H10.multiply(new BigDecimal(productQuantity));
                                                    //然后用订单明细数量乘以价格，得到订单金额K
                                                    log.info("订单明细数量乘以价格，得到订单金额K10:{}",K10);
                                                    if(K9.compareTo(K10)>0){
                                                        MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                                        miniAccountRewardVo.setUserId(C10.getUserId());
                                                        miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                                        miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                                        miniAccountRewardVo.setAmount(K9.subtract(K10));
                                                        miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());
                                                        miniAccountRewardVo.setOrderId(orderId+"");
                                                        miniAccountRewardVo.setCommissionType(CommissionTypeEnum.DIFFER.getType());

                                                        miniAccountRewardList.add(miniAccountRewardVo);
                                                    }
                                                }

                                                List<RewardSchemeDetVo> C10DifferRewardSchemeList2 = C10RewardSchemeList.stream().filter(e -> e.getDifferCalType() == DifferCalTypeEnum.COMMISSION_SHARING.getStatus())
                                                        .collect(Collectors.toList());
                                                log.info("过滤级差计算方式为分佣率奖励规则C10DifferRewardSchemeList2：{}",JSONObject.toJSONString(C10DifferRewardSchemeList2));
                                                if(C10DifferRewardSchemeList2!=null&&C10DifferRewardSchemeList2.size()>0){
                                                    RewardSchemeDetVo C10rewardSchemeDetVo = C10DifferRewardSchemeList2.get(0);
                                                    if(StringUtils.isNotEmpty(C10rewardSchemeDetVo.getOneCommissionRate())){
                                                        BigDecimal H10 = new BigDecimal(C10rewardSchemeDetVo.getOneCommissionRate());
                                                        if(H10.compareTo(H92)>0){
                                                            MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                                            miniAccountRewardVo.setUserId(C10.getUserId());
                                                            miniAccountRewardVo.setRewardId(C10rewardSchemeDetVo.getRewardId()+"");
                                                            miniAccountRewardVo.setRewardDetId(C10rewardSchemeDetVo.getId()+"");
                                                            miniAccountRewardVo.setAmount(money.multiply((H10.subtract(H92)).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP)));
                                                            miniAccountRewardVo.setRemark(C10rewardSchemeDetVo.getCommissionTitle());

                                                            miniAccountRewardVo.setOrderId(orderId+"");
                                                            miniAccountRewardVo.setCommissionType(CommissionTypeEnum.DIFFER.getType());
                                                            miniAccountRewardList.add(miniAccountRewardVo);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }



    @Override
    public void handleTeamCommission(List<RewardSchemeDetVo> rewardSchemeDetList, String shopUserId,Long orderId,
                                     BigDecimal money,Long orderMemberType,
                                     List<MiniAccountRewardVo> miniAccountRewardList) {
        List<RewardSchemeDetVo> teamList = rewardSchemeDetList.stream().filter(e -> e.getRewardType() == RewardTypeEnum.TEAM.getStatus())
                .collect(Collectors.toList());
        log.info("过滤团队奖励规则teamList：{}",JSONObject.toJSONString(teamList));
        if(teamList!=null&&teamList.size()>0){

            //会员类型
            MemberTypeParam memberTypeParam = new MemberTypeParam();
            memberTypeParam.setStatus(CommonConstants.NUMBER_ONE);
            List<MemberTypeVo> memberTypeList = memberTypeService.getMemberType(memberTypeParam);
            if(memberTypeList!=null&&memberTypeList.size()>0){
                for (MemberTypeVo memberTypeVo : memberTypeList) {
                    Long memberTypeId = Long.valueOf(memberTypeVo.getId());
                    //过滤会员类型
                    teamList = teamList.stream().filter(e->e.getMemberTypeId().equals(memberTypeId)).collect(Collectors.toList());
                    if(teamList!=null&&teamList.size()>0){
                        MiniAccount orderMiniAccount = miniAccountService.getByShopUserId(shopUserId);
                        log.info("下单人会员，orderMiniAccount: {}", JSONObject.toJSONString(orderMiniAccount));
                        List<RewardSchemeDetVo> saleYesList = teamList.stream().filter(e -> e.getSalesmanFlag() != null
                                && e.getSalesmanFlag() == SalesmanFlagEnum.YES.getStatus()).collect(Collectors.toList());
                        log.info("获取业务员分佣为是的奖励方案明细规则saleYesList：{}",JSONObject.toJSONString(saleYesList));
                        if(saleYesList!=null&&saleYesList.size()>0){
                            MiniAccount C4 = null;
                            if(StringUtils.isNotEmpty(orderMiniAccount.getSaleUserId())){
                                if(StringUtils.isNotEmpty(orderMiniAccount.getSaleUserId())){
                                    LambdaQueryWrapper<MiniAccount>saleWrapper = new LambdaQueryWrapper<>();
                                    saleWrapper.eq(MiniAccount::getUserId,orderMiniAccount.getSaleUserId());
                                    // 则根据下单人会员表记录对应的业务员ID查出业务员会员C4，
                                    C4 = miniAccountService.getOne(saleWrapper);
                                    log.info("业务员会员信息，C4: {}", JSONObject.toJSONString(C4));
                                }
                            }
                            if(C4!=null){
                                MemberLevelRelationParam C4Param = new MemberLevelRelationParam();
                                C4Param.setMemberTypeId(memberTypeId);
                                C4Param.setUserId(C4.getUserId());
                                //查询上上级会员当前会员类型对应会员等级id
                                String C4MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C4Param);
                                log.info("业务员会员C4当前会员类型对应会员等级id：{}",C4MemberLevelId);
                                if(StringUtils.isNotEmpty(C4MemberLevelId)){
                                    //过滤收益等级
                                    saleYesList = saleYesList.stream().filter(e -> StringUtils.isEmpty(e.getMemberLevelId()) || (StringUtils.isNotEmpty(e.getMemberLevelId()) &&
                                            e.getMemberLevelId().indexOf(C4MemberLevelId) != -1)).collect(Collectors.toList());
                                    log.info("过滤会员类型等级奖励方案明细规则saleYesList：{}",JSONObject.toJSONString(saleYesList));
                                    if(saleYesList!=null&&saleYesList.size()>0){
                                        //过滤一级比例，一级金额
                                        saleYesList = saleYesList.stream().filter(e->StringUtils.isNotEmpty(e.getTwoCommissionRate())||StringUtils.isNotEmpty(e.getTwoCommissionAmount()))
                                                .collect(Collectors.toList());
                                        log.info("过滤一级比例，一级金额奖励规则saleYesCommissionList：{}",JSONObject.toJSONString(saleYesList));
                                    }
                                    if(saleYesList!=null&&saleYesList.size()>0){
                                        RewardSchemeDetVo rewardSchemeDetVo = saleYesList.get(0);

                                        Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C4.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                        if(b){
                                            MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                            BigDecimal amount = BigDecimal.ZERO;
                                            if(rewardSchemeDetVo.getOneCommissionAmount()!=null&&!rewardSchemeDetVo.getOneCommissionAmount().equals("")){
                                                amount = new BigDecimal(rewardSchemeDetVo.getOneCommissionAmount());
                                            }
                                            if(rewardSchemeDetVo.getOneCommissionRate()!=null&&!rewardSchemeDetVo.getOneCommissionRate().equals("")){
                                                //佣金金额 = 佣金比例* 订单明细金额
                                                amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getOneCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                            }
                                            miniAccountRewardVo.setUserId(C4.getUserId());
                                            miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                            miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                            miniAccountRewardVo.setAmount(amount);
                                            miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());

                                            miniAccountRewardVo.setOrderId(orderId+"");
                                            miniAccountRewardVo.setCommissionType(CommissionTypeEnum.TEAM.getType());

                                            miniAccountRewardList.add(miniAccountRewardVo);
                                        }
                                    }
                                }
                            }
                        }
                        //过滤分佣规则业务员分佣为否
                        List<RewardSchemeDetVo> saleNoList = teamList.stream().filter(e -> (e.getSalesmanFlag() == null)||(e.getSalesmanFlag() != null
                                && e.getSalesmanFlag() == SalesmanFlagEnum.NO.getStatus())).collect(Collectors.toList());
                        log.info("获取业务员分佣为否的奖励方案明细规则saleNoList：{}",JSONObject.toJSONString(saleNoList));
                        if(saleNoList!=null&&saleNoList.size()>0){
                            List<RewardSchemeDetVo> selfYesList = saleNoList.stream().filter(e -> e.getSelfCommission() != null && e.getSelfCommission() == SelfCommissionEnum.YES.getStatus())
                                    .collect(Collectors.toList());
                            log.info("获取自身分佣的奖励方案明细规则selfYesList：{}",JSONObject.toJSONString(selfYesList));
                            if(selfYesList!=null&&selfYesList.size()>0){
                                MiniAccount C4 = orderMiniAccount;
                                log.info("自身会员信息，C4: {}", JSONObject.toJSONString(C4));
                                if(C4!=null){
                                    MemberLevelRelationParam C4Param = new MemberLevelRelationParam();
                                    C4Param.setMemberTypeId(memberTypeId);
                                    C4Param.setUserId(C4.getUserId());
                                    //查询上上级会员当前会员类型对应会员等级id
                                    String C4MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C4Param);
                                    log.info("自身会员会员C4当前会员类型对应会员等级id：{}",C4MemberLevelId);
                                    if(StringUtils.isNotEmpty(C4MemberLevelId)){
                                        //过滤收益等级
                                        selfYesList = selfYesList.stream().filter(e -> StringUtils.isEmpty(e.getMemberLevelId()) || (StringUtils.isNotEmpty(e.getMemberLevelId()) &&
                                                e.getMemberLevelId().indexOf(C4MemberLevelId) != -1)).collect(Collectors.toList());
                                        log.info("过滤会员类型等级奖励方案明细规则selfYesList：{}",JSONObject.toJSONString(selfYesList));
                                        if(selfYesList!=null&&selfYesList.size()>0){
                                            //过滤一级比例，一级金额
                                            selfYesList = selfYesList.stream().filter(e->StringUtils.isNotEmpty(e.getOneCommissionRate())||StringUtils.isNotEmpty(e.getOneCommissionAmount()))
                                                    .collect(Collectors.toList());
                                            log.info("过滤一级比例，一级金额奖励规则selfYesList：{}",JSONObject.toJSONString(selfYesList));
                                        }
                                        if(selfYesList!=null&&selfYesList.size()>0){
                                            RewardSchemeDetVo rewardSchemeDetVo = selfYesList.get(0);

                                            Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C4.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                            if(b){
                                                MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                                BigDecimal amount = BigDecimal.ZERO;
                                                if(rewardSchemeDetVo.getOneCommissionAmount()!=null&&!rewardSchemeDetVo.getOneCommissionAmount().equals("")){
                                                    amount = new BigDecimal(rewardSchemeDetVo.getOneCommissionAmount());
                                                }
                                                if(rewardSchemeDetVo.getOneCommissionRate()!=null&&!rewardSchemeDetVo.getOneCommissionRate().equals("")){
                                                    //佣金金额 = 佣金比例* 订单明细金额
                                                    amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getOneCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                                }
                                                miniAccountRewardVo.setUserId(C4.getUserId());
                                                miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                                miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                                miniAccountRewardVo.setAmount(amount);
                                                miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());

                                                miniAccountRewardVo.setOrderId(orderId+"");
                                                miniAccountRewardVo.setCommissionType(CommissionTypeEnum.TEAM.getType());

                                                miniAccountRewardList.add(miniAccountRewardVo);
                                            }
                                        }
                                    }

                                    String parentId = C4.getParentId();
                                    MiniAccount C5 = null;
                                    List<RewardSchemeDetVo>C5RewardSchemeList = null;
                                    while (StringUtils.isNotEmpty(parentId) || C4 == null){
                                        LambdaQueryWrapper<MiniAccount>parentWrapper = new LambdaQueryWrapper<>();
                                        parentWrapper.eq(MiniAccount::getUserId,parentId);
                                        //则根据下单用户查询其上级会员C1
                                        MiniAccount C1 = miniAccountService.getOne(parentWrapper);
                                        log.info("上级用户id{}对应小程序用户: {}", parentId,JSONObject.toJSONString(C1));
                                        if(C1!=null){
                                            MemberLevelRelationParam C1Param = new MemberLevelRelationParam();
                                            C1Param.setMemberTypeId(memberTypeId);
                                            C1Param.setUserId(C1.getUserId());
                                            //查询上上级会员当前会员类型对应会员等级id
                                            String C1MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C1Param);
                                            log.info("上级会员C1当前会员类型对应会员等级id：{}",C1MemberLevelId);
                                            if(StringUtils.isNotEmpty(C1MemberLevelId)){
                                                MemberLevelRuleVo C1MemberLevelRule =
                                                        memberLevelRuleService.getByMemberLevelId(Long.valueOf(C1MemberLevelId));
                                                if(C1MemberLevelRule!=null){
                                                    //过滤收益等级
                                                    List<RewardSchemeDetVo> C1SaleNoList  = saleNoList.stream().filter(e -> StringUtils.isEmpty(e.getMemberLevelId()) || (StringUtils.isNotEmpty(e.getMemberLevelId()) &&
                                                            e.getMemberLevelId().indexOf(C1MemberLevelId) != -1)).collect(Collectors.toList());
                                                    log.info("过滤会员类型等级奖励方案明细规则C1SaleNoList：{}",JSONObject.toJSONString(C1SaleNoList));
                                                    if(C1SaleNoList!=null&&C1SaleNoList.size()>0){

                                                        List<RewardSchemeDetVo>returnList = new ArrayList<>();
                                                        for (RewardSchemeDetVo rewardSchemeDetVo : C1SaleNoList) {
                                                            Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C4.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                                            if(b){
                                                                returnList.add(rewardSchemeDetVo);
                                                            }
                                                        }
                                                        if(returnList!=null&&returnList.size()>0){
                                                            C5RewardSchemeList = returnList;
                                                            C5 = C1;
                                                        }
                                                    }
                                                }
                                            }
                                            if(C5 == null){
                                                parentId = C1.getParentId();
                                            }else{
                                                parentId = "";
                                            }
                                        }else{
                                            parentId = "";
                                        }
                                    }
                                    if(C5!=null){
                                        RewardSchemeDetVo rewardSchemeDetVo = C5RewardSchemeList.get(0);
                                        MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                        BigDecimal amount = BigDecimal.ZERO;
                                        if(rewardSchemeDetVo.getOneCommissionAmount()!=null&&!rewardSchemeDetVo.getOneCommissionAmount().equals("")){
                                            amount = new BigDecimal(rewardSchemeDetVo.getOneCommissionAmount());
                                        }
                                        if(rewardSchemeDetVo.getOneCommissionRate()!=null&&!rewardSchemeDetVo.getOneCommissionRate().equals("")){
                                            //佣金金额 = 佣金比例* 订单明细金额
                                            amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getOneCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                        }
                                        miniAccountRewardVo.setUserId(C5.getUserId());
                                        miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                        miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                        miniAccountRewardVo.setAmount(amount);
                                        miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());

                                        miniAccountRewardVo.setOrderId(orderId+"");
                                        miniAccountRewardVo.setCommissionType(CommissionTypeEnum.TEAM.getType());

                                        miniAccountRewardList.add(miniAccountRewardVo);
                                    }

                                }
                            }
                            List<RewardSchemeDetVo> selfNoList = saleNoList.stream().filter(e -> (e.getSelfCommission() == null)||(e.getSelfCommission() != null && e.getSelfCommission() == SelfCommissionEnum.NO.getStatus()))
                                    .collect(Collectors.toList());
                            if(selfNoList!=null&&selfNoList.size()>0){
                                MiniAccount C4 = null;
                                if(StringUtils.isNotEmpty(orderMiniAccount.getParentId())){
                                    if(StringUtils.isNotEmpty(orderMiniAccount.getParentId())){
                                        LambdaQueryWrapper<MiniAccount>parentWrapper = new LambdaQueryWrapper<>();
                                        parentWrapper.eq(MiniAccount::getUserId,orderMiniAccount.getParentId());
                                        // 则根据下单人会员表记录对应的业务员ID查出业务员会员C4，
                                        C4 = miniAccountService.getOne(parentWrapper);
                                        log.info("上级会员信息，C4: {}", JSONObject.toJSONString(C4));
                                    }
                                }

                                if(C4!=null){
                                    MemberLevelRelationParam C4Param = new MemberLevelRelationParam();
                                    C4Param.setMemberTypeId(memberTypeId);
                                    C4Param.setUserId(C4.getUserId());
                                    //查询上上级会员当前会员类型对应会员等级id
                                    String C4MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C4Param);
                                    log.info("上级会员C4当前会员类型对应会员等级id：{}",C4MemberLevelId);
                                    if(StringUtils.isNotEmpty(C4MemberLevelId)){
                                        //过滤收益等级
                                        selfNoList = selfNoList.stream().filter(e -> StringUtils.isEmpty(e.getMemberLevelId()) || (StringUtils.isNotEmpty(e.getMemberLevelId()) &&
                                                e.getMemberLevelId().indexOf(C4MemberLevelId) != -1)).collect(Collectors.toList());
                                        log.info("过滤会员类型等级奖励方案明细规则selfNoList：{}",JSONObject.toJSONString(selfNoList));
                                        if(selfNoList!=null&&selfNoList.size()>0){
                                            //过滤一级比例，一级金额
                                            selfNoList = selfNoList.stream().filter(e->StringUtils.isNotEmpty(e.getOneCommissionAmount())||StringUtils.isNotEmpty(e.getOneCommissionRate()))
                                                    .collect(Collectors.toList());
                                            log.info("过滤一级比例，一级金额奖励规则selfNoList：{}",JSONObject.toJSONString(selfNoList));
                                        }
                                        if(selfNoList!=null&&selfNoList.size()>0){
                                            RewardSchemeDetVo rewardSchemeDetVo = selfNoList.get(0);
                                            Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C4.getUserId(), rewardSchemeDetVo.getDirectMemberCount());

                                            if(b){
                                                MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                                BigDecimal amount = BigDecimal.ZERO;
                                                if(rewardSchemeDetVo.getOneCommissionAmount()!=null&&!rewardSchemeDetVo.getOneCommissionAmount().equals("")){
                                                    amount = new BigDecimal(rewardSchemeDetVo.getOneCommissionAmount());
                                                }
                                                if(rewardSchemeDetVo.getOneCommissionRate()!=null&&!rewardSchemeDetVo.getOneCommissionRate().equals("")){
                                                    //佣金金额 = 佣金比例* 订单明细金额
                                                    amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getOneCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                                }
                                                miniAccountRewardVo.setUserId(C4.getUserId());
                                                miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                                miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                                miniAccountRewardVo.setAmount(amount);
                                                miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());

                                                miniAccountRewardVo.setOrderId(orderId+"");
                                                miniAccountRewardVo.setCommissionType(CommissionTypeEnum.TEAM.getType());

                                                miniAccountRewardList.add(miniAccountRewardVo);
                                            }

                                        }
                                    }

                                    String parentId = C4.getParentId();
                                    MiniAccount C5 = null;
                                    List<RewardSchemeDetVo>C5RewardSchemeList = null;
                                    while (StringUtils.isNotEmpty(parentId) || C4 == null){
                                        LambdaQueryWrapper<MiniAccount>parentWrapper = new LambdaQueryWrapper<>();
                                        parentWrapper.eq(MiniAccount::getUserId,parentId);
                                        //则根据下单用户查询其上级会员C1
                                        MiniAccount C1 = miniAccountService.getOne(parentWrapper);
                                        log.info("上级用户id{}对应小程序用户: {}", parentId,JSONObject.toJSONString(C1));
                                        if(C1!=null){
                                            MemberLevelRelationParam C1Param = new MemberLevelRelationParam();
                                            C1Param.setMemberTypeId(memberTypeId);
                                            C1Param.setUserId(C1.getUserId());
                                            //查询上上级会员当前会员类型对应会员等级id
                                            String C1MemberLevelId = memberLevelRelationService.getMemberLevelIdNotMemberLevelId(C1Param);
                                            log.info("上级会员C1当前会员类型对应会员等级id：{}",C1MemberLevelId);
                                            if(StringUtils.isNotEmpty(C1MemberLevelId)){
                                                MemberLevelRuleVo C1MemberLevelRule =
                                                        memberLevelRuleService.getByMemberLevelId(Long.valueOf(C1MemberLevelId));
                                                if(C1MemberLevelRule!=null){
                                                    //过滤收益等级
                                                    List<RewardSchemeDetVo> C1SaleNoList  = saleNoList.stream().filter(e -> StringUtils.isEmpty(e.getMemberLevelId()) || (StringUtils.isNotEmpty(e.getMemberLevelId()) &&
                                                            e.getMemberLevelId().indexOf(C1MemberLevelId) != -1)).collect(Collectors.toList());
                                                    log.info("过滤会员类型等级奖励方案明细规则C1SaleNoList：{}",JSONObject.toJSONString(C1SaleNoList));
                                                    if(C1SaleNoList!=null&&C1SaleNoList.size()>0){

                                                        List<RewardSchemeDetVo>returnList = new ArrayList<>();
                                                        for (RewardSchemeDetVo rewardSchemeDetVo : C1SaleNoList) {
                                                            Boolean b = vailDirectMemberCount(rewardSchemeDetVo.getDirectMemberLevelIds(), C4.getUserId(), rewardSchemeDetVo.getDirectMemberCount());
                                                            if(b){
                                                                returnList.add(rewardSchemeDetVo);
                                                            }
                                                        }
                                                        if(returnList!=null&&returnList.size()>0){
                                                            C5RewardSchemeList = returnList;
                                                            C5 = C1;
                                                        }
                                                    }
                                                }
                                            }
                                            if(C5 == null){
                                                parentId = C1.getParentId();
                                            }else{
                                                parentId = "";
                                            }
                                        }else{
                                            parentId = "";
                                        }
                                    }
                                    if(C5!=null){
                                        RewardSchemeDetVo rewardSchemeDetVo = C5RewardSchemeList.get(0);
                                        MiniAccountRewardVo miniAccountRewardVo = new MiniAccountRewardVo();
                                        BigDecimal amount = BigDecimal.ZERO;
                                        if(rewardSchemeDetVo.getOneCommissionAmount()!=null&&!rewardSchemeDetVo.getOneCommissionAmount().equals("")){
                                            amount = new BigDecimal(rewardSchemeDetVo.getOneCommissionAmount());
                                        }
                                        if(rewardSchemeDetVo.getOneCommissionRate()!=null&&!rewardSchemeDetVo.getOneCommissionRate().equals("")){
                                            //佣金金额 = 佣金比例* 订单明细金额
                                            amount = money.multiply(new BigDecimal(rewardSchemeDetVo.getOneCommissionRate()).divide(new BigDecimal(100), 4, BigDecimal.ROUND_HALF_UP));
                                        }
                                        miniAccountRewardVo.setUserId(C5.getUserId());
                                        miniAccountRewardVo.setRewardId(rewardSchemeDetVo.getRewardId()+"");
                                        miniAccountRewardVo.setRewardDetId(rewardSchemeDetVo.getId()+"");
                                        miniAccountRewardVo.setAmount(amount);
                                        miniAccountRewardVo.setRemark(rewardSchemeDetVo.getCommissionTitle());

                                        miniAccountRewardVo.setOrderId(orderId+"");
                                        miniAccountRewardVo.setCommissionType(CommissionTypeEnum.TEAM.getType());

                                        miniAccountRewardList.add(miniAccountRewardVo);
                                    }

                                }
                            }
                        }
                    }
                }
            }
        }
    }



    /**
     * 处理消费额升级
     * @param memberLevelRuleMessage 升级会员类型-升级规则
     * @param miniAccount 当前用户
     * @param amount 会员消费额
     */
    private void handleAmountUpgrade(MemberLevelRuleMessageVo memberLevelRuleMessage,MiniAccount miniAccount,BigDecimal amount){

        List<MemberLevelRuleVo> rules = memberLevelRuleMessage.getRules();

        Long memberTypeId = memberLevelRuleMessage.getMemberTypeId();
        //判断会员类型
        MemberType memberType = memberTypeService.getById(memberTypeId);
        if(rules!=null&&rules.size()>0){
            for (MemberLevelRuleVo upgradeMemberLevelRule : rules) {
                //订单升级会员等级id
                Long upgradeMemberLeverId = upgradeMemberLevelRule.getMemberLevelId();
                log.info("订单升级会员等级{}对应会员规则信息，upgradeMemberLevelRule: {}", upgradeMemberLeverId,JSONObject.toJSONString(upgradeMemberLevelRule));
                if(upgradeMemberLevelRule!=null){
                    //判断是否存在前置最低会员等级
                    //是否满足升级
                    Boolean upgradeFlag = false;
                    //判断是否存在前置最低会员等级
                    if(StringUtils.isNotEmpty(upgradeMemberLevelRule.getPreLowMemberLevelId())){
                        upgradeFlag = checkUpgradePreLow(upgradeMemberLevelRule, miniAccount);
                    }else{
                        upgradeFlag = true;
                    }
                    if(upgradeFlag){
                        if(amount.compareTo(upgradeMemberLevelRule.getAmountStart())>=0){
                            MemberLevelRelationParam memberLevelRelationParam = new MemberLevelRelationParam();
                            memberLevelRelationParam.setUserId(miniAccount.getUserId());
                            memberLevelRelationParam.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                            List<MemberLevelRelation> upgradeMemberLevelRelationList = memberLevelRelationService.getMemberLevelRelation(memberLevelRelationParam);
                            if(upgradeMemberLevelRelationList!=null&&upgradeMemberLevelRelationList.size()>0){
                                MemberLevelRelation upgradeMemberLevelRelation = upgradeMemberLevelRelationList.get(0);
                                MemberLevelRuleVo memberLevelRule =
                                        memberLevelRuleService.getByMemberLevelId(Long.valueOf(upgradeMemberLevelRelation.getMemberLevelId()));
                                if(memberLevelRule!=null&&upgradeMemberLevelRule.getSort()>memberLevelRule.getSort()){
                                    if(memberType.getRegionFlag() == RegionFlagEnum.NO.getStatus()){
                                        //非区域类型直接升级并且发送升级会员等级消息

                                        if(StringUtils.isNotEmpty(upgradeMemberLevelRelation.getMemberLevelId())){
                                            MemberLevel oldMemberLevel = memberLevelService.getById(upgradeMemberLevelRelation.getMemberLevelId());
                                            MemberLevel upgradeMemberLevel = memberLevelService.getById(upgradeMemberLeverId);
                                            if(oldMemberLevel.getMemberFlag() == MemberFlagEnum.NO.getStatus()&&
                                                    upgradeMemberLevel.getMemberFlag() == MemberFlagEnum.YES.getStatus()){
                                                upgradeMemberLevelRelation.setUpLevelTime(LocalDateTime.now());
                                            }
                                        }

                                        upgradeMemberLevelRelation.setMemberLevelId(upgradeMemberLeverId+"");
                                        log.info("更新会员等级关系，memberLevelRelation: {}", JSONObject.toJSONString(upgradeMemberLevelRelation));
                                        memberLevelRelationService.updateById(upgradeMemberLevelRelation);

                                        UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                                        upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                                        upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                                        upgradeMemberLevelMessage.setUserId(Long.valueOf(miniAccount.getUserId()));
                                        //发送升级会员等级消息
                                        sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                                        // 赠送抽奖机会
                                        PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                                        BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                                        prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                                        sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                                    }else{
                                        //区域类型需要审核
                                        upgradeMemberLevelRelation.setApplyMemberLevelId(upgradeMemberLeverId+"");
                                        memberLevelRelationService.updateById(upgradeMemberLevelRelation);
                                    }
                                    break;
                                }
                            }else{
                                MemberLevelRelation newMemberLevelRelation = new MemberLevelRelation();
                                newMemberLevelRelation.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                                newMemberLevelRelation.setUserId(miniAccount.getUserId());
                                if(memberType.getRegionFlag() == RegionFlagEnum.NO.getStatus()){
                                    //非区域类型直接升级并且发送升级会员等级消息
                                    newMemberLevelRelation.setMemberLevelId(upgradeMemberLeverId+"");
                                    newMemberLevelRelation.setUpLevelTime(LocalDateTime.now());

                                    log.info("新增会员等级关系，upgradeMemberLevelRelation: {}", JSONObject.toJSONString(newMemberLevelRelation));
                                    memberLevelRelationService.save(newMemberLevelRelation);

                                    UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                                    upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                                    upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                                    upgradeMemberLevelMessage.setUserId(Long.valueOf(miniAccount.getUserId()));
                                    //发送升级会员等级消息
                                    sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                                    // 赠送抽奖机会
                                    PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                                    BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                                    prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                                    sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                                }else{
                                    //区域类型需要审核
                                    newMemberLevelRelation.setApplyMemberLevelId(upgradeMemberLeverId+"");
                                    log.info("新增会员等级关系，upgradeMemberLevelRelation: {}", JSONObject.toJSONString(newMemberLevelRelation));
                                    memberLevelRelationService.save(newMemberLevelRelation);
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 处理商品消费额升级
     * @param memberLevelRuleMessage 升级会员类型-升级规则
     * @param miniAccount 当前用户
     * @param amount 会员消费额
     */
    private void handleMemberAmountUpgrade(MemberLevelRuleMessageVo memberLevelRuleMessage,MiniAccount miniAccount,BigDecimal amount){

        List<MemberLevelRuleVo> rules = memberLevelRuleMessage.getRules();

        //判断会员类型
        MemberType memberType = memberTypeService.getById(memberLevelRuleMessage.getMemberTypeId());

        if(rules!=null&&rules.size()>0){
            for (MemberLevelRuleVo upgradeMemberLevelRule : rules) {
                //订单升级会员等级id
                Long upgradeMemberLeverId = upgradeMemberLevelRule.getMemberLevelId();
                log.info("订单升级会员等级{}对应会员规则信息，upgradeMemberLevelRule: {}", upgradeMemberLeverId,JSONObject.toJSONString(upgradeMemberLevelRule));
                if(upgradeMemberLevelRule!=null){
                    //判断是否存在前置最低会员等级
                    //是否满足升级
                    Boolean upgradeFlag = false;
                    //判断是否存在前置最低会员等级
                    if(StringUtils.isNotEmpty(upgradeMemberLevelRule.getPreLowMemberLevelId())){
                        upgradeFlag = checkUpgradePreLow(upgradeMemberLevelRule, miniAccount);
                    }else{
                        upgradeFlag = true;
                    }
                    if(upgradeFlag){
                        if(amount.compareTo(upgradeMemberLevelRule.getMemberAmountStart())>=0){
                            MemberLevelRelationParam memberLevelRelationParam = new MemberLevelRelationParam();
                            memberLevelRelationParam.setUserId(miniAccount.getUserId());
                            memberLevelRelationParam.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                            List<MemberLevelRelation> upgradeMemberLevelRelationList = memberLevelRelationService.getMemberLevelRelation(memberLevelRelationParam);
                            if(upgradeMemberLevelRelationList!=null&&upgradeMemberLevelRelationList.size()>0){
                                MemberLevelRelation upgradeMemberLevelRelation = upgradeMemberLevelRelationList.get(0);
                                MemberLevelRuleVo memberLevelRule =
                                        memberLevelRuleService.getByMemberLevelId(Long.valueOf(upgradeMemberLevelRelation.getMemberLevelId()));
                                if(memberLevelRule!=null&&upgradeMemberLevelRule.getSort()>memberLevelRule.getSort()){

                                    if(memberType.getRegionFlag() == RegionFlagEnum.NO.getStatus()){
                                        //非区域类型直接升级并且发送升级会员等级消息

                                        if(StringUtils.isNotEmpty(upgradeMemberLevelRelation.getMemberLevelId())){
                                            MemberLevel oldMemberLevel = memberLevelService.getById(upgradeMemberLevelRelation.getMemberLevelId());
                                            MemberLevel upgradeMemberLevel = memberLevelService.getById(upgradeMemberLeverId);
                                            if(oldMemberLevel.getMemberFlag() == MemberFlagEnum.NO.getStatus()&&
                                                    upgradeMemberLevel.getMemberFlag() == MemberFlagEnum.YES.getStatus()){
                                                upgradeMemberLevelRelation.setUpLevelTime(LocalDateTime.now());
                                            }
                                        }


                                        upgradeMemberLevelRelation.setMemberLevelId(upgradeMemberLeverId+"");
                                        log.info("更新会员等级关系，memberLevelRelation: {}", JSONObject.toJSONString(upgradeMemberLevelRelation));
                                        memberLevelRelationService.updateById(upgradeMemberLevelRelation);

                                        UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                                        upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                                        upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                                        upgradeMemberLevelMessage.setUserId(Long.valueOf(miniAccount.getUserId()));
                                        //发送升级会员等级消息
                                        sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                                        // 赠送抽奖机会
                                        PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                                        BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                                        prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                                        sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                                    }else{
                                        //区域类型需要审核
                                        upgradeMemberLevelRelation.setApplyMemberLevelId(upgradeMemberLeverId+"");
                                        memberLevelRelationService.updateById(upgradeMemberLevelRelation);
                                    }
                                    break;
                                }
                            }else{
                                MemberLevelRelation newMemberLevelRelation = new MemberLevelRelation();
                                newMemberLevelRelation.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                                newMemberLevelRelation.setUserId(miniAccount.getUserId());
                                if(memberType.getRegionFlag() == RegionFlagEnum.NO.getStatus()){
                                    //非区域类型直接升级并且发送升级会员等级消息
                                    newMemberLevelRelation.setMemberLevelId(upgradeMemberLeverId+"");
                                    newMemberLevelRelation.setUpLevelTime(LocalDateTime.now());
                                    log.info("新增会员等级关系，upgradeMemberLevelRelation: {}", JSONObject.toJSONString(newMemberLevelRelation));
                                    memberLevelRelationService.save(newMemberLevelRelation);

                                    UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                                    upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                                    upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                                    upgradeMemberLevelMessage.setUserId(Long.valueOf(miniAccount.getUserId()));
                                    //发送升级会员等级消息
                                    sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                                    // 赠送抽奖机会
                                    PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                                    BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                                    prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                                    sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                                }else{
                                    //区域类型需要审核
                                    newMemberLevelRelation.setApplyMemberLevelId(upgradeMemberLeverId+"");
                                    log.info("新增会员等级关系，upgradeMemberLevelRelation: {}", JSONObject.toJSONString(newMemberLevelRelation));
                                    memberLevelRelationService.save(newMemberLevelRelation);
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 处理指定商品升级
     * @param memberLevelRuleMessage 升级会员类型-升级规则
     * @param miniAccount 当前用户
     * @param upgradeMemberLeverId 会员等级升级id
     */
    private void handleAppointGoods(MemberLevelRuleMessageVo memberLevelRuleMessage,MiniAccount miniAccount,Long upgradeMemberLeverId){

        MemberLevelRuleVo upgradeMemberLevelRule = memberLevelRuleService.getByMemberLevelId(upgradeMemberLeverId);

        //判断会员类型
        MemberType memberType = memberTypeService.getById(memberLevelRuleMessage.getMemberTypeId());

        log.info("订单升级会员等级{}对应会员规则信息，upgradeMemberLevelRule: {}", upgradeMemberLeverId,JSONObject.toJSONString(upgradeMemberLevelRule));
        if(upgradeMemberLevelRule!=null){
            //是否满足升级
            Boolean upgradeFlag = false;
            //判断是否存在前置最低会员等级
            if(StringUtils.isNotEmpty(upgradeMemberLevelRule.getPreLowMemberLevelId())){
                upgradeFlag = checkUpgradePreLow(upgradeMemberLevelRule, miniAccount);
            }else{
                upgradeFlag = true;
            }
            if(upgradeFlag){
                MemberLevelRelationParam memberLevelRelationParam = new MemberLevelRelationParam();
                memberLevelRelationParam.setUserId(miniAccount.getUserId());
                memberLevelRelationParam.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                List<MemberLevelRelation> upgradeMemberLevelRelationList = memberLevelRelationService.getMemberLevelRelation(memberLevelRelationParam);
                if(upgradeMemberLevelRelationList!=null&&upgradeMemberLevelRelationList.size()>0){
                    MemberLevelRelation upgradeMemberLevelRelation = upgradeMemberLevelRelationList.get(0);
                    MemberLevelRuleVo memberLevelRule =
                            memberLevelRuleService.getByMemberLevelId(Long.valueOf(upgradeMemberLevelRelation.getMemberLevelId()));
                    if(memberLevelRule!=null&&upgradeMemberLevelRule.getSort()>memberLevelRule.getSort()){
                        if(memberType.getRegionFlag() == RegionFlagEnum.NO.getStatus()){
                            //非区域类型直接升级并且发送升级会员等级消息


                            if(StringUtils.isNotEmpty(upgradeMemberLevelRelation.getMemberLevelId())){

                                MemberLevel oldMemberLevel = memberLevelService.getById(upgradeMemberLevelRelation.getMemberLevelId());
                                MemberLevel upgradeMemberLevel = memberLevelService.getById(upgradeMemberLeverId);
                                if(oldMemberLevel.getMemberFlag() == MemberFlagEnum.NO.getStatus()&&
                                        upgradeMemberLevel.getMemberFlag() == MemberFlagEnum.YES.getStatus()){
                                    upgradeMemberLevelRelation.setUpLevelTime(LocalDateTime.now());
                                }
                            }


                            upgradeMemberLevelRelation.setMemberLevelId(upgradeMemberLeverId+"");
                            log.info("更新会员等级关系，memberLevelRelation: {}", JSONObject.toJSONString(upgradeMemberLevelRelation));
                            memberLevelRelationService.updateById(upgradeMemberLevelRelation);

                            UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                            upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                            upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                            upgradeMemberLevelMessage.setUserId(Long.valueOf(miniAccount.getUserId()));
                            //发送升级会员等级消息
                            sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                            // 赠送抽奖机会
                            PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                            BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                            prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                            sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                        }else{
                            //区域类型需要审核
                            upgradeMemberLevelRelation.setApplyMemberLevelId(upgradeMemberLeverId+"");
                            memberLevelRelationService.updateById(upgradeMemberLevelRelation);
                        }
                    }
                }else{
                    MemberLevelRelation newMemberLevelRelation = new MemberLevelRelation();
                    newMemberLevelRelation.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                    newMemberLevelRelation.setUserId(miniAccount.getUserId());
                    if(memberType.getRegionFlag() == RegionFlagEnum.NO.getStatus()){
                        //非区域类型直接升级并且发送升级会员等级消息
                        newMemberLevelRelation.setMemberLevelId(upgradeMemberLeverId+"");
                        newMemberLevelRelation.setUpLevelTime(LocalDateTime.now());
                        log.info("新增会员等级关系，upgradeMemberLevelRelation: {}", JSONObject.toJSONString(newMemberLevelRelation));
                        memberLevelRelationService.save(newMemberLevelRelation);

                        UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                        upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                        upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                        upgradeMemberLevelMessage.setUserId(Long.valueOf(miniAccount.getUserId()));
                        //发送升级会员等级消息
                        sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                        // 赠送抽奖机会
                        PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                        BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                        prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                        sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                    }else{
                        //区域类型需要审核
                        newMemberLevelRelation.setApplyMemberLevelId(upgradeMemberLeverId+"");
                        log.info("新增会员等级关系，upgradeMemberLevelRelation: {}", JSONObject.toJSONString(newMemberLevelRelation));
                        memberLevelRelationService.save(newMemberLevelRelation);
                    }
                }
            }
        }
    }

    /**
     * 处理团队业绩升级
     * @param memberLevelRuleMessage 升级会员类型-升级规则
     * @param miniAccount 当前用户
     */
    private void handleTeamPerformance(MemberLevelRuleMessageVo memberLevelRuleMessage,MiniAccount miniAccount){
        List<MemberLevelRuleVo> rules = memberLevelRuleMessage.getRules();

        //判断会员类型
        MemberType memberType = memberTypeService.getById(memberLevelRuleMessage.getMemberTypeId());
        //非区域类型
        if(memberType.getRegionFlag() == RegionFlagEnum.NO.getStatus()){
            if(rules!=null&&rules.size()>0){
                //获取当前订单用户上级
                String parentId = miniAccount.getParentId();
                //获取上级用户下的下级，下下级做为一个团队，判断是否升级上级用户
                if(StringUtils.isNotEmpty(parentId)){
                    List<String>teamShopUserIds = miniAccountExtendsService.getTeamShopUserIds(parentId);
                    if(teamShopUserIds!=null&&teamShopUserIds.size()>0){
                        for (MemberLevelRuleVo upgradeMemberLevelRule : rules) {
                            //订单升级会员等级id
                            Long upgradeMemberLeverId = upgradeMemberLevelRule.getMemberLevelId();
                            log.info("订单升级会员等级{}对应会员规则信息，upgradeMemberLevelRule: {}", upgradeMemberLeverId,JSONObject.toJSONString(upgradeMemberLevelRule));
                            if(upgradeMemberLevelRule!=null){
                                //是否满足升级
                                Boolean upgradeFlag = false;
                                //判断是否存在前置最低会员等级
                                if(StringUtils.isNotEmpty(upgradeMemberLevelRule.getPreLowMemberLevelId())){
                                    upgradeFlag = checkUpgradePreLow(upgradeMemberLevelRule, miniAccount);
                                }else{
                                    upgradeFlag = true;
                                }
                                if(upgradeFlag){
                                    Integer teamTimes = upgradeMemberLevelRule.getTeamTimes();
                                    BigDecimal allAmount = BigDecimal.ZERO;
                                    if(teamTimes!=null){
                                        BigDecimal sumAmount = miniAccountMemberUpService.getSumAmount(teamShopUserIds);
                                        allAmount = allAmount.add(sumAmount);
                                    }else{
                                        List<MemberLevelRuleProductVo> productList = memberLevelRuleMessage.getProducts();
                                        List<String>skuIds = new ArrayList<>();
                                        if(productList!=null&&productList.size()>0){
                                            for (MemberLevelRuleProductVo memberLevelRuleProductVo : productList) {
                                                String skuId = memberLevelRuleProductVo.getSkuId();
                                                skuIds.add(skuId);
                                            }
                                        }
                                        // 获取当前日期
                                        LocalDate today = LocalDate.now();
                                        // 计算团队业绩周期
                                        LocalDate teamTimesLocalDate = today.minus(teamTimes, ChronoUnit.MONTHS);

                                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                                        String startDate = teamTimesLocalDate.format(formatter2) + " 00:00:00";
                                        String endDate = today.format(formatter);

                                        BigDecimal sumAmount = remoteOrderService.getSumAmount(startDate,endDate,skuIds,teamShopUserIds);
                                        allAmount = allAmount.add(sumAmount);
                                    }
                                    BigDecimal teamAmountStart = upgradeMemberLevelRule.getTeamAmountStart();
                                    if(allAmount.compareTo(teamAmountStart)>=0){
                                        MemberLevelRelationParam memberLevelRelationParam = new MemberLevelRelationParam();
                                        memberLevelRelationParam.setUserId(parentId);
                                        memberLevelRelationParam.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                                        List<MemberLevelRelation> parentUpgradeMemberLevelRelationList = memberLevelRelationService.getMemberLevelRelation(memberLevelRelationParam);
                                        if(parentUpgradeMemberLevelRelationList!=null&&parentUpgradeMemberLevelRelationList.size()>0){
                                            MemberLevelRelation memberLevelRelation = parentUpgradeMemberLevelRelationList.get(0);
                                            MemberLevelRuleVo memberLevelRule =
                                                    memberLevelRuleService.getByMemberLevelId(Long.valueOf(memberLevelRelation.getMemberLevelId()));
                                            if(memberLevelRule!=null&&upgradeMemberLevelRule.getSort()>memberLevelRule.getSort()){

                                                if(StringUtils.isNotEmpty(memberLevelRelation.getMemberLevelId())){
                                                    MemberLevel oldMemberLevel = memberLevelService.getById(memberLevelRelation.getMemberLevelId());
                                                    MemberLevel upgradeMemberLevel = memberLevelService.getById(upgradeMemberLeverId);
                                                    if(oldMemberLevel.getMemberFlag() == MemberFlagEnum.NO.getStatus()&&
                                                            upgradeMemberLevel.getMemberFlag() == MemberFlagEnum.YES.getStatus()){
                                                        memberLevelRelation.setUpLevelTime(LocalDateTime.now());
                                                    }
                                                }


                                                memberLevelRelation.setMemberLevelId(upgradeMemberLeverId+"");
                                                log.info("更新会员等级关系，memberLevelRelation: {}", JSONObject.toJSONString(memberLevelRelation));
                                                memberLevelRelationService.updateById(memberLevelRelation);

                                                UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                                                upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                                                upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                                                upgradeMemberLevelMessage.setUserId(Long.valueOf(miniAccount.getUserId()));
                                                //发送升级会员等级消息
                                                sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                                                // 赠送抽奖机会
                                                PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                                                BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                                                prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                                                sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                                                break;
                                            }
                                        }else{
                                            MemberLevelRelation newMemberLevelRelation = new MemberLevelRelation();
                                            newMemberLevelRelation.setMemberLevelId(upgradeMemberLevelRule.getMemberLevelId()+"");
                                            newMemberLevelRelation.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                                            newMemberLevelRelation.setUserId(parentId);
                                            newMemberLevelRelation.setUpLevelTime(LocalDateTime.now());
                                            log.info("新增会员等级关系，newMemberLevelRelation: {}", JSONObject.toJSONString(newMemberLevelRelation));
                                            memberLevelRelationService.save(newMemberLevelRelation);
                                            UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                                            upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                                            upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                                            upgradeMemberLevelMessage.setUserId(Long.valueOf(parentId));
                                            //发送升级会员等级消息
                                            sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                                            // 赠送抽奖机会
                                            PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                                            BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                                            prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                                            sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                //获取当前订单用户上上级
                String aboveParentId = miniAccount.getAboveParentId();
                //获取上上级用户下的下级，下下级做为一个团队，判断是否升级上上级用户
                if(StringUtils.isNotEmpty(aboveParentId)){
                    List<String>teamShopUserIds = miniAccountExtendsService.getTeamShopUserIds(aboveParentId);
                    if(teamShopUserIds!=null&&teamShopUserIds.size()>0){
                        for (MemberLevelRuleVo upgradeMemberLevelRule : rules) {
                            //订单升级会员等级id
                            Long upgradeMemberLeverId = upgradeMemberLevelRule.getMemberLevelId();
                            log.info("订单升级会员等级{}对应会员规则信息，upgradeMemberLevelRule: {}", upgradeMemberLeverId,JSONObject.toJSONString(upgradeMemberLevelRule));
                            if(upgradeMemberLevelRule!=null){
                                //是否满足升级
                                Boolean upgradeFlag = false;
                                //判断是否存在前置最低会员等级
                                if(StringUtils.isNotEmpty(upgradeMemberLevelRule.getPreLowMemberLevelId())){
                                    upgradeFlag = checkUpgradePreLow(upgradeMemberLevelRule, miniAccount);
                                }else{
                                    upgradeFlag = true;
                                }

                                if(upgradeFlag){
                                    Integer teamTimes = upgradeMemberLevelRule.getTeamTimes();
                                    BigDecimal allAmount = BigDecimal.ZERO;
                                    if(teamTimes!=null){
                                        BigDecimal sumAmount = miniAccountMemberUpService.getSumAmount(teamShopUserIds);
                                        allAmount = allAmount.add(sumAmount);
                                    }else{
                                        List<MemberLevelRuleProductVo> productList = memberLevelRuleMessage.getProducts();
                                        List<String>skuIds = new ArrayList<>();
                                        if(productList!=null&&productList.size()>0){
                                            for (MemberLevelRuleProductVo memberLevelRuleProductVo : productList) {
                                                String skuId = memberLevelRuleProductVo.getSkuId();
                                                skuIds.add(skuId);
                                            }
                                        }
                                        // 获取当前日期
                                        LocalDate today = LocalDate.now();
                                        // 计算团队业绩周期
                                        LocalDate teamTimesLocalDate = today.minus(teamTimes, ChronoUnit.MONTHS);

                                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                                        String startDate = teamTimesLocalDate.format(formatter2) + " 00:00:00";
                                        String endDate = today.format(formatter);

                                        BigDecimal sumAmount = remoteOrderService.getSumAmount(startDate,endDate,skuIds,teamShopUserIds);
                                        allAmount = allAmount.add(sumAmount);
                                    }

                                    BigDecimal teamAmountStart = upgradeMemberLevelRule.getTeamAmountStart();
                                    if(allAmount.compareTo(teamAmountStart)>=0){
                                        MemberLevelRelationParam memberLevelRelationParam = new MemberLevelRelationParam();
                                        memberLevelRelationParam.setUserId(aboveParentId);
                                        memberLevelRelationParam.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                                        List<MemberLevelRelation> aboveParentUpgradeMemberLevelRelationList = memberLevelRelationService.getMemberLevelRelation(memberLevelRelationParam);
                                        if(aboveParentUpgradeMemberLevelRelationList!=null&&aboveParentUpgradeMemberLevelRelationList.size()>0){
                                            MemberLevelRelation memberLevelRelation = aboveParentUpgradeMemberLevelRelationList.get(0);
                                            MemberLevelRuleVo memberLevelRule =
                                                    memberLevelRuleService.getByMemberLevelId(Long.valueOf(memberLevelRelation.getMemberLevelId()));
                                            if(memberLevelRule!=null&&upgradeMemberLevelRule.getSort()>memberLevelRule.getSort()){

                                                if(StringUtils.isNotEmpty(memberLevelRelation.getMemberLevelId())){
                                                    MemberLevel oldMemberLevel = memberLevelService.getById(memberLevelRelation.getMemberLevelId());
                                                    MemberLevel upgradeMemberLevel = memberLevelService.getById(upgradeMemberLeverId);
                                                    if(oldMemberLevel.getMemberFlag() == MemberFlagEnum.NO.getStatus()&&
                                                            upgradeMemberLevel.getMemberFlag() == MemberFlagEnum.YES.getStatus()){
                                                        memberLevelRelation.setUpLevelTime(LocalDateTime.now());
                                                    }
                                                }

                                                memberLevelRelation.setMemberLevelId(upgradeMemberLeverId+"");

                                                log.info("更新会员等级关系，memberLevelRelation: {}", JSONObject.toJSONString(memberLevelRelation));
                                                memberLevelRelationService.updateById(memberLevelRelation);

                                                UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                                                upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                                                upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                                                upgradeMemberLevelMessage.setUserId(Long.valueOf(miniAccount.getUserId()));
                                                //发送升级会员等级消息
                                                sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                                                // 赠送抽奖机会
                                                PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                                                BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                                                prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                                                sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                                                break;
                                            }
                                        }else{
                                            MemberLevelRelation newMemberLevelRelation = new MemberLevelRelation();
                                            newMemberLevelRelation.setMemberLevelId(upgradeMemberLevelRule.getMemberLevelId()+"");
                                            newMemberLevelRelation.setMemberTypeId(memberLevelRuleMessage.getMemberTypeId());
                                            newMemberLevelRelation.setUserId(aboveParentId);
                                            newMemberLevelRelation.setUpLevelTime(LocalDateTime.now());

                                            log.info("新增会员等级关系，newMemberLevelRelation: {}", JSONObject.toJSONString(newMemberLevelRelation));
                                            memberLevelRelationService.save(newMemberLevelRelation);
                                            UpgradeMemberLevelMessage upgradeMemberLevelMessage = new UpgradeMemberLevelMessage();
                                            upgradeMemberLevelMessage.setTenantId(TenantContextHolder.getTenantId());
                                            upgradeMemberLevelMessage.setMessageType(UpgradeTypeEnum.MEMBERUP.getType());
                                            upgradeMemberLevelMessage.setUserId(Long.valueOf(aboveParentId));
                                            //发送升级会员等级消息
                                            sender.sendUpgradeMemberLevelMessage(upgradeMemberLevelMessage);
                                            // 赠送抽奖机会
                                            PrizeMemberMessage prizeMemberMessage = new PrizeMemberMessage();
                                            BeanUtils.copyProperties(upgradeMemberLevelMessage,prizeMemberMessage);
                                            prizeMemberMessage.setMemberLeverId(upgradeMemberLeverId);
                                            sender.sendPrizeMemberUpMessage(prizeMemberMessage);
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void revertAccountBalance(AccountReturnBalanceMessage message) {

        Integer cancelOrder = message.getCancelOrder();
        String userId = message.getUserId();
        String orderId = message.getOrderId();
        MiniAccountExtends miniAccountExtends = miniAccountExtendsService.findByUserId(userId);
        BigDecimal payGolden = message.getPayGolden() == null ? BigDecimal.ZERO :message.getPayGolden();
        BigDecimal payCommission = message.getPayCommission() == null ? BigDecimal.ZERO :message.getPayCommission();

        if(payGolden.add(payCommission).compareTo(BigDecimal.ZERO)>0){
            MiniAccount account = miniAccountService.getByShopUserId(miniAccountExtends.getShopUserId());
            //归还金豆
            if(payGolden.compareTo(BigDecimal.ZERO)>0){

                BigDecimal usedGolden = account.getUsedGolden() == null ? BigDecimal.ZERO : account.getUsedGolden();
                BigDecimal currentGolden = account.getCurrentGolden() == null ? BigDecimal.ZERO : account.getCurrentGolden();

                account.setUsedGolden(usedGolden.subtract(payGolden));
                account.setCurrentGolden(currentGolden.add(payGolden));

                if(cancelOrder!=null&&cancelOrder == CommonConstants.NUMBER_ONE){
                    MiniAccountGolden miniAccountGolden = new MiniAccountGolden();
                    miniAccountGolden.setCommissionType(CommissionTypeEnum.ORDER_CANCEL_COMMISSION.getType());
                    miniAccountGolden.setAmount(payGolden);
                    miniAccountGolden.setUserId(miniAccountExtends.getShopUserId());
                    miniAccountGolden.setOrderType(GoldenOrderTypeEnum.BUY_ORDER.getType());
                    miniAccountGolden.setOrderId(Long.valueOf(orderId));
                    miniAccountGolden.setSource(CommonConstants.NUMBER_ZERO);
                    miniAccountGolden.setLastGolden(currentGolden);
                    miniAccountGolden.setTotalGolden(account.getCurrentGolden());

                    miniAccountGolden.setRemark("订单取消");
                    miniAccountGoldenService.save(miniAccountGolden);
                }else{
                    LambdaQueryWrapper<MiniAccountGolden> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(MiniAccountGolden::getCommissionType,CommissionTypeEnum.ORDER_COMMISSION.getType());
                    wrapper.eq(MiniAccountGolden::getUserId,miniAccountExtends.getShopUserId());
                    wrapper.eq(MiniAccountGolden::getOrderId,orderId);
                    wrapper.eq(MiniAccountGolden::getOrderType,GoldenOrderTypeEnum.BUY_ORDER.getType());
                    List<MiniAccountGolden> list = miniAccountGoldenService.list(wrapper);
                    if(list!=null&&list.size()>0){
                        for (MiniAccountGolden miniAccountGolden : list) {
                            miniAccountGoldenService.removeById(miniAccountGolden.getId());
                        }
                    }
                }

            }
            //归还佣金
            if(payCommission.compareTo(BigDecimal.ZERO)>0){

                BigDecimal usedCommission = account.getUsedCommission() == null ? BigDecimal.ZERO :account.getUsedCommission();
                BigDecimal currentCommission = account.getCurrentCommission() == null ? BigDecimal.ZERO :account.getCurrentCommission();

                account.setUsedCommission(usedCommission.subtract(payCommission));
                account.setCurrentCommission(currentCommission.add(payCommission));

                if(cancelOrder!=null&&cancelOrder == CommonConstants.NUMBER_ONE){
                    MiniAccountCommission miniAccountCommission = new MiniAccountCommission();
                    miniAccountCommission.setCommissionType(CommissionTypeEnum.ORDER_CANCEL_COMMISSION.getType());
                    miniAccountCommission.setAmount(payCommission);
                    miniAccountCommission.setUserId(miniAccountExtends.getShopUserId());
                    miniAccountCommission.setOrderId(Long.valueOf(orderId));
                    miniAccountCommission.setLastCommission(currentCommission);
                    miniAccountCommission.setTotalCommission(account.getCurrentCommission());
                    miniAccountCommission.setSource(CommonConstants.NUMBER_ZERO);
                    miniAccountCommission.setRemark("订单取消");
                    miniAccountCommissionService.save(miniAccountCommission);
                }else{
                    LambdaQueryWrapper<MiniAccountCommission>wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(MiniAccountCommission::getCommissionType,CommissionTypeEnum.ORDER_COMMISSION.getType());
                    wrapper.eq(MiniAccountCommission::getUserId,miniAccountExtends.getShopUserId());
                    wrapper.eq(MiniAccountCommission::getOrderId,orderId);
                    List<MiniAccountCommission> list = miniAccountCommissionService.list(wrapper);
                    if(list!=null&&list.size()>0){
                        for (MiniAccountCommission miniAccountCommission : list) {
                            miniAccountCommissionService.removeById(miniAccountCommission.getId());
                        }
                    }
                }
            }
            this.updateById(account);
        }
    }

    /**
     * 判断直推会员数
     * @param directMemberLevelIds
     * @param userId
     * @param directMemberCount
     * @return
     */
    private Boolean vailDirectMemberCount(String directMemberLevelIds,String userId,Integer directMemberCount){
        Integer count = miniAccountService.getDirectMemberCountByMemberTypeIds(directMemberLevelIds,userId);
        Boolean result = false;
        if(directMemberCount==null ||(directMemberCount != null && count >= directMemberCount)){
            result = true;
        }
        return result;
    }
}
