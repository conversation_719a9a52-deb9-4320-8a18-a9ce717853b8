package com.medusa.gruul.account.model.param;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: 我的团队成员参数
 * @Date: Created in 11:11 2023/8/30
 */
@ApiModel(value = "MyTeamMiniAccountParam 实体", description = "我的团队成员 param")
@Data
public class MyTeamMiniAccountParam extends QueryParam {

    /**
     * 团队类型：1：一级；2：二级
     */
    @ApiModelProperty(value = "团队类型：1：一级；2：二级")
    @NotNull(message = "团队类型不能为空")
    private Integer type;

    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    private String name;


    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String phone;

    /**
     * 会员等级
     */
    @ApiModelProperty(value = "会员等级")
    private String memberLevel;


    /**
     * 加入时间排序:1：升序；2：倒序
     */
    @ApiModelProperty(value = "加入时间排序:1：升序；2：倒序")
    private Integer createTimeSort;

    /**
     * 佣金排序:1：升序；2：倒序
     */
    @ApiModelProperty(value = "佣金排序:1：升序；2：倒序")
    private Integer amountSort;

    /**
     * 会员等级排序:1：升序；2：倒序
     */
    @ApiModelProperty(value = "会员等级排序:1：升序；2：倒序")
    private Integer levelSort;

    /**
     * 用户id
     */
    private String userId;
}
