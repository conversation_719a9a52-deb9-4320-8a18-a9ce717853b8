package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccountCouponCode;
import com.medusa.gruul.account.api.model.param.MiniAccountCouponCodeParam;
import com.medusa.gruul.account.api.model.vo.ShopCouponCodeVo;
import com.medusa.gruul.account.api.model.vo.ShopsCouponCodeExcelVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MiniAccountCouponCodeMapper extends BaseMapper<MiniAccountCouponCode> {

    /**
     * 查询商家自己的核销记录
     * @param page
     * @param param
     * @return
     */
    IPage<ShopCouponCodeVo> selectShopUserVerifyList(Page page,@Param("param")MiniAccountCouponCodeParam param);

    /**
     * 查询商家核销记录
     * @param page
     * @param miniAccountCouponCodeParam
     * @return
     */
    IPage<ShopCouponCodeVo> selectShopVerifyList(Page page, @Param("param")MiniAccountCouponCodeParam miniAccountCouponCodeParam);

    /**
     * 查询导出商家核销记录
     * @param page
     * @param param
     * @return
     */
    IPage<ShopsCouponCodeExcelVo> selectShopVerifyExcelList(Page page, @Param("param")MiniAccountCouponCodeParam param);
}
