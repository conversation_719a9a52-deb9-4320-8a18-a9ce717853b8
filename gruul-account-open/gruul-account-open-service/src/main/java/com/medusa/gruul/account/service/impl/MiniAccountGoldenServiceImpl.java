package com.medusa.gruul.account.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccount;
import com.medusa.gruul.account.api.entity.MiniAccountCommission;
import com.medusa.gruul.account.api.entity.MiniAccountGolden;
import com.medusa.gruul.account.api.enums.CommissionTypeEnum;
import com.medusa.gruul.account.mapper.MiniAccountGoldenMapper;
import com.medusa.gruul.account.model.dto.MiniAccountGoldenDto;
import com.medusa.gruul.account.model.param.MiniAccountGoldenManageParam;
import com.medusa.gruul.account.model.param.MiniAccountGoldenParam;
import com.medusa.gruul.account.model.vo.*;
import com.medusa.gruul.account.service.IMiniAccountGoldenService;
import com.medusa.gruul.account.service.IMiniAccountService;
import com.medusa.gruul.common.core.util.CurUserUtil;
import com.medusa.gruul.common.core.util.HuToolExcelUtils;
import com.medusa.gruul.common.core.util.PageUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 11:25 2025/6/11
 */
@Service
public class MiniAccountGoldenServiceImpl extends ServiceImpl<MiniAccountGoldenMapper, MiniAccountGolden>implements IMiniAccountGoldenService {
    @Autowired
    private IMiniAccountService miniAccountService;

    @Override
    @Transactional
    public void addMiniAccountGolden(MiniAccountGoldenDto dto) {
        MiniAccountGolden accountGolden = new MiniAccountGolden();
        BeanUtil.copyProperties(dto, accountGolden);

        //修改个人金豆
        MiniAccount account = miniAccountService.getByShopUserId(dto.getUserId());
        BigDecimal golden = account.getGolden() == null ? BigDecimal.ZERO : account.getGolden();
        BigDecimal currentGolden = account.getCurrentGolden() ==null ? BigDecimal.ZERO : account.getCurrentGolden();

        //团队金豆-总金豆
        account.setGolden(golden.add(accountGolden.getAmount()));
        account.setCurrentGolden(currentGolden.add(accountGolden.getAmount()));

        accountGolden.setLastGolden(currentGolden);
        accountGolden.setTotalGolden(account.getCurrentGolden());

        this.save(accountGolden);

        LambdaUpdateWrapper<MiniAccount> userUpdateWrapper = new LambdaUpdateWrapper<>();
        userUpdateWrapper.set(MiniAccount::getGolden, account.getGolden());
        userUpdateWrapper.set(MiniAccount::getCurrentGolden, account.getCurrentGolden());
        userUpdateWrapper.eq(MiniAccount::getId, account.getId());
        this.miniAccountService.update(null, userUpdateWrapper);

    }

    @Override
    public UserGoldenVo getMiniAccountGoldenVo() {
        // 非shop_user_id，是miniAccount的user_id
        String userId = CurUserUtil.getMiniReqeustAccountInfo().getUserId();
        return this.baseMapper.getUserGoldenVo(userId);
    }

    @Override
    public IPage<MiniAccountGoldenVo> pageMyGolden(MiniAccountGoldenParam param) {
        String userId = CurUserUtil.getHttpCurUser().getUserId();

        param.setShopUserId(userId);

        if(StringUtils.isNotEmpty(param.getStartTime())){
            param.setStartTime(param.getStartTime()+" 00:00:00");
        }
        if(StringUtils.isNotEmpty(param.getEndTime())){
            param.setEndTime(param.getEndTime()+" 23:59:59");
        }
        IPage<MiniAccountGoldenVo> page = this.baseMapper.pageMyGolden(new Page(param.getCurrent(),param.getSize()), param);
        return page;
    }

    @Override
    public PageUtils<MiniAccountGoldenManageVo> searchMiniAccountGoldenDet(MiniAccountGoldenManageParam param) {
        IPage<MiniAccountGoldenManageVo> page = this.baseMapper.searchMiniAccountGoldenDet(new Page<>(param.getCurrent(), param.getSize()), param);
        return new PageUtils<>(page);
    }

    @Override
    public void exportMiniAccountGoldenManage(MiniAccountGoldenManageParam param) {
        // 设置导出最大限制
        HuToolExcelUtils.exportParamToMax(param);
        PageUtils<MiniAccountGoldenManageVo> pageUtils = searchMiniAccountGoldenDet(param);
        List<MiniAccountGoldenManageVo> dataList = pageUtils.getList();
        // 使用Lambda转换方式导出Excel
        HuToolExcelUtils.exportData(dataList, "佣金明细", item -> new MiniAccountGoldenManageExcelVo());
    }
}
