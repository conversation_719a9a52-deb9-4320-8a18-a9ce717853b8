package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.account.api.entity.MemberLevelRuleProduct;
import com.medusa.gruul.account.model.vo.MemberLevelRuleProductVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:24 2025/3/18
 */
@Mapper
public interface MemberLevelRuleProductMapper extends BaseMapper<MemberLevelRuleProduct> {

    /**
     * 获取会员规则商品
     * @return
     */
    List<MemberLevelRuleProductVo> getMemberLevelRuleProduct(@Param("memberTypeId") Long memberTypeId);


}
