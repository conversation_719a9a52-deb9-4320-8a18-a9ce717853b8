package com.medusa.gruul.account.service.impl;

import com.medusa.gruul.account.api.entity.MemberLevelRightsRelation;
import com.medusa.gruul.account.mapper.MemberLevelRightsRelationMapper;

import com.medusa.gruul.account.service.IMemberLevelRightsRelationService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 会员等级、权益关联表
 * @Author: jeecg-boot
 * @Date:   2022-02-23
 * @Version: V1.0
 */
@Service
public class MemberLevelRightsRelationServiceImpl extends ServiceImpl<MemberLevelRightsRelationMapper, MemberLevelRightsRelation> implements IMemberLevelRightsRelationService {


    @Override
    public List<MemberLevelRightsRelation> selectMemberPowerList(List<String> memberLevelIdList) {
        return this.baseMapper.selectMemberPowerList(memberLevelIdList);
    }

    @Override
    public void deleteMemberRights(String memberLevelId) {
        this.baseMapper.deleteMemberRights(memberLevelId);
    }
}
