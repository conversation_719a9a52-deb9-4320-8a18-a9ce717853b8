package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountApplyRegionMember;
import com.medusa.gruul.account.model.dto.AuditDataDto;
import com.medusa.gruul.account.model.param.MiniAccountApplyRegionMemberParam;
import com.medusa.gruul.account.model.vo.MiniAccountApplyRegionMemberVo;
import com.medusa.gruul.common.core.util.PageUtils;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:12 2025/6/27
 */
public interface IMiniAccountApplyRegionMemberService extends IService<MiniAccountApplyRegionMember> {
    /**
     * 分页查询区域会员审核列表
     * @param param
     * @return
     */
    PageUtils<MiniAccountApplyRegionMemberVo>page(MiniAccountApplyRegionMemberParam param);

    /**
     * 区域会员审核
     * @param dto
     */
    void miniAccountApplyRegionMemberAudit(AuditDataDto dto);
}
