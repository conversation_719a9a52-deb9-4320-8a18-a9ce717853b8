package com.medusa.gruul.account.model.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:41 2024/9/27
 */
@Data
@ApiModel(value = "ApiMiniAccountPackageOrderParam 实体", description = "小程序用户权益包管理param")
public class ApiMiniAccountPackageOrderParam extends QueryParam {
    /**
     * 状态->0.未核销；1.已核销；2.已失效
     */
    @ApiModelProperty("状态->0.未核销；1.已核销；2.已失效")
    private Integer status;

    /**
     * 小程序用户Id
     */
    @ApiModelProperty(value = "小程序用户Id")
    private String userId;
}
