package com.medusa.gruul.account.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.medusa.gruul.account.api.entity.MiniAccountPackageGoods;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageGoodsParam;
import com.medusa.gruul.account.api.model.vo.PackageGoodsShowVo;
import com.medusa.gruul.account.model.vo.ApiPackageGoodsVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageGoodsVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageOrderVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 20:27 2024/9/4
 */
@Repository
public interface MiniAccountPackageGoodsMapper extends BaseMapper<MiniAccountPackageGoods> {

    IPage<MiniAccountPackageGoodsVo> getPageList(Page<MiniAccountPackageGoodsVo> page,
                                                 @Param("paramMap") MiniAccountPackageGoodsParam miniAccountPackageGoodsParam);

    /**
     * 根据用户权益包订单id获取权益包商品信息
     * @param page
     * @param param
     * @return
     */
    IPage<MiniAccountPackageGoodsVo> getMiniAccountPackageGoods(Page<MiniAccountPackageGoodsVo> page,
                                                                @Param("paramMap")MiniAccountPackageGoodsParam param);

    /**
     * 根据权益包订单id获取权益包商品
     * @param mainId
     * @return
     */
    List<ApiPackageGoodsVo> getApiPackageGoods(@Param("mainId")String mainId, @Param("userId")String userId);

    /**
     * 根据订单id，权益包id获取权益包商品核销记录
     * @param orderId
     * @param packageId
     * @return
     */
    List<PackageGoodsShowVo> getPackageGoodsShowVo(@Param("orderId")String orderId,
                                                   @Param("packageId")String packageId);

    /**
     * 获取用户受赠的权益包商品
     * @return
     */
    List<ApiPackageGoodsVo> getGiftPackageGoods(@Param("userId")String userId);
}
