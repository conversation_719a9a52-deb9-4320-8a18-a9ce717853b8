package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountPackageOrder;
import com.medusa.gruul.account.api.model.param.MiniAccountPackageOrderParam;
import com.medusa.gruul.account.api.model.vo.MiniAccountCouponSearchVo;
import com.medusa.gruul.account.model.param.ApiMiniAccountPackageOrderParam;
import com.medusa.gruul.account.model.vo.ApiMiniAccountPackageOrderVo;
import com.medusa.gruul.account.model.vo.MiniAccountPackageOrderVo;
import com.medusa.gruul.afs.api.model.UpdatePackageOrderStatusMessage;
import com.medusa.gruul.order.api.model.OrderVo;

/**
 * @Author: plh
 * @Description: 会员权益包购买记录Service
 * @Date: Created in 15:13 2024/9/5
 */
public interface IMiniAccountPackageOrderService extends IService<MiniAccountPackageOrder> {
    /**
     * 添加队列发送权益包订单消息
     * @param orderVo
     */
    void addMiniAccountPackageOrderByMq(OrderVo orderVo);

    /**
     * 分页获取用户权益包订单
     * @param miniAccountPackageOrderParam
     * @return
     */
    IPage<MiniAccountPackageOrderVo> getPageList(MiniAccountPackageOrderParam miniAccountPackageOrderParam);

    /**
     * 分页获取小程序用户权益包管理信息
     * @param param
     * @return
     */
    IPage<ApiMiniAccountPackageOrderVo>getApiMiniAccountPackageOrder(ApiMiniAccountPackageOrderParam param);

    /**
     * 更新小程序用户订单状态
     */
    void updateMiniAccountPackageOrderStatus();

    /**
     * 判断权益包商品订单是否允许退款
     * @param orderId
     * @return
     */
    Boolean vailPackageOrder(Long orderId);

    /**
     * 修改权益包订单状态
     * @param message
     */
    void updatePackageOrderStatus(UpdatePackageOrderStatusMessage message);

    /**
     * 导出用户权益包订单
     * @param miniAccountPackageOrderParam
     */
    void exportPackageOrder(MiniAccountPackageOrderParam miniAccountPackageOrderParam);


    /**
     * 删除订单关联套包信息
     * @param orderId
     */
    void deleteByOrderId(Long orderId);
}
