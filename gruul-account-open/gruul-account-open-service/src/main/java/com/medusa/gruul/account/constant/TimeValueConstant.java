package com.medusa.gruul.account.constant;

/**
 * <AUTHOR>
 * @description
 * @data: 2019/11/27
 */
public class TimeValueConstant {

    /**
     * 1 分钟
     */
    public static final Long TEMPTIME = 1 * 60 * 1000L;

    /**
     * 5 分钟
     */
    public static final Long FIVEMINUTES = 5 * 60 * 1000L;

    /**
     * 10 分钟
     */
    public static final Long TENMINUTES = 10 * 60 * 1000L;


    /**
     * 30分钟
     */
    public static final Long HALFHOUR = 30 * 60 * 1000L;

    /**
     * 24小时  (1天)
     */
    public static final Long ONEDAY = 1 * 24 * 60 * 60 * 1000L;

    /**
     * 48小时  (2天)
     */
    public static final Long NEXTDAY = 2 * 24 * 60 * 60 * 1000L;

    /**
     * 5天
     */
    public static final Long FIVEDAY = 5 * 24 * 60 * 60 * 1000L;

    /**
     * 7天
     */
    public static final Long SEVENDAY = 7 * 24 * 60 * 60 * 1000L;


    /**
     * 1小时
     */
    public static final Long ONEHOUR = 60 * 60 * 1000L;

    /**
     * 1５天
     */
    public static final Long NINETEENDAYS = 15 * 24 * 60 * 60 * 1000L;
    /**
     * 20天
     */
    public static final Long TWENTYDAY = 20 * 24 * 60 * 60 * 1000L;
}
