package com.medusa.gruul.account.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.medusa.gruul.account.api.entity.MiniAccountGolden;
import com.medusa.gruul.account.api.entity.MiniAccountGoldenChange;
import com.medusa.gruul.account.api.enums.CommissionTypeEnum;
import com.medusa.gruul.account.api.enums.GoldenChangeTypeEnum;
import com.medusa.gruul.account.mapper.MiniAccountGoldenChangeMapper;
import com.medusa.gruul.account.model.dto.MiniAccountGoldenChangeAuditDto;
import com.medusa.gruul.account.model.dto.MiniAccountGoldenChangeDeleteDto;
import com.medusa.gruul.account.model.dto.MiniAccountGoldenChangeDto;
import com.medusa.gruul.account.model.dto.MiniAccountGoldenDto;
import com.medusa.gruul.account.model.param.MiniAccountGoldenChangeParam;
import com.medusa.gruul.account.model.vo.MiniAccountGoldenChangeVo;
import com.medusa.gruul.account.service.IMiniAccountExtendsService;
import com.medusa.gruul.account.service.IMiniAccountGoldenChangeService;
import com.medusa.gruul.account.service.IMiniAccountGoldenService;
import com.medusa.gruul.account.service.IMiniAccountService;
import com.medusa.gruul.common.core.constant.CommonConstants;
import com.medusa.gruul.common.core.constant.enums.ApproveStatusEnum;
import com.medusa.gruul.common.core.exception.ServiceException;
import com.medusa.gruul.common.core.util.CurUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:38 2025/7/21
 */
@Service
public class MiniAccountGoldenChangeServiceImpl extends ServiceImpl<MiniAccountGoldenChangeMapper, MiniAccountGoldenChange> implements IMiniAccountGoldenChangeService {

    @Autowired
    private IMiniAccountGoldenService miniAccountGoldenService;




    @Override
    @Transactional
    public void addMiniAccountGoldenChange(MiniAccountGoldenChangeDto dto) {

        MiniAccountGoldenChange miniAccountGoldenChange = new MiniAccountGoldenChange();
        BeanUtils.copyProperties(dto,miniAccountGoldenChange);
        miniAccountGoldenChange.setAuditStatus(ApproveStatusEnum.AUDIT.getStatus());

        this.save(miniAccountGoldenChange);

    }

    @Override
    @Transactional
    public void editMiniAccountGoldenChange(MiniAccountGoldenChangeDto dto) {

        Long id = dto.getId();
        if(id == null){
            throw new ServiceException("金豆变更记录id不能为空！");
        }
        MiniAccountGoldenChange miniAccountGoldenChange = this.getById(id);
        if(miniAccountGoldenChange == null){
            throw new ServiceException("金豆变更记录不存在！");
        }
        Integer auditStatus = miniAccountGoldenChange.getAuditStatus();
        if(auditStatus != ApproveStatusEnum.REJECT.getStatus()){
            throw new ServiceException("只能修改审核不通过记录！");
        }

        BeanUtils.copyProperties(dto,miniAccountGoldenChange);
        miniAccountGoldenChange.setAuditStatus(ApproveStatusEnum.AUDIT.getStatus());
        this.updateById(miniAccountGoldenChange);
    }

    @Override
    @Transactional
    public void deleteMiniAccountGoldenChange(MiniAccountGoldenChangeDeleteDto dto) {
        Long id = dto.getId();
        if(id == null){
            throw new ServiceException("金豆变更记录id不能为空！");
        }
        MiniAccountGoldenChange miniAccountGoldenChange = this.getById(id);
        if(miniAccountGoldenChange == null){
            throw new ServiceException("金豆变更记录不存在！");
        }
        Integer auditStatus = miniAccountGoldenChange.getAuditStatus();
        if(auditStatus != ApproveStatusEnum.REJECT.getStatus()){
            throw new ServiceException("只能删除审核不通过记录！");
        }
        this.removeById(dto.getId());

    }

    @Override
    @Transactional
    public void auditMiniAccountGoldenChange(MiniAccountGoldenChangeAuditDto dto) {
        String userId = CurUserUtil.getHttpCurUser().getUserId();
        String nikeName = CurUserUtil.getHttpCurUser().getNikeName();

        Long id = dto.getId();
        MiniAccountGoldenChange miniAccountGoldenChange = this.getById(id);
        if(miniAccountGoldenChange == null){
            throw new ServiceException("金豆变更记录不存在！");
        }
        Integer auditStatus = miniAccountGoldenChange.getAuditStatus();
        if(auditStatus != ApproveStatusEnum.AUDIT.getStatus()){
            throw new ServiceException("只能审核待审核记录！");
        }
        if(dto.getAuditStatus() == ApproveStatusEnum.REJECT.getStatus()
                &&StringUtils.isEmpty(dto.getAuditReason())){
            throw new ServiceException("审核不通过时需要填写原因！");
        }
        BeanUtils.copyProperties(dto,miniAccountGoldenChange);
        miniAccountGoldenChange.setAuditTime(LocalDateTime.now());
        miniAccountGoldenChange.setAuditUserId(userId);
        miniAccountGoldenChange.setAuditUserName(nikeName);

        if(dto.getAuditStatus() == ApproveStatusEnum.APPROVED.getStatus()){
            //审核通过
            MiniAccountGoldenDto miniAccountGoldenDto = new MiniAccountGoldenDto();
            miniAccountGoldenDto.setUserId(miniAccountGoldenChange.getUserId());
            miniAccountGoldenDto.setCommissionType(CommissionTypeEnum.CHANGE.getType());
            miniAccountGoldenDto.setSource(CommonConstants.NUMBER_ONE);
            miniAccountGoldenDto.setOrderType(CommonConstants.NUMBER_THREE);
            if(miniAccountGoldenChange.getType() == GoldenChangeTypeEnum.ADD.getStatus()){
                miniAccountGoldenDto.setAmount(miniAccountGoldenChange.getAmount());
                miniAccountGoldenDto.setRemark("手动增加金豆");
            }else{
                miniAccountGoldenDto.setAmount(miniAccountGoldenChange.getAmount().negate());
                miniAccountGoldenDto.setRemark("手动减少金豆");
            }
            miniAccountGoldenService.addMiniAccountGolden(miniAccountGoldenDto);
        }

        this.updateById(miniAccountGoldenChange);


    }

    @Override
    public IPage<MiniAccountGoldenChangeVo> queryList(MiniAccountGoldenChangeParam param) {
        IPage<MiniAccountGoldenChangeVo> page = this.baseMapper.queryList(new Page<>(param.getCurrent(),param.getSize()),param);
        return page;
    }

    @Override
    public MiniAccountGoldenChangeVo getMiniAccountGoldenChangeVo(MiniAccountGoldenChangeParam param) {
        return this.baseMapper.getMiniAccountGoldenChange(param);
    }


}
