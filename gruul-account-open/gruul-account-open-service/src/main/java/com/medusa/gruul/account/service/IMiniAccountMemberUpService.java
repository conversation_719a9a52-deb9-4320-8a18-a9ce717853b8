package com.medusa.gruul.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.account.api.entity.MiniAccountMemberUp;
import com.medusa.gruul.account.model.param.MiniAccountMemberUpParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 8:47 2025/6/19
 */
public interface IMiniAccountMemberUpService extends IService<MiniAccountMemberUp> {

    /**
     * 根据小程序用户id，会员类型id条件查询会员升级消费额记录
     * @param param
     * @return
     */
    MiniAccountMemberUp getMiniAccountMemberUp(MiniAccountMemberUpParam param);

    /**
     * 获取团队升级消费额
     * @param shopUserIds
     * @return
     */
    BigDecimal getSumAmount(List<String> shopUserIds);
}
