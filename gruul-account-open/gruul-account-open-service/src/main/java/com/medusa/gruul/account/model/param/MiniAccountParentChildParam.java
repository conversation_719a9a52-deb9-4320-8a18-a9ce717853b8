package com.medusa.gruul.account.model.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:31 2024/5/13
 */
@Data
public class MiniAccountParentChildParam {

    @ApiModelProperty(value = "用户id")
    private String userId;
    @ApiModelProperty(value = "上级用户id")
    private String parentUserId;
    @ApiModelProperty(value = "下级用户ids")
    private List<String> childUserIds;

}
