package com.medusa.gruul.shops.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.medusa.gruul.shops.api.entity.ShopCouponPartner;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 16:30 2024/8/23
 */
@Repository
public interface ShopCouponPartnerMapper extends BaseMapper<ShopCouponPartner> {
    /**
     * 获取优惠券指定商家
     * @param couponId
     * @return
     */
    List<String> getShopIds(@Param("couponId")Long couponId);
}
