package com.medusa.gruul.shops.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.common.core.util.PageUtils;
import com.medusa.gruul.shops.api.entity.ShopFullDonation;
import com.medusa.gruul.shops.model.dto.ShopFullDonationAuditDto;
import com.medusa.gruul.shops.model.dto.ShopFullDonationDto;
import com.medusa.gruul.shops.api.model.ShopFullDonationParam;
import com.medusa.gruul.shops.api.model.ShopFullDonationVo;

import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 14:31 2025/7/4
 */
public interface IShopFullDonationService extends IService<ShopFullDonation> {

    /**
     * 添加满减满赠活动
     * @param dto
     */
    void add(ShopFullDonationDto dto);

    /**
     * 编辑满减满赠活动
     * @param dto
     */
    void edit(ShopFullDonationDto dto);

    /**
     * 根据id删除满赠满减活动
     * @param id
     */
    void deleteById(Long id);

    /**
     * 根据id审核满赠满减活动
     * @param dto
     */
    void audit(ShopFullDonationAuditDto dto);

    /**
     * 根据id停用满赠满减活动
     * @param dto
     */
    void stop(ShopFullDonationAuditDto dto);

    /**
     * 更新满赠满减活动
     */
    void autoUpdateStatusByTime();

    /**
     * 分页查询满减满赠活动
     * @param param
     * @return
     */
    PageUtils<ShopFullDonationVo> getShopFullDonation(ShopFullDonationParam param);

    /**
     * 根据id获取满减满赠活动
     * @param dto
     * @return
     */
    ShopFullDonationVo getShopFullDonationById(ShopFullDonationAuditDto dto);

    /**
     * 获取商品是否符合满减满赠活动，返回符合的商品标题
     * @param productId
     * @param shopId
     * @return
     */
    List<String> getShowTitleList(Long productId, String shopId,Integer buyType);


    /**
     * 查询有效的满减满赠活动
     * @param param
     * @return
     */
    List<ShopFullDonationVo> getValidShopFullDonation(ShopFullDonationParam param);
}
