<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.medusa</groupId>
        <artifactId>gruul-afs-open</artifactId>
        <version>0.1</version>
    </parent>

    <artifactId>gruul-afs-open-service</artifactId>
    <packaging>jar</packaging>

    <description>gruul afs</description>

    <dependencies>
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-common-auth-open</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <!--账户api模块-->
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-account-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <!--平台API模块-->
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-platform-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <!-- api模块-->
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-logistics-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <!--api模块-->
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-order-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <!--商品api模块-->
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-goods-open-api</artifactId>
            <version>${gruul.version}</version>
        </dependency>

        <!-- swagger-ui -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger.fox.version}</version>
        </dependency>
        <!--注册中心客户端-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mp.version}</version>
        </dependency>

        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <!--数据库-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>


        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${spring-boot.version}</version>
            <exclusions>
                <!--排除tomcat依赖-->
                <exclusion>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <!--RocketMQ-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>


</project>