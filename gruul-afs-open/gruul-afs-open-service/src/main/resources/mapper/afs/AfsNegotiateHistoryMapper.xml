<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.medusa.gruul.afs.mapper.AfsNegotiateHistoryMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.medusa.gruul.afs.api.entity.AfsNegotiateHistory">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="deleted"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="user_avatar_url" property="userAvatarUrl"/>
        <result column="info" property="info"/>
        <result column="image" property="image"/>
        <result column="order_id" property="orderId"/>
        <result column="apply_user_type" property="applyUserType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,update_time,is_deleted,id, user_id, user_name, user_avatar_url, info, image, order_id, apply_user_type
    </sql>
    <select id="negotiateHistoryList" resultType="com.medusa.gruul.afs.api.entity.AfsNegotiateHistory">
        SELECT * FROM t_afs_negotiate_history WHERE order_id = #{orderId}
        <if test="type!=null">
            AND apply_user_type = #{type}
        </if>
        <if test="deliveryOrderId!=null">
            AND delivery_order_id = #{deliveryOrderId}
        </if>
        <if test="deliveryOrderId==null">
            AND ifnull(delivery_order_id,'') = ''
        </if>
        ORDER BY create_time DESC
    </select>
</mapper>
