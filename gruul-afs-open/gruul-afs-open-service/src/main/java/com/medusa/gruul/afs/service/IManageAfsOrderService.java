package com.medusa.gruul.afs.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.medusa.gruul.afs.api.entity.AfsOrder;
import com.medusa.gruul.afs.model.*;
import com.medusa.gruul.common.core.util.PageUtils;

import java.util.List;

/**
 * <p>
 * 售后工单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-05
 */
public interface IManageAfsOrderService extends IService<AfsOrder> {

    /**
     * Seller refuse.
     *
     * @param dto the dto
     */
    void sellerRefuse(SellerRefusalDto dto);

    /**
     * Seller approve.
     *
     * @param afsId    the afs id
     * @param isSystem the is system
     */
    void sellerApprove(Long afsId, Boolean isSystem);

    /**
     * Note.
     *
     * @param dto the dto
     */
    void note(SellerNoteDto dto);

    /**
     * User apply refund.
     *
     * @param afsOrder the afs order
     * @param isSystem the is system
     */
    void userApplyRefund(AfsOrder afsOrder, boolean isSystem);

    /**
     * User apply return.
     *
     * @param afsOrder   the afs order
     * @param isSystem   the is system
     * @param needReturn the need return
     */
    void userApplyReturn(AfsOrder afsOrder, Boolean isSystem, boolean needReturn);

    /**
     * Search manage afs order vo page page utils.
     *
     * @param dto the dto
     * @return the page utils
     */
    PageUtils<ManageAfsOrderVo> searchManageAfsOrderVoPage(SearchDto dto);


    /**
     * 商家批量修改售后订单收款发送状态
     * @param ids
     * @param receiveSyncStatus
     */
    void updateAfsReceiveSyncStatus(List<Long> ids, String receiveSyncStatus);

    /**
     * 商家批量修改售后订单退货单发送状态
     * @param ids
     * @param receiveSyncReturnStatus
     */
    void updateAfsReceiveSyncReturnStatus(List<Long> ids, String receiveSyncReturnStatus);
    /**
     * 分页查询外部系统查询售后收款单
     * @param param
     * @return
     */
    PageUtils<OutAfsReceiveOrderVo>searchOutAfsReceiveOrder(OutAfsReceiveOrderParam param);

    /**
     * 分页查询外部系统查询售后退货单
     * @param param
     * @return
     */
    PageUtils<OutAfsRefundOrderVo>searchOutAfsRefundOrder(OutAfsRefundOrderParam param);

    /**
     * 导出售后工单列表
     * @param dto
     */
    void exportManageAfsOrder(SearchDto dto);
}
