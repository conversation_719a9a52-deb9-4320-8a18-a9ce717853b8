package com.medusa.gruul.afs.mp;


import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.medusa.gruul.account.api.enums.AccountQueueEnum;
import com.medusa.gruul.afs.api.constant.AfsConstant;
import com.medusa.gruul.afs.api.constant.AfsQueueEnum;
import com.medusa.gruul.afs.api.entity.AfsOrderItem;
import com.medusa.gruul.afs.api.enums.AfsOrderStatusEnum;
import com.medusa.gruul.afs.api.model.UpdatePackageOrderStatusMessage;
import com.medusa.gruul.afs.model.AfsOrderVo;
import com.medusa.gruul.afs.api.model.AfsRemoveDeliverOrderMessage;
import com.medusa.gruul.afs.mp.model.BaseAfsOrderMessage;
import com.medusa.gruul.order.api.constant.OrderQueueEnum;
import com.medusa.gruul.order.api.model.BatchRevertStockMessage;
import com.medusa.gruul.order.api.model.CloseExchangeOrderMessage;
import com.medusa.gruul.order.api.model.CloseOrderMessage;
import com.medusa.gruul.platform.api.constant.ExchangeConstant;
import com.medusa.gruul.platform.api.constant.QueueNameConstant;
import com.medusa.gruul.platform.api.model.dto.SubscribeMsgSendDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.LinkedList;

/**
 * <AUTHOR>
 * @Description: Sender.java
 * @date 2019/10/6 13:48
 */
@Slf4j
@Component
public class Sender {

    @Autowired
    private RabbitTemplate rabbitTemplate;


    /**
     * 商家超时未审批自动通过申请
     *
     * @param message
     * @param expiration
     * @return void
     * <AUTHOR>
     * @date 2019/12/9 20:17
     */
    public void sendMerchantAutoConfirmMessage(BaseAfsOrderMessage message, long expiration) {
        log.info("sendMerchantAutoConfirmMessage:" + message);
        log.info("send time:" + LocalDateTime.now());
        convertAndSend(AfsQueueEnum.QUEUE_AFS_MERCHANT_AUTO_CONFIRM, message,
                new ExpirationMessagePostProcessor(expiration));
    }
    /**
     * 发送申请退款成功队列
     * @param message
     */
    public void sendCloseOrderReturnMessage(CloseOrderMessage message){
        log.info("sendCloseOrderReturnMessage message:" + message.toString());
        convertAndSend(OrderQueueEnum.QUEUE_ORDER_AGREE_RETURN,message);
    }

    /**
     * 订单移出发货单
     *
     * @param message
     * @return void
     * <AUTHOR>
     * @date 2019/12/9 20:18
     */
    public void sendRemoveSendBillOrderMessage(AfsRemoveDeliverOrderMessage message) {
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        log.info("sendRemoveSendBillOrderMessage:" + message.toString());
        rabbitTemplate.convertAndSend("gruul.afs.deliver.remove", message, correlationData);
    }

    public void sendUpdatePackageOrderStatusMessage(UpdatePackageOrderStatusMessage message) {
        log.info("sendUpdatePackageOrderStatusMessage:" + message.toString());
        convertAndSend(AccountQueueEnum.QUEUE_ACCOUNT_PACKAGE_STATUS_UPDATE,message);
    }
    private void convertAndSend(AccountQueueEnum queue,Object message){
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        rabbitTemplate.convertAndSend(queue.getExchange(), queue.getRouteKey(), message, correlationData);
    }

    private void convertAndSend(AfsQueueEnum queue, Object message, MessagePostProcessor messagePostProcessor) {
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        rabbitTemplate.convertAndSend(queue.getExchange(), queue.getRouteKey(), message, messagePostProcessor,
                correlationData);
    }

    private void convertAndSend(OrderQueueEnum queue,Object message){
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
        rabbitTemplate.convertAndSend(queue.getExchange(), queue.getRouteKey(), message, correlationData);
    }

    /**
     * 发送退货消息通知
     *
     * @return void
     * <AUTHOR>
     * @date 2019/12/9 20:17
     */
    public void sendWechatReturnMessage(AfsOrderVo afsOrder, String openId) {
        log.info("sendWechatReturnMessage start:" + JSONUtil.toJsonStr(afsOrder));

        SubscribeMsgSendDto dto = new SubscribeMsgSendDto();
        dto.setTemplateId(afsOrder.getReturnTemplateId());
        dto.setOpenId(openId);
        dto.setToPath(StrUtil.format("/pages/afterSaleDetail/afterSaleDetail?afsid={}", afsOrder.getId()));
        LinkedList<String> s = new LinkedList<>();
        s.add(afsOrder.getReceiptBillId().toString());

        String productName = "";
        for (AfsOrderItem afsOrderItem : afsOrder.getItem()) {
            if(productName.length()>0){
                productName += "，";
            }
            productName += afsOrderItem.getProductName();
        }

        if(productName.length() >= AfsConstant.NUMBER_TWENTY){
            s.add(productName.indexOf(0,16)+"...");
        }else{
            s.add(productName);
        }
        s.add("￥" + afsOrder.getRefundAmount().toString());
        s.add(0 + "积分");
        s.add("商家已同意退款申请，请及时退货！");
        dto.setSendDatas(s);
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());

        rabbitTemplate.convertAndSend(ExchangeConstant.PLATFORM_EXCHANGE,
                QueueNameConstant.PLATFORM_SUBSCRIBE_MSG_SEND, dto, correlationData);
        log.info("sendWechatReturnMessage end:" + afsOrder.getId());
    }

    /**
     * 发送退款消息通知
     *
     * @return void
     * <AUTHOR>
     * @date 2019/12/9 20:17
     */
    public void sendWechatRefundMessage(AfsOrderVo afsOrder, String openId) {
        log.info("sendRefundMessage start:" + afsOrder.toString());
        SubscribeMsgSendDto dto = new SubscribeMsgSendDto();
        dto.setTemplateId(afsOrder.getTemplateId());
        dto.setOpenId(openId);
        dto.setToPath(StrUtil.format("/pages/afterSaleDetail/afterSaleDetail?afsid={}", afsOrder.getId()));
        LinkedList<String> s = new LinkedList<>();
        s.add(afsOrder.getReceiptBillId().toString());

        String productName = "";
        for (AfsOrderItem afsOrderItem : afsOrder.getItem()) {
            if(productName.length()>0){
                productName += "，";
            }
            productName += afsOrderItem.getProductName();
        }

        if(productName.length() >= AfsConstant.NUMBER_TWENTY){
            s.add(productName.indexOf(0,16)+"...");
        }else{
            s.add(productName);
        }

        if (afsOrder.getStatus().equals(AfsOrderStatusEnum.SUCCESS)) {
            s.add("退款成功");
        } else {
            s.add("退款失败");
        }
        s.add("￥" + afsOrder.getRefundAmount().toString());
        s.add(0+ "积分");
        dto.setSendDatas(s);
        CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());

        rabbitTemplate.convertAndSend(ExchangeConstant.PLATFORM_EXCHANGE,
                QueueNameConstant.PLATFORM_SUBSCRIBE_MSG_SEND, dto, correlationData);
        log.info("sendRefundMessage end:" + afsOrder.getId());
    }

    /**
     * 关闭换货订单
     * @param message
     */
    public void sendCloseExchangeOrderReturnMessage(CloseExchangeOrderMessage message){
        log.info("sendCloseExchangeOrderReturnMessage message:" + message.toString());
        convertAndSend(OrderQueueEnum.QUEUE_ORDER_EXCHANGE_RETURN,message);
    }

    /**
     * 发送归还库存消息
     * @param message
     */
    public void sendBatchRevertStockMessage(BatchRevertStockMessage message) {
        log.info("sendBatchRevertStockMessage:" + message.toString());
        convertAndSend(OrderQueueEnum.QUEUE_BATCH_REVERT_STOCK, message);
    }
}