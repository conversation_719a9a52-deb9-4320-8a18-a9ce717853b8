package com.medusa.gruul.afs.controller.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 9:07 2024/10/15
 */
@Getter
public enum ExternalOrderEnum {

    /**
     * 未发送  NOT_ISSUED
     */
    NOT_ISSUED(0,"未发送"),
    /**
     * 已发送 ISSUED
     */
    ISSUED(1,"已发送");



    @EnumValue
    /**
     * 值
     */
    private final int status;

    /**
     * 描述
     */
    private final String desc;


    ExternalOrderEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

}
