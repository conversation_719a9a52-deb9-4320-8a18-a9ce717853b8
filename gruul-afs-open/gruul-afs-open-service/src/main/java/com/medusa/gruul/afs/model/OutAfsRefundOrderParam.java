package com.medusa.gruul.afs.model;

import com.medusa.gruul.common.core.param.QueryParam;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 18:52 2024/10/15
 */
@ApiModel("生成外部系统售后退货单Param")
@Data
public class OutAfsRefundOrderParam extends QueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

}
