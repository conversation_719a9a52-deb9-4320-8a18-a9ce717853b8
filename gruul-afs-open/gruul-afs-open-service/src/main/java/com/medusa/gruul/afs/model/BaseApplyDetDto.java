package com.medusa.gruul.afs.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 10:55 2025/4/7
 */
@Data
@ApiModel(value = "用户申请售后详情的参数")
public class BaseApplyDetDto {

    /**
     * 商品sku编号
     */
    @NotNull(message = "商品sku不能为空")
    @ApiModelProperty(value = "商品sku编号")
    private Long productSkuId;


    /**
     * 商品数量
     */
    @Min(1)
    @NotNull(message = "商品数量不能为空")
    @ApiModelProperty(value = "商品数量")
    private Integer productQuantity;


    @ApiModelProperty(value = "价格类型")
    private Integer priceType;

    @ApiModelProperty(value = "会员类型id")
    private Long memberTypeId;

}
