package com.medusa.gruul.afs.model;

import com.medusa.gruul.afs.api.enums.AfsOrderStatusEnum;
import com.medusa.gruul.order.api.enums.DeliverTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 评价管理导出Excel VO类
 */
@Data
@ApiModel(value = "售后工单导出Excel VO类")
public class ManageAfsOrderExcelVo {

    @ApiModelProperty(value = "序号")
    private Integer index;

    @ApiModelProperty(value = "订单号")
    private String no;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "金额")
    private BigDecimal productPrice;

    @ApiModelProperty(value = "数量")
    private Integer productQuantity;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 收货人姓名
     */
    @ApiModelProperty(value = "收货人姓名")
    private String receiverName;

    /**
     * 收货人电话
     */
    @ApiModelProperty(value = "收货人电话")
    private String receiverPhone;
    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String shopName;




    @ApiModelProperty(value = "申请时间")
    private String createTime;

    @ApiModelProperty(value = "配送方式")
    private String deliveryType;

    @ApiModelProperty(value = "收货地址")
    private String receiverAddress;

    /**
     * 订单退款金额
     */
    @ApiModelProperty(value = "订单退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "售后工单类型")
    private String type;

    @ApiModelProperty(value = "售后工单状态")
    private String status;


    @ApiModelProperty(value = "备注")
    private String remark;



}
