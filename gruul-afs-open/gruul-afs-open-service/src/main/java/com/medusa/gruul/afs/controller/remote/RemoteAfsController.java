package com.medusa.gruul.afs.controller.remote;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.medusa.gruul.afs.api.entity.AfsOrder;
import com.medusa.gruul.afs.api.entity.AfsOrderItem;
import com.medusa.gruul.afs.api.model.AfsSimpleVo;
import com.medusa.gruul.afs.service.IAfsOrderItemService;
import com.medusa.gruul.afs.service.IAfsOrderService;
import com.medusa.gruul.common.core.annotation.EscapeLogin;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * The type Remote afs controller.
 *
 * <AUTHOR>
 * @description: RemoteAfsController.java
 * @date 2020 /10/5 09:59
 */
@RestController
@RequestMapping("/remote")
@Api(tags = "Feign接口")
public class RemoteAfsController {
    @Resource
    private IAfsOrderService afsOrderService;

    @Resource
    private IAfsOrderItemService afsOrderItemService;

    /**
     * 售后单详情,该方法使用Feign默认租户信息，不包含默认查询全部
     *
     * @param receiptBillId the receipt bill id
     * @return the afs order by receipt bill id
     */
    @EscapeLogin
    @ApiOperation("售后单详情")
    @GetMapping("/{receiptBillId}")
    public List<AfsSimpleVo> getAfsOrderByReceiptBillId(@PathVariable(value = "receiptBillId") @NotNull Long receiptBillId) {
        return afsOrderService.getAfsOrderByReceiptBillId(receiptBillId);
    }



    /**
     * 获取用户订单数量
     * @param userId
     * @return
     */
    @GetMapping("/remote/afsOrderNumber")
    Long getAfsOrderNumber(@RequestParam(value = "userId") String userId) {
        List<AfsOrder> afsOrders = afsOrderService.getBaseMapper().selectList(new LambdaQueryWrapper<AfsOrder>()
                .eq(AfsOrder::getUserId, userId));
        long size = afsOrders.size();
        return size;
    }

    /**
     * 根据商品id判断是否有售后订单
     * @param ids
     * @return
     */
    @EscapeLogin
    @ApiOperation("售后单详情")
    @GetMapping("/getIsAfsOrder")
    Boolean getIsAfsOrder(@RequestParam("ids") Long[] ids){
        QueryWrapper<AfsOrderItem> afsOrderItemQueryWrapper = new QueryWrapper<>();
        afsOrderItemQueryWrapper.lambda().in(AfsOrderItem::getProductId,ids);
        int count = afsOrderItemService.count(afsOrderItemQueryWrapper);
        return count>0;
    }

    /**
     * 根据订单ID和售后ID查询还在进行中的订单
     * @param orderId
     * @param afsId
     * @return
     */
    @EscapeLogin
    @ApiOperation("根据订单ID和售后ID查询还在进行中的订单")
    @GetMapping("/selectProgressByOrderIdAndIdNotIn")
    List<AfsOrder> selectProgressByOrderIdAndIdNotIn(@RequestParam(value = "orderId") Long orderId,
                                                     @RequestParam(value = "afsId") Long afsId){
        List<AfsOrder> afsOrderList = afsOrderService.selectProgressByOrderIdAndIdNotIn(orderId,afsId);
        return afsOrderList;
    }

    @GetMapping("/getAfterQty")
    @ApiOperation("根据商品id，商品规格id，用户id获取客户购买商品售后数量")
    @EscapeLogin
    Integer getAfterQty(@RequestParam(value = "productId")Long productId,
                        @RequestParam(value = "productSkuId")Long productSkuId,
                        @RequestParam(value = "userId")String userId){
        Integer afterQty = afsOrderService.getAfterQty(productId,productSkuId,userId);
        return afterQty;
    }
    @GetMapping("/getAfterQtyByOrder")
    @ApiOperation("根据商品id，商品规格id，订单id获取客户购买商品售后数量")
    @EscapeLogin
    Integer getAfterQtyByOrder(@RequestParam(value = "productId")Long productId,
                               @RequestParam(value = "productSkuId")Long productSkuId,
                               @RequestParam(value = "orderId")Long orderId){
        Integer afterQty = afsOrderService.getAfterQtyByOrder(productId,productSkuId,orderId);
        return afterQty;
    }

}
