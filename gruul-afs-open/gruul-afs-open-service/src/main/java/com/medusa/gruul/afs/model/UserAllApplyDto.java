package com.medusa.gruul.afs.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:56 2025/6/26
 */
@Data
@ApiModel(value = "用户申请整单售后的参数")
public class UserAllApplyDto {

    /**
     * 申请售后的订单ID
     */
    @NotNull(message = "申请售后的订单不能为空")
    @ApiModelProperty(value = "申请售后的订单ID")
    private Long orderId;

    /**
     * 说明
     */
    @NotBlank
    @Length(min = 1, max = 200)
    @ApiModelProperty(value = "说明")
    private String description;

    /**
     * 照片
     */
    @ApiModelProperty(value = "照片")
    private String images;



}
