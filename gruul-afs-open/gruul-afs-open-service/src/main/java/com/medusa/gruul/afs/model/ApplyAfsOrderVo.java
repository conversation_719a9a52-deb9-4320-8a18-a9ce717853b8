package com.medusa.gruul.afs.model;

import com.medusa.gruul.afs.api.enums.AfsOrderTypeEnum;
import com.medusa.gruul.order.api.model.OrderItemVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 15:59 2025/6/12
 */
@Data
@ApiModel(value = "申请售后单信息Vo")
public class ApplyAfsOrderVo {

    @ApiModelProperty(value = "退款金额")
    private BigDecimal realAmount;

    @ApiModelProperty(value = "整单退标识")
    private Integer allAfsOrderFlag;

    @ApiModelProperty(value = "退款单详情")
    private List<OrderItemVo>orderItemList;


}
