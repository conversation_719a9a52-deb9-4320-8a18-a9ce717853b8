<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.medusa</groupId>
        <artifactId>gruul-afs-open</artifactId>
        <version>0.1</version>
    </parent>

    <artifactId>gruul-afs-open-api</artifactId>
    <packaging>jar</packaging>

    <description>afs api模块</description>


    <dependencies>
        <!--common 库-->
        <dependency>
            <groupId>com.medusa</groupId>
            <artifactId>gruul-common-auth-open</artifactId>
            <version>${gruul.version}</version>
        </dependency>
        <!--mybatis plus extension,包含了mybatis plus core-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>${mp.version}</version>
        </dependency>
    </dependencies>


</project>