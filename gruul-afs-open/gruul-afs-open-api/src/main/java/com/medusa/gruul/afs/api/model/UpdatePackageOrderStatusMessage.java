package com.medusa.gruul.afs.api.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: plh
 * @Description: TODO
 * @Date: Created in 17:12 2024/10/14
 */
@Data
public class UpdatePackageOrderStatusMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    private String orderId;

    /**
     * 状态:100->未用;101->已用;200->已失效
     */
    @ApiModelProperty(value = "状态:100->未用;101->已用;200->已失效;")
    private Integer status;

}
